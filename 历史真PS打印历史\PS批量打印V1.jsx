// 全局变量
// 存储原始文档排序的路径
var originalDocPaths = [];

// 保存原始文档排序
function saveOriginalDocOrder() {
    originalDocPaths = [];
    // 保存文档路径而不是文档对象
    for (var i = 0; i < app.documents.length; i++) {
        originalDocPaths.push(app.documents[i].fullName);
    }

    // 显示保存的原始文档顺序（调试信息）
    var orderInfo = "已保存原始文档顺序:\n";
    for (var i = 0; i < originalDocPaths.length; i++) {
        var docName = originalDocPaths[i].name || originalDocPaths[i].toString().split('/').pop();
        orderInfo += (i+1) + ". " + docName + "\n";
    }
    alert(orderInfo);
}

// 恢复原始文档排序
function restoreDocOrder() {
    if (originalDocPaths.length === 0) {
        alert("没有保存的原始文档排序");
        return false;
    }

    try {
        // 显示将要恢复的文档顺序（调试信息）
        var restoreInfo = "恢复原始文档顺序:\n";
        for (var i = 0; i < originalDocPaths.length; i++) {
            var docName = originalDocPaths[i].name || originalDocPaths[i].toString().split('/').pop();
            restoreInfo += (i+1) + ". " + docName + "\n";
        }
        alert(restoreInfo);

        // 关闭所有文档，不保存更改
        for (var i = app.documents.length - 1; i >= 0; i--) {
            app.documents[i].close(SaveOptions.DONOTSAVECHANGES);
        }

        // 按原始顺序重新打开文档
        for (var i = 0; i < originalDocPaths.length; i++) {
            app.open(originalDocPaths[i]);
        }

        // 恢复后清空原始文档排序，避免重复使用
        originalDocPaths = [];

        return true;
    } catch (e) {
        alert("恢复排序时出错: " + e);
        return false;
    }
}

// 按文件名重新排序文档
function reorderDocsByName() {
    if (app.documents.length <= 1) {
        alert("文档数量不足，无需排序");
        return false;
    }

    try {
        // 创建文档数组
        var docs = [];
        for (var i = 0; i < app.documents.length; i++) {
            docs.push(app.documents[i]);
        }

        // 输出排序前的文档名称，用于调试
        var beforeSort = "排序前的文档顺序:\n";
        for (var i = 0; i < docs.length; i++) {
            beforeSort += (i+1) + ". " + docs[i].name + "\n";
        }

        // 按文件名排序 - 使用自然排序算法，正确处理数字
        docs.sort(function(a, b) {
            var aName = a.name;
            var bName = b.name;

            // 从文件名中提取所有数字
            var aNumbers = aName.match(/\d+/g) || [];
            var bNumbers = bName.match(/\d+/g) || [];

            // 如果文件名中有数字，则按第一个数字排序
            if (aNumbers.length > 0 && bNumbers.length > 0) {
                var aNum = parseInt(aNumbers[0]);
                var bNum = parseInt(bNumbers[0]);
                if (aNum !== bNum) {
                    return aNum - bNum;
                }
            }

            // 如果第一个数字相同，则按完整文件名排序
            return aName.localeCompare(bName, undefined, {numeric: true, sensitivity: 'base'});
        });

        // 输出排序后的文档名称，用于调试
        var afterSort = "排序后的文档顺序:\n";
        for (var i = 0; i < docs.length; i++) {
            afterSort += (i+1) + ". " + docs[i].name + "\n";
        }

        // 显示排序前后的文档顺序（调试信息）
        alert(beforeSort + "\n" + afterSort);

        // 使用不同的方法排序文档
        // 先关闭所有文档，然后按排序后的顺序重新打开
        var docPaths = [];
        for (var i = 0; i < docs.length; i++) {
            // 保存文档路径
            docPaths.push(docs[i].fullName);
        }

        // 关闭所有文档，不保存更改
        for (var i = app.documents.length - 1; i >= 0; i--) {
            app.documents[i].close(SaveOptions.DONOTSAVECHANGES);
        }

        // 按排序后的顺序重新打开文档
        for (var i = 0; i < docPaths.length; i++) {
            app.open(docPaths[i]);
        }

        return true;
    } catch (e) {
        alert("重新排序时出错: " + e);
        return false;
    }
}

function main() {
    // Check if Photoshop is running
    if (app.documents.length === 0) {
        alert("请先打开至少一个文档！");
        return;
    }

    // 保存原始文档排序
    saveOriginalDocOrder();

    // Create dialog - 使用标准dialog类型的窗口
    var dlg = new Window("dialog", "批量打印V1.2");
    dlg.alignChildren = ["center", "top"];
    dlg.spacing = 10;
    dlg.margins = 16;

    // Main print button
    var mainPrintBtn = dlg.add("button", undefined, "【 批 量 打 印 】");
    mainPrintBtn.preferredSize.width = 150;

    // Order buttons group
    var orderGroup = dlg.add("group");
    orderGroup.orientation = "row";
    orderGroup.alignChildren = ["center", "center"];
    orderGroup.spacing = 10;

    var restoreOrderBtn = orderGroup.add("button", undefined, "恢复排序");
    restoreOrderBtn.preferredSize.width = 100;

    var reorderBtn = orderGroup.add("button", undefined, "重新排序");
    reorderBtn.preferredSize.width = 100;

    // Size inputs
    var sizeGroup1 = dlg.add("group");
    sizeGroup1.orientation = "row";
    sizeGroup1.alignChildren = ["left", "center"];
    sizeGroup1.spacing = 10;

    sizeGroup1.add("statictext", undefined, "宽度(毫米)：");
    var widthInput = sizeGroup1.add("edittext", undefined, "210");
    widthInput.preferredSize.width = 50;

    var sizeGroup2 = dlg.add("group");
    sizeGroup2.orientation = "row";
    sizeGroup2.alignChildren = ["left", "center"];
    sizeGroup2.spacing = 10;

    sizeGroup2.add("statictext", undefined, "高度(毫米)：");
    var heightInput = sizeGroup2.add("edittext", undefined, "297");
    heightInput.preferredSize.width = 50;

    // 添加分辨率输入框
    var sizeGroup3 = dlg.add("group");
    sizeGroup3.orientation = "row";
    sizeGroup3.alignChildren = ["left", "center"];
    sizeGroup3.spacing = 10;

    sizeGroup3.add("statictext", undefined, "分辨率(dpi)：");
    var resolutionInput = sizeGroup3.add("edittext", undefined, "300");
    resolutionInput.preferredSize.width = 50;

    // Proportional checkbox
    var proportionalCheck = dlg.add("checkbox", undefined, "按比例调整");
    proportionalCheck.value = true;

    // Resize button
    var resizeBtn = dlg.add("button", undefined, "调整图像大小");
    resizeBtn.preferredSize.width = 150;

    // Print and close buttons
    var actionGroup = dlg.add("group");
    actionGroup.orientation = "row";
    actionGroup.alignChildren = ["center", "center"];
    actionGroup.spacing = 10;

    var printBtn = actionGroup.add("button", undefined, "点此打印");
    printBtn.preferredSize.width = 100;

    var closeBtn = actionGroup.add("button", undefined, "关闭退出");
    closeBtn.preferredSize.width = 100;



    // Footer text
    var footerText1 = dlg.add("statictext", undefined, "恒心-perseverance");
    footerText1.alignment = "center";

    // Button functionality
    mainPrintBtn.onClick = function() {
        batchPrint();
    };

    restoreOrderBtn.onClick = function() {
        if (restoreDocOrder()) {
            alert("已恢复原始文档排序");
        }
    };

    reorderBtn.onClick = function() {
        if (reorderDocsByName()) {
            alert("已按文件名重新排序文档");
        }
    };

    resizeBtn.onClick = function() {
        resizeImages(parseFloat(widthInput.text), parseFloat(heightInput.text), parseFloat(resolutionInput.text), proportionalCheck.value);
    };

    printBtn.onClick = function() {
        printCurrentDocument();
    };

    closeBtn.onClick = function() {
        // 在关闭对话框时清空原始文档排序记忆
        originalDocPaths = [];
        dlg.close();
    };



    // 显示对话框
    dlg.center();
    return dlg.show();
}

// Function to resize images
function resizeImages(width, height, resolution, keepProportion) {
    if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
        alert("请输入有效的宽度和高度值！");
        return;
    }

    if (isNaN(resolution) || resolution <= 0) {
        alert("请输入有效的分辨率值！");
        return;
    }

    try {
        // 保存原始单位和对话框模式
        var originalRulerUnits = app.preferences.rulerUnits;
        var originalDialogMode = app.displayDialogs;

        // 设置为毫米单位并禁用对话框
        app.displayDialogs = DialogModes.NO;
        app.preferences.rulerUnits = Units.MM;

        // Process all open documents
        for (var i = 0; i < app.documents.length; i++) {
            var doc = app.documents[i];
            app.activeDocument = doc;

            if (keepProportion) {
                // 计算维持纵横比的新尺寸
                var ratio = doc.width.as('mm') / doc.height.as('mm');
                var newWidth, newHeight;

                if (ratio > width / height) {
                    // 宽度是限制因素
                    newWidth = width;
                    newHeight = newWidth / ratio;
                } else {
                    // 高度是限制因素
                    newHeight = height;
                    newWidth = newHeight * ratio;
                }

                // 直接使用毫米单位调整大小，并设置分辨率
                doc.resizeImage(UnitValue(newWidth, "mm"), UnitValue(newHeight, "mm"),
                               resolution, ResampleMethod.BICUBIC);
            } else {
                // 直接使用毫米单位调整到精确尺寸，并设置分辨率
                doc.resizeImage(UnitValue(width, "mm"), UnitValue(height, "mm"),
                               resolution, ResampleMethod.BICUBIC);
            }
        }

        // 恢复原始设置
        app.preferences.rulerUnits = originalRulerUnits;
        app.displayDialogs = originalDialogMode;

        alert("所有图像已调整大小！");
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalRulerUnits !== 'undefined') {
            app.preferences.rulerUnits = originalRulerUnits;
        }
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }
        alert("调整大小时出错: " + e);
    }
}

// 简单的日志函数
function logMessage(message) {
    // 只在控制台输出，不显示对话框
    $.writeln(message);
}

// 打印方法 - 只使用成功的方法
function printDocument(doc) {
    try {
        logMessage("打印文档: " + doc.name);
        doc.print();
        return true;
    } catch(err) {
        logMessage("打印失败: " + err);
        return false;
    }
}

// 只打印当前活动文档
function printCurrentDocument() {
    if (app.documents.length === 0) {
        alert("没有打开的文档！");
        return;
    }

    logMessage("打印当前文档: " + app.activeDocument.name);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 打印当前文档
        if (printDocument(app.activeDocument)) {
            alert("文档 " + app.activeDocument.name + " 已成功打印。");
        } else {
            alert("文档打印失败。请尝试手动打印。");
        }

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }
        alert("打印时出错: " + e);
    }
}

// Function to batch print all open documents
function batchPrint() {
    if (app.documents.length === 0) {
        alert("没有打开的文档！");
        return;
    }

    logMessage("开始批量打印操作, 文档数量: " + app.documents.length);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 处理所有打开的文档
        var successCount = 0;

        for (var i = 0; i < app.documents.length; i++) {
            var doc = app.documents[i];
            app.activeDocument = doc;

            // 使用成功的打印方法
            if (printDocument(doc)) {
                successCount++;
            }
        }

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;

        if (successCount > 0) {
            alert("批量打印完成！成功打印了 " + successCount + " 个文档。");
        } else {
            alert("没有文档被成功打印。请尝试手动打印。");
        }
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }
        alert("打印时出错: " + e);
    }
}

// 直接执行脚本
main();
