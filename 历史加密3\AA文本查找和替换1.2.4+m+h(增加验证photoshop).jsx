// 验证函数
function verifyUser() {
    // 检查是否在Photoshop中运行
    if (app.name !== "Adobe Photoshop") {
        alert("请使用Photoshop运行此脚本！");
        return false;
    }

    // 检查时间验证
    if (myDate() > 20251225) {
        alert("此脚本插件已经失效，请在【QQ群248884685】中更新版本！");
        openURL("qm.qq.com/q/mtN87EDPPM");
        return false;
    }
    
    // 获取用户数据文件夹路径
    var userDataFolder = Folder.userData + "/Adobe/Logs";
    var verificationFile = new File(userDataFolder + "/TextPhotoshop.json");
    
    // 检查验证文件是否存在
    if (verificationFile.exists) {
        verificationFile.open("r");
        var fileContent = verificationFile.read();
        verificationFile.close();
        
        try {
            var jsonData = parseJSON(fileContent);
            // 检查JSON内容是否匹配
            if (jsonData["喷印联盟验证信息"] && 
                jsonData["喷印联盟验证信息"]["喷印联盟QQ群:248884685"] === "恒心-perseverance出品") {
                return true; // 验证通过
            }
        } catch (e) {
            // JSON解析错误，需要重新验证
        }
    }
    
    // 创建密码输入对话框
    var pwdDlg = new Window("dialog", "请输入密码验证", undefined);
    pwdDlg.orientation = "column";
    pwdDlg.alignChildren = "center";
    
    var pwdGroup = pwdDlg.add("group");
    pwdGroup.orientation = "row";
    pwdGroup.alignChildren = "center";
    pwdGroup.add("statictext", undefined, "密码:");
    
    // 创建五个密码输入框
    var pwdInputs = [];
    for (var i = 0; i < 5; i++) {
        var pwdInput = pwdGroup.add("edittext", undefined, "", {password: true});
        pwdInput.characters = 1;
        pwdInput.preferredSize.width = 30;
        pwdInputs.push(pwdInput);
    }
    
    var btnGroup = pwdDlg.add("group");
    var cancelBtn = btnGroup.add("button", undefined, "取消");
    var okBtn = btnGroup.add("button", undefined, "确定", {name: "ok"});
    pwdDlg.add("statictext", undefined, "输入密钥方可使用此工具");
    pwdDlg.add("statictext", undefined, "如没有密钥可进QQ群获得");
    pwdDlg.add("statictext", undefined, "喷印联盟QQ群:248884685");
    var result = false;
    
    okBtn.onClick = function() {
        // 获取五个输入框的值并组合
        var password = "";
        for (var i = 0; i < pwdInputs.length; i++) {
            password += pwdInputs[i].text;
        }
        
        // 这里设置正确的密码
        var correctPasswords = ["喷印", "联盟", "QQ群", "248884685", "perseverance"];
        var isCorrect = true;
        
        for (var i = 0; i < correctPasswords.length; i++) {
            if (pwdInputs[i].text !== correctPasswords[i]) {
                isCorrect = false;
                break;
            }
        }
        
        if (isCorrect) {
            result = true;
            
            // 创建验证文件
            try {
                // 确保目录存在
                var folder = new Folder(userDataFolder);
                if (!folder.exists) {
                    folder.create();
                }
                
                // 创建JSON文件
                var file = new File(userDataFolder + "/TextPhotoshop.json");
                file.open("w");
                var jsonContent = stringifyJSON({
                    "喷印联盟验证信息": {
                        "喷印联盟QQ群:248884685": "恒心-perseverance出品"
                    }
                });
                file.write(jsonContent);
                file.close();
            } catch (e) {
                alert("创建验证文件失败: " + e);
            }
            
            pwdDlg.close();
        } else {
            alert("密码错误，请重试！");
        }
    };
    
    cancelBtn.onClick = function() {
        pwdDlg.close();
    };
    
    pwdDlg.center();
    pwdDlg.show();
    
    return result;
}

// 获取当前日期
function myDate() {
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    
    // 格式化为YYYYMMDD格式
    return year * 10000 + month * 100 + day;
}

// 打开URL函数
function openURL(url) {
    var f = File(Folder.temp + "/aiOpenURL.url");
    f.open("w");
    f.write("[InternetShortcut]\rURL=http://" + url + "\r");
    f.close();
    f.execute();
}

// 自定义JSON解析函数
function parseJSON(jsonStr) {
    return (new Function("return " + jsonStr))();
}

// 自定义JSON字符串化函数
function stringifyJSON(obj) {
    var result = [];
    for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
            var value = obj[key];
            if (typeof value === "object") {
                value = stringifyJSON(value);
            } else if (typeof value === "string") {
                value = '"' + value.replace(/"/g, '\\"') + '"';
            }
            result.push('"' + key + '":' + value);
        }
    }
    return "{" + result.join(",") + "}";
}

// 验证用户，如果验证失败则退出
if (!verifyUser()) {
    alert("验证失败，程序将退出！");
} else {
        try {
            // 尝试使用 suspendHistory
            app.activeDocument.suspendHistory("文本查找替换", "main()");
        } catch (e) {
            // 如果出错，直接调用 main 函数
            main();
        }
    }

function main() {
    // 检查是否有打开的文档
    if (documents.length === 0) {
        alert("请先打开一个文档！");
        return;
    }

    // 创建主对话框
    var dlg = new Window("dialog", "文本批量替换 V1.2.4 by 恒心-perseverance", undefined, {resizeable: true, closeButton: true});
    dlg.orientation = "column";
    dlg.alignChildren = "fill";
    
    // ===== 查找和替换面板 =====
    var findReplacePanel = dlg.add("panel", undefined, "查找和替换");
    findReplacePanel.orientation = "column";
    findReplacePanel.alignChildren = "left";
    findReplacePanel.margins = 15;
    
    // 查找文本
    var findGroup = findReplacePanel.add("group");
    findGroup.orientation = "row";
    findGroup.alignChildren = "center";
    findGroup.add("statictext", undefined, "查找:");
    var findText = findGroup.add("edittext", undefined, "", {multiline: true});
    findText.preferredSize.width = 450;
    findText.preferredSize.height = 80;
    
    // 替换文本
    var replaceGroup = findReplacePanel.add("group");
    replaceGroup.orientation = "row";
    replaceGroup.alignChildren = "center";
    replaceGroup.add("statictext", undefined, "替换:");
    var replaceText = replaceGroup.add("edittext", undefined, "", {multiline: true});
    replaceText.preferredSize.width = 450;
    replaceText.preferredSize.height = 80;
    
    // ===== 选项面板 =====
    var optionsPanel = dlg.add("panel", undefined, "选项");
    optionsPanel.orientation = "column";
    optionsPanel.alignChildren = "fill";
    optionsPanel.margins = 15;
    
    // 处理范围和匹配选项 (第一行)
    var row1 = optionsPanel.add("group");
    row1.orientation = "row";
    row1.alignChildren = "top";
    row1.spacing = 20;
    
    // 处理范围子面板
    var scopeGroup = row1.add("group");
    scopeGroup.orientation = "column";
    scopeGroup.alignChildren = "left";
    scopeGroup.add("statictext", undefined, "处理范围:");
    
    var scopeRadioGroup = scopeGroup.add("group");
    scopeRadioGroup.orientation = "row";
    var activeDocRadio = scopeRadioGroup.add("radiobutton", undefined, "当前文档");
    var allDocsRadio = scopeRadioGroup.add("radiobutton", undefined, "所有文档");
    activeDocRadio.value = true;
    
    // 匹配选项子面板
    var matchGroup = row1.add("group");
    matchGroup.orientation = "column";
    matchGroup.alignChildren = "left";
    matchGroup.add("statictext", undefined, "匹配模式:");
    
    var matchModeDropdown = matchGroup.add("dropdownlist", undefined, ["包含", "开头于", "结尾于", "正则表达式"]);
    matchModeDropdown.selection = 0;
    
    // 操作选项 (第二行)
    var row2 = optionsPanel.add("group");
    row2.orientation = "row";
    row2.alignChildren = "top";
    row2.spacing = 20;
    
    // 操作选项子面板
    var filterGroup = row2.add("group");
    filterGroup.orientation = "column";
    filterGroup.alignChildren = "left";
    filterGroup.add("statictext", undefined, "操作选项:");
    
    var filterRow = filterGroup.add("group");
    filterRow.orientation = "row";
    filterRow.spacing = 10;
    var caseSensitiveCheck = filterRow.add("checkbox", undefined, "区分大小写");
    var wholeWordCheck = filterRow.add("checkbox", undefined, "全词匹配");
    var excludeHiddenCheck = filterRow.add("checkbox", undefined, "排除隐藏图层");
    excludeHiddenCheck.value = true;
    
    // ===== 日志区域 =====
    var logPanel = dlg.add("panel", undefined, "操作日志");
    logPanel.orientation = "column";
    logPanel.alignChildren = "fill";
    logPanel.margins = 15;
    
    // 创建日志文本区域
    var logText = logPanel.add("edittext", undefined, "", {multiline: true, readonly: true});
    logText.preferredSize.width = 450;
    logText.preferredSize.height = 80;
    
    // ===== 按钮组 =====
    var btnGroup = dlg.add("group");
    btnGroup.orientation = "row";
    btnGroup.alignChildren = "center";
    
    var cancelBtn = btnGroup.add("button", undefined, "取消");
    var previewBtn = btnGroup.add("button", undefined, "预览");
    var replaceBtn = btnGroup.add("button", undefined, "替换");
    var footerGroup = dlg.add('group');
    footerGroup.orientation = "column"; // 改为垂直排列
    footerGroup.alignment = "center";
    footerGroup.alignChildren = "center";
    var footerText = footerGroup.add('statictext', undefined, '喷印联盟★广告图文技术与开发者探索交流群：248884685');
    footerText.multiline = true; // 启用多行显示
    footerText.alignment = "center";
    var footerText2 = footerGroup.add('statictext', undefined, 'by 恒心-perseverance');
    footerText2.alignment = "center";
    // 存储查找结果
    var findResults = {
        totalDocs: 0,
        totalLayers: 0,
        matchedLayers: [],
        skippedLayers: []
    };
    // 预览按钮事件
    previewBtn.onClick = function() {
        if (!validateInputs()) return;
        
        // 清空之前的结果
        findResults = {
            totalDocs: 0,
            totalLayers: 0,
            matchedLayers: [],
            skippedLayers: []
        };
        
        // 执行查找
        findTextInLayers(true);
        
        // 显示预览结果
        var previewMsg = "预览结果: ";
        previewMsg += "处理文档数: " + findResults.totalDocs + ", ";
        previewMsg += "扫描图层数: " + findResults.totalLayers + ", ";
        previewMsg += "匹配图层数: " + findResults.matchedLayers.length + ", ";
        previewMsg += "跳过图层数: " + findResults.skippedLayers.length + "\n\n";
        
        if (findResults.matchedLayers.length > 0) {
            previewMsg += "匹配图层:\n";
            for (var i = 0; i < Math.min(findResults.matchedLayers.length, 5); i++) {
                var layer = findResults.matchedLayers[i];
                var originalText = layer.text;
                var newText = getReplacedText(originalText);
                previewMsg += "- " + layer.docName + " > " + originalText + " → " + newText + "\n";
            }
            
            if (findResults.matchedLayers.length > 5) {
                previewMsg += "... 以及其他 " + (findResults.matchedLayers.length - 5) + " 个图层\n";
            }
        } else {
            previewMsg += "未找到匹配项。\n";
        }
        
        logText.text = previewMsg;
    };
    // 获取替换后的文本
    function getReplacedText(originalText) {
        var searchText = findText.text;
        var replaceString = replaceText.text;
        var matchMode = matchModeDropdown.selection.index;
        var isCaseSensitive = caseSensitiveCheck.value;
        var isWholeWord = wholeWordCheck.value;
        
        var newText = originalText;
        
        if (matchMode === 3) {
            // 正则表达式替换
            try {
                var regexFlags = (isCaseSensitive ? "" : "i") + "g";
                var regex = new RegExp(searchText, regexFlags);
                newText = originalText.replace(regex, replaceString);
            } catch (e) {
                return originalText + " [正则表达式错误]";
            }
        } else {
            if (isWholeWord) {
                // 全词匹配替换
                var wordBoundaryRegex = new RegExp("\\b" + escapeRegExp(searchText) + "\\b", (isCaseSensitive ? "" : "i") + "g");
                newText = originalText.replace(wordBoundaryRegex, replaceString);
            } else {
                if (matchMode === 0) { // 包含
                    var regexFlags = isCaseSensitive ? "g" : "gi";
                    var regex = new RegExp(escapeRegExp(searchText), regexFlags);
                    newText = originalText.replace(regex, replaceString);
                } else if (matchMode === 1) { // 开头于
                    if ((isCaseSensitive ? originalText : originalText.toLowerCase()).indexOf(
                        (isCaseSensitive ? searchText : searchText.toLowerCase())) === 0) {
                        newText = replaceString + originalText.substring(searchText.length);
                    }
                } else if (matchMode === 2) { // 结尾于
                    var compareText = isCaseSensitive ? originalText : originalText.toLowerCase();
                    var compareSearch = isCaseSensitive ? searchText : searchText.toLowerCase();
                    if (compareText.lastIndexOf(compareSearch) === (compareText.length - compareSearch.length)) {
                        newText = originalText.substring(0, originalText.length - searchText.length) + replaceString;
                    }
                }
            }
        }
        
        return newText;
    }
    
    // 替换按钮事件
    replaceBtn.onClick = function() {
        if (!validateInputs()) return;
        
        // 如果没有预览，先执行查找
        if (findResults.matchedLayers.length === 0) {
            findTextInLayers(true);
        }
        
        // 确认替换
        if (findResults.matchedLayers.length === 0) {
            alert("未找到匹配项，无需替换。");
            return;
        }
        
        var confirmMsg = "确认替换 " + findResults.matchedLayers.length + " 处文本吗？\n";
        confirmMsg += "此操作将影响 " + findResults.totalDocs + " 个文档中的 " + findResults.matchedLayers.length + " 个图层。";
        
        if (!confirm(confirmMsg)) {
            return;
        }
        
        // 执行替换
        var replaceResults = replaceTextInLayers();
        
        // 显示替换结果
        var resultMsg = "替换完成: ";
        resultMsg += "处理文档数: " + replaceResults.totalDocs + ", ";
        resultMsg += "成功替换数: " + replaceResults.successCount + ", ";
        resultMsg += "失败替换数: " + replaceResults.failCount + "\n\n";
        
        if (replaceResults.errors.length > 0) {
            resultMsg += "错误信息:\n";
            for (var i = 0; i < Math.min(replaceResults.errors.length, 3); i++) {
                resultMsg += "- " + replaceResults.errors[i] + "\n";
            }
            
            if (replaceResults.errors.length > 3) {
                resultMsg += "... 以及其他 " + (replaceResults.errors.length - 3) + " 个错误\n";
            }
        }
        
        logText.text = resultMsg;
        
        // 如果全部成功，显示成功消息
        if (replaceResults.failCount === 0) {
            alert("替换成功完成！共替换 " + replaceResults.successCount + " 处文本。");
        } else {
            alert("替换部分完成。成功: " + replaceResults.successCount + ", 失败: " + replaceResults.failCount + "。请查看日志了解详情。");
        }
    };
    
    // 取消按钮事件
    cancelBtn.onClick = function() {
        dlg.close();
    };
    
    // 验证输入
    function validateInputs() {
        if (findText.text === "") {
            alert("请输入要查找的文本！");
            findText.active = true;
            return false;
        }
        return true;
    }
    
    // 检查图层是否锁定的函数
    function isLayerLocked(layer) {
        // 检查所有锁定状态
        if (layer.allLocked || layer.pixelsLocked || layer.positionLocked || layer.transparentPixelsLocked) {
            return true;
        }
        
        // 使用ActionDescriptor检查更深层次的锁定状态
        try {
            var ref = new ActionReference();
            ref.putIdentifier(charIDToTypeID("Lyr "), layer.id);
            var desc = executeActionGet(ref);
            
            // 检查图层是否完全锁定
            if (desc.hasKey(stringIDToTypeID('layerLocking'))) {
                var lockingDesc = desc.getObjectValue(stringIDToTypeID('layerLocking'));
                var protectAll = lockingDesc.getBoolean(stringIDToTypeID('protectAll'));
                if (protectAll) {
                    return true;
                }
            }
        } catch (e) {
            // 如果出错，保守起见认为图层是锁定的
            return true;
        }
        
        return false;
    }
    
    // 解锁图层函数
    function unlockLayer(layer) {
        try {
            // 解锁图层的所有锁定状态
            if (layer.allLocked) {
                layer.allLocked = false;
            }
            
            // 对于非文本图层，解锁特定的锁定类型
            if (layer.kind !== LayerKind.TEXT) {
                if (layer.pixelsLocked) {
                    layer.pixelsLocked = false;
                }
                
                if (layer.transparentPixelsLocked) {
                    layer.transparentPixelsLocked = false;
                }
            }
            
            // 位置锁定可以应用于任何图层类型
            if (layer.positionLocked) {
                layer.positionLocked = false;
            }
            
            return true;
        } catch (e) {
            return false;
        }
    }
    
    // 临时解锁图层并执行操作的函数
    function withUnlockedLayer(layer, callback) {
        // 保存原始锁定状态
        var originalAllLocked = layer.allLocked;
        var originalPixelsLocked = layer.pixelsLocked;
        var originalPositionLocked = layer.positionLocked;
        var originalTransparentPixelsLocked = layer.transparentPixelsLocked;
        
        try {
            // 临时解锁图层
            layer.allLocked = false;
            layer.pixelsLocked = false;
            layer.positionLocked = false;
            layer.transparentPixelsLocked = false;
            
            // 执行回调函数
            callback();
            
            return true;
        } catch (e) {
            // 如果无法解锁或操作失败
            return false;
        } finally {
            // 恢复原始锁定状态
            try {
                layer.allLocked = originalAllLocked;
                layer.pixelsLocked = originalPixelsLocked;
                layer.positionLocked = originalPositionLocked;
                layer.transparentPixelsLocked = originalTransparentPixelsLocked;
            } catch (e) {
                // 忽略恢复锁定状态时的错误
            }
        }
    }
    
    // 查找文本函数
    function findTextInLayers(previewOnly) {
        var searchText = findText.text;
        var isCaseSensitive = caseSensitiveCheck.value;
        var isWholeWord = wholeWordCheck.value;
        var matchMode = matchModeDropdown.selection.index;
        var excludeHidden = excludeHiddenCheck.value;
        
        // 确定处理范围
        var docsToProcess = [];
        
        if (activeDocRadio.value) {
            docsToProcess.push(app.activeDocument);
        } else if (allDocsRadio.value) {
            for (var i = 0; i < app.documents.length; i++) {
                docsToProcess.push(app.documents[i]);
            }
        }
        
        findResults.totalDocs = docsToProcess.length;
        
        // 遍历文档
        for (var d = 0; d < docsToProcess.length; d++) {
            var currentDoc = docsToProcess[d];
            var originalDoc = app.activeDocument;
            
            try {
                app.activeDocument = currentDoc;
                
                // 获取要处理的图层
                var layersToProcess = [];
                
                // 获取所有图层
                collectAllLayers(currentDoc.layers, layersToProcess);
                
                findResults.totalLayers += layersToProcess.length;
                
                // 遍历图层
                for (var l = 0; l < layersToProcess.length; l++) {
                    var layer = layersToProcess[l];
                    
                    // 检查过滤条件
                    if (excludeHidden && !layer.visible) {
                        findResults.skippedLayers.push({
                            name: layer.name,
                            docName: currentDoc.name,
                            reason: "隐藏图层"
                        });
                        continue;
                    }
                    
                    // 检查图层是否锁定
                    var isLocked = isLayerLocked(layer);
                    
                    // 只处理文本图层
                    if (layer.kind == LayerKind.TEXT) {
                        var layerText = layer.textItem.contents;
                        
                        // 检查是否匹配
                        var isMatch = false;
                        
                        if (matchMode === 3) {
                            // 正则表达式模式
                            try {
                                var regexFlags = isCaseSensitive ? "" : "i";
                                var regex = new RegExp(searchText, regexFlags);
                                isMatch = regex.test(layerText);
                            } catch (e) {
                                alert("正则表达式错误: " + e.message);
                                return;
                            }
                        } else {
                            // 其他匹配模式
                            var compareLayerText = isCaseSensitive ? layerText : layerText.toLowerCase();
                            var compareSearchText = isCaseSensitive ? searchText : searchText.toLowerCase();
                            
                            if (isWholeWord) {
                                // 全词匹配
                                var wordBoundaryRegex = new RegExp("\\b" + escapeRegExp(compareSearchText) + "\\b", isCaseSensitive ? "" : "i");
                                isMatch = wordBoundaryRegex.test(compareLayerText);
                            } else {
                                // 非全词匹配
                                switch (matchMode) {
                                    case 0: // 包含
                                        isMatch = compareLayerText.indexOf(compareSearchText) !== -1;
                                        break;
                                    case 1: // 开头于
                                        isMatch = compareLayerText.indexOf(compareSearchText) === 0;
                                        break;
                                    case 2: // 结尾于
                                        isMatch = compareLayerText.lastIndexOf(compareSearchText) === (compareLayerText.length - compareSearchText.length);
                                        break;
                                }
                            }
                        }
                        
                        if (isMatch) {
                            // 将匹配的图层添加到匹配图层列表中
                            findResults.matchedLayers.push({
                                layer: layer,
                                name: layer.name,
                                docName: currentDoc.name,
                                docIndex: d,
                                text: layerText,
                                locked: isLocked
                            });
                        }
                    }
                }
            } catch (e) {
                alert("处理文档 " + currentDoc.name + " 时出错: " + e.message);
            } finally {
                // 恢复原始文档
                app.activeDocument = originalDoc;
            }
        }
        
        return findResults;
    }
    
    // 替换文本函数
    function replaceTextInLayers() {
        var replaceString = replaceText.text;
        var matchMode = matchModeDropdown.selection.index;
        var isCaseSensitive = caseSensitiveCheck.value;
        var isWholeWord = wholeWordCheck.value;
        var searchText = findText.text;
        
        var results = {
            totalDocs: findResults.totalDocs,
            successCount: 0,
            failCount: 0,
            errors: []
        };
        
        // 创建处理过的文档集合
        var processedDocs = {};
        
        // 遍历匹配的图层
        for (var i = 0; i < findResults.matchedLayers.length; i++) {
            var match = findResults.matchedLayers[i];
            var layer = match.layer;
            var docIndex = match.docIndex;
            var isLocked = match.locked;
            
            try {
                // 激活文档
                app.activeDocument = app.documents[docIndex];
                
                // 记录已处理的文档
                if (!processedDocs[docIndex]) {
                    // 为每个文档创建历史快照
                    app.activeDocument.suspendHistory("文本替换操作", "");
                    processedDocs[docIndex] = true;
                }
                
                // 如果图层锁定，尝试解锁
                if (isLocked) {
                    var unlocked = unlockLayer(layer);
                    
                    if (!unlocked) {
                        results.errors.push("无法解锁图层: " + layer.name);
                        results.failCount++;
                        continue;
                    }
                }
                
                // 图层未锁定或已成功解锁，执行替换
                try {
                    // 获取原始文本
                    var originalText = layer.textItem.contents;
                    
                    // 执行替换
                    var newText = getReplacedText(originalText);
                    
                    // 应用新文本
                    layer.textItem.contents = newText;
                    results.successCount++;
                } catch (textError) {
                    results.errors.push("无法修改图层文本: " + layer.name + " - " + textError.message);
                    results.failCount++;
                }
            } catch (e) {
                results.errors.push("处理图层 " + match.name + " 时出错: " + e.message);
                results.failCount++;
            }
        }
        
        return results;
    }
    
    // 辅助函数：转义正则表达式特殊字符
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    // 辅助函数：递归收集所有图层
    function collectAllLayers(layers, result) {
        for (var i = 0; i < layers.length; i++) {
            var layer = layers[i];
            
            // 添加当前图层
            result.push(layer);
            
            // 如果是图层组，递归处理
            if (layer.typename === "LayerSet") {
                collectAllLayers(layer.layers, result);
            }
        }
    }
    
    // 显示对话框
    dlg.center();
    dlg.show();
}
