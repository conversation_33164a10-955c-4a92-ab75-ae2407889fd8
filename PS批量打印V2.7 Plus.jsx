// 全局变量
// 存储原始文档排序的路径
var originalDocPaths = [];

// 全局变量用于打印统计
// 这些变量在UI中使用
var gTotalDocs = 0;
var gPrintedDocs = 0;

var gStatusText = null;
var gTotalDocsText = null;
var gPrintedDocsText = null;


// 保存原始文档排序
function saveOriginalDocOrder() {
    originalDocPaths = [];
    // 保存当前打开的所有文档的路径和名称
    for (var i = 0; i < app.documents.length; i++) {
        try {
            var doc = app.documents[i];
            var docInfo = {
                name: doc.name,
                path: ""
            };
            
            // 尝试获取文档路径（如果有）
            if (doc.path != "") {
                docInfo.path = doc.fullName.toString();
            }
            
            originalDocPaths.push(docInfo);
            debugLog("保存文档: " + doc.name);
        } catch (e) {
            debugLog("保存文档顺序时出错: " + e);
        }
    }
    debugLog("已保存 " + originalDocPaths.length + " 个文档的原始排序");
}

// 恢复原始文档排序
function restoreDocOrder() {
    if (originalDocPaths.length === 0) {
        if (gStatusText) gStatusText.text = "没有保存的原始文档排序";
        app.refresh();
        return false;
    }
    
    // 创建当前打开文档的映射，以便快速查找
    var currentDocs = {};
    for (var i = 0; i < app.documents.length; i++) {
        try {
            currentDocs[app.documents[i].name] = app.documents[i];
        } catch (e) {
            debugLog("映射当前文档时出错: " + e);
        }
    }
    
    // 关闭所有文档
    while (app.documents.length > 0) {
        app.documents[0].close(SaveOptions.DONOTSAVECHANGES);
    }
    
    // 按原始顺序重新打开文档
    var reopenedCount = 0;
    for (var i = 0; i < originalDocPaths.length; i++) {
        try {
            var docInfo = originalDocPaths[i];
            var path = docInfo.path;
            var name = docInfo.name;
            
            // 如果有路径，尝试使用路径打开
            if (path && path !== "") {
                debugLog("尝试打开文档路径: " + path);
                var result = safeOpenFile(path);
                if (result) {
                    reopenedCount++;
                    continue;
                }
            }
            
            // 如果没有路径或路径打开失败，尝试使用名称
            debugLog("尝试打开文档名称: " + name);
            var result = safeOpenFile(name);
            if (result) reopenedCount++;
            
        } catch (e) {
            debugLog("重新打开文档时出错: " + e);
        }
    }
    if (gStatusText) gStatusText.text = "恢复排序完成";
    app.refresh();
    return true;
}

// 按文件名重新排序文档
function reorderDocsByName() {
    if (app.documents.length <= 1) {
        if (gStatusText) gStatusText.text = "无需排序";
        app.refresh();
        return false;
    }
    // 收集所有文档路径与名称
    var docs = [];
    for (var i = 0; i < app.documents.length; i++) {
        docs.push({name: app.documents[i].name, path: app.documents[i].fullName});
    }
    // 生成按文件名排序的新顺序
    var sorted = docs.slice().sort(function(a, b) { return a.name.localeCompare(b.name); });
    // 检查是否已排序
    var changed = false;
    for (var i = 0; i < docs.length; i++) {
        if (docs[i].name !== sorted[i].name) { changed = true; break; }
    }
    if (!changed) {
        if (gStatusText) gStatusText.text = "无需排序";
        app.refresh();
        return false;
    }
    // 一次性关闭所有文档
    while (app.documents.length > 0) {
        app.documents[0].close(SaveOptions.DONOTSAVECHANGES);
    }
    // 快速按新顺序打开
    for (var i = 0; i < sorted.length; i++) {
        safeOpenFile(sorted[i].path);
    }
    if (gStatusText) gStatusText.text = "已按文件名重新排序文档";
    app.refresh();
    return true;
}

function main() {
    // Check if Photoshop is running
    if (app.documents.length === 0) {
        alert("请先打开至少一个文档！");
        return;
    }
    // 保存原始文档排序
    saveOriginalDocOrder();

    // Create dialog - 使用标准dialog类型的窗口
    var dlg = new Window("dialog", "Photoshop批量打印V2.7 Plus");
    dlg.alignChildren = ["center", "top"];
    dlg.spacing = 10;
    dlg.margins = 16;

    // Main print button
    var mainPrintBtn = dlg.add("button", undefined, "【 批 量 打 印 】");
    mainPrintBtn.preferredSize.width = 150;
    // 设置按钮文本为橙色
    try {
        var orangeColor = mainPrintBtn.graphics.newPen(mainPrintBtn.graphics.PenType.SOLID_COLOR, [1, 0.5, 0], 1);
        mainPrintBtn.graphics.foregroundColor = orangeColor;
    } catch (e) {}


    // Order buttons group
    var orderGroup = dlg.add("group");
    orderGroup.orientation = "row";
    orderGroup.alignChildren = ["center", "center"];
    orderGroup.spacing = 10;

    var restoreOrderBtn = orderGroup.add("button", undefined, "恢复排序");
    restoreOrderBtn.preferredSize.width = 100;

    var reorderBtn = orderGroup.add("button", undefined, "重新排序");
    reorderBtn.preferredSize.width = 100;

    // 合并尺寸输入控件为一行（上文本，下输入框）
    var sizeRowGroup = dlg.add("group");
    sizeRowGroup.orientation = "row";
    sizeRowGroup.alignChildren = ["center", "top"];
    sizeRowGroup.spacing = 5; // 缩小左右间距

    // 宽度
    var widthGroup = sizeRowGroup.add("group");
    widthGroup.orientation = "column";
    widthGroup.alignChildren = ["center", "top"];
    widthGroup.spacing = 2;
    widthGroup.add("statictext", undefined, "宽度(毫米)");
    var widthInput = widthGroup.add("edittext", undefined, "210");
    widthInput.preferredSize.width = 50;

    // 比例锁定按钮（链条icon）
    var lockGroup = sizeRowGroup.add("group");
    lockGroup.orientation = "row";
    lockGroup.alignChildren = ["center", "bottom"];
    // 无margins，保证底对齐
    lockGroup.spacing = 0;
    // 透明statictext占位，下移按钮
    var lockSpacer = lockGroup.add("statictext", undefined, "");
    lockSpacer.preferredSize = {width: 1, height: 40}; // 适当高度可微调
    // 使用unicode链条符号，淡黑色背景，23x23px
    var lockBtn = lockGroup.add("button", undefined, "\uD83E\uDDF7", {name: "lock"}); // 默认未激活，🧷
    lockBtn.preferredSize = {width: 23, height: 23}; // 高度与输入框一致
    // 不设置backgroundColor，避免报错
    lockBtn.graphics.foregroundColor = lockBtn.graphics.newPen(lockBtn.graphics.PenType.SOLID_COLOR, [0.7,0.7,0.7], 1);
    lockBtn.text = "\uD83E\uDDF7"; // 🧷
    lockBtn.helpTip = "点击锁定宽高比例";
    var isLocked = false;
    var whRatio = 1;

    lockBtn.onClick = function() {
        isLocked = !isLocked;
        if (isLocked) {
            // 切换为锁定，记录当前比例
            var w = parseFloat(widthInput.text);
            var h = parseFloat(heightInput.text);
            whRatio = (!isNaN(w) && !isNaN(h) && h !== 0) ? w / h : 1;
            lockBtn.text = "\uD83D\uDD17"; // 🔗
            lockBtn.helpTip = "已锁定宽高比例";
        } else {
            lockBtn.text = "\uD83E\uDDF7"; // 🧷
            lockBtn.helpTip = "点击锁定宽高比例";
        }
    };



    // 高度
    var heightGroup = sizeRowGroup.add("group");
    heightGroup.orientation = "column";
    heightGroup.alignChildren = ["center", "top"];
    heightGroup.spacing = 2;
    heightGroup.add("statictext", undefined, "高度(毫米)");
    var heightInput = heightGroup.add("edittext", undefined, "297");
    heightInput.preferredSize.width = 50;
    var lastHeight = parseFloat(heightInput.text);
    var lastWidth = parseFloat(widthInput.text);
    var suppressChange = false;

    // 宽度输入变化时联动高度
    widthInput.onChanging = function() {
        if (suppressChange) return;
        var w = parseFloat(widthInput.text);
        var h = parseFloat(heightInput.text);
        if (isLocked && !isNaN(w) && whRatio > 0) {
            suppressChange = true;
            var newH = w / whRatio;
            heightInput.text = (Math.round(newH * 100) / 100).toString();
            suppressChange = false;
        }
        lastWidth = w;
    };
    // 高度输入变化时联动宽度
    heightInput.onChanging = function() {
        if (suppressChange) return;
        var w = parseFloat(widthInput.text);
        var h = parseFloat(heightInput.text);
        if (isLocked && !isNaN(h) && whRatio > 0) {
            suppressChange = true;
            var newW = h * whRatio;
            widthInput.text = (Math.round(newW * 100) / 100).toString();
            suppressChange = false;
        }
        lastHeight = h;
    };


    // 分辨率
    var resGroup = sizeRowGroup.add("group");
    resGroup.orientation = "column";
    resGroup.alignChildren = ["center", "top"];
    resGroup.spacing = 2;
    resGroup.add("statictext", undefined, "分辨率(dpi)");
    var resolutionInput = resGroup.add("edittext", undefined, "300");
    resolutionInput.preferredSize.width = 50;

    // 勾选框与调整图像大小按钮同一行
    var propResizeGroup = dlg.add("group");
    propResizeGroup.orientation = "row";
    propResizeGroup.alignChildren = ["center", "center"];
    var proportionalCheck = propResizeGroup.add("checkbox", undefined, "按比例调整");
    proportionalCheck.value = true;
    var resizeBtn = propResizeGroup.add("button", undefined, "调整图像大小");
    resizeBtn.preferredSize.width = 120;

    // 横转竖与竖转横按钮组
    var rotateGroup = dlg.add("group");
    rotateGroup.orientation = "row";
    rotateGroup.alignChildren = ["center", "center"];
    rotateGroup.spacing = 10;
    var rotateToPortraitBtn = rotateGroup.add("button", undefined, "横转竖");
    rotateToPortraitBtn.preferredSize.width = 100;
    var rotateToLandscapeBtn = rotateGroup.add("button", undefined, "竖转横");
    rotateToLandscapeBtn.preferredSize.width = 100;

    // 打印配置按钮
    var btnRowGroup = dlg.add("group");
    btnRowGroup.orientation = "row";
    btnRowGroup.alignChildren = ["center", "center"];
    btnRowGroup.spacing = 10;

    var openPrintDialogBtn = btnRowGroup.add("button", undefined, "打印配置");
    openPrintDialogBtn.preferredSize.width = 100;

    var advancedSettingsBtn = btnRowGroup.add("button", undefined, "高级设置");
    advancedSettingsBtn.preferredSize.width = 100;
    advancedSettingsBtn.onClick = function() {
        showAdvancedSettingsDialog();
    };

    // 打印和关闭按钮
    var actionGroup = dlg.add("group");
    actionGroup.orientation = "row";
    actionGroup.alignChildren = ["center", "center"];
    actionGroup.spacing = 10;

    var printBtn = actionGroup.add("button", undefined, "打印当前");
    printBtn.preferredSize.width = 100;

    var closeBtn = actionGroup.add("button", undefined, "关闭退出");
    closeBtn.preferredSize.width = 100;

    // 添加打印统计信息面板 - 移动到底部
    var statsPanel = dlg.add("panel", undefined, "数据加载，打印进度及统计");
    statsPanel.orientation = "column";
    statsPanel.alignChildren = ["left", "top"];
    statsPanel.spacing = 5;
    statsPanel.margins = 10;
    statsPanel.preferredSize.width = 300;

    // 添加状态文本 - 关联到全局变量
    gStatusText = statsPanel.add("statictext", undefined, "正在加载打印数据...");
    gStatusText.preferredSize.width = 280;

    // 添加打印统计信息 - 关联到全局变量
    gTotalDocsText = statsPanel.add("statictext", undefined, "总文档数: " + app.documents.length);
    gPrintedDocsText = statsPanel.add("statictext", undefined, "已打印文档: 0");

    // Footer text
    var footerText1 = dlg.add("statictext", undefined, "恒心-perseverance");
    footerText1.alignment = "center";
    footerText1.graphics.foregroundColor = footerText1.graphics.newPen(footerText1.graphics.PenType.SOLID_COLOR, [1, 0.5, 0], 1);




    // 按钮功能
    mainPrintBtn.onClick = function() {
        batchPrint();
    };

    restoreOrderBtn.onClick = function() {
        if (restoreDocOrder()) {
            if (gStatusText) gStatusText.text = "已恢复原始文档排序";
            app.refresh();
        }
    };

    reorderBtn.onClick = function() {
        if (reorderDocsByName()) {
            if (gStatusText) gStatusText.text = "已按文件名重新排序文档";
            app.refresh();
        }
    };

    resizeBtn.onClick = function() {
        resizeImages(parseFloat(widthInput.text), parseFloat(heightInput.text), parseFloat(resolutionInput.text), proportionalCheck.value);
    };

    // 横转竖：所有文档宽>高时顺时针旋转90度
    rotateToPortraitBtn.onClick = function() {
        if (app.documents.length === 0) {
            if (gStatusText) gStatusText.text = "没有打开的文档！";
            app.refresh();
            return;
        }
        var total = app.documents.length;
        var rotated = 0;
        for (var i = 0; i < total; i++) {
            var doc = app.documents[i];
            if (doc.width > doc.height) {
                try {
                    doc.rotateCanvas(90);
                    rotated++;
                } catch (e) {
                    // 忽略个别文档出错
                }
            }
        }
        if (gStatusText) gStatusText.text = "横转竖完成，总文档数: " + total + "，已旋转: " + rotated;
        app.refresh();
    };

    // 竖转横：所有文档高>宽时顺时针旋转90度
    rotateToLandscapeBtn.onClick = function() {
        if (app.documents.length === 0) {
            if (gStatusText) gStatusText.text = "没有打开的文档！";
            app.refresh();
            return;
        }
        var total = app.documents.length;
        var rotated = 0;
        for (var i = 0; i < total; i++) {
            var doc = app.documents[i];
            if (doc.height > doc.width) {
                try {
                    doc.rotateCanvas(90);
                    rotated++;
                } catch (e) {
                    // 忽略个别文档出错
                }
            }
        }
        if (gStatusText) gStatusText.text = "竖转横完成，总文档数: " + total + "，已旋转: " + rotated;
        app.refresh();
    };



    printBtn.onClick = function() {
        printCurrentDocument();
    };

    // 打开打印界面按钮的点击事件
    openPrintDialogBtn.onClick = function() {
        openPrintDialog();
    };

    closeBtn.onClick = function() {
        // 在关闭对话框时清空原始文档排序记忆
        originalDocPaths = [];
        dlg.close();
    };


    // 显示对话框
    dlg.center();
    return dlg.show();
}

// 调整图像大小
function resizeImages(width, height, resolution, keepProportion) {
    if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
        if (gStatusText) gStatusText.text = "请输入有效的宽度和高度值！";
        app.refresh();
        return;
    }

    if (isNaN(resolution) || resolution <= 0) {
        if (gStatusText) gStatusText.text = "请输入有效的分辨率值！";
        app.refresh();
        return;
    }

    // 更新状态
    if (gStatusText) gStatusText.text = "正在准备调整图像大小...";
    app.refresh();

    try {
        // 保存原始单位和对话框模式
        var originalRulerUnits = app.preferences.rulerUnits;
        var originalDialogMode = app.displayDialogs;

        // 设置为毫米单位并禁用对话框
        app.displayDialogs = DialogModes.NO;
        app.preferences.rulerUnits = Units.MM;

        // 优化：减少状态刷新，跳过异常文档
        var docsToResize = [];
        for (var i = 0; i < app.documents.length; i++) {
            docsToResize.push(app.documents[i]);
        }
        var totalDocs = docsToResize.length;
        var errorDocs = [];
        for (var i = 0; i < totalDocs; i++) {
            var doc = docsToResize[i];
            try {
                // 只每10张更新一次状态，减少刷新次数
                if (i % 10 === 0 && gStatusText) {
                    gStatusText.text = "正在调整图像: " + doc.name + " (" + (i+1) + "/" + totalDocs + ")";
                    app.refresh();
                }
                // 每次resize前都设为活动文档，确保操作有效
                app.activeDocument = doc;
                if (keepProportion) {
                    var ratio = doc.width.as('mm') / doc.height.as('mm');
                    var newWidth, newHeight;
                    if (ratio > width / height) {
                        newWidth = width;
                        newHeight = newWidth / ratio;
                    } else {
                        newHeight = height;
                        newWidth = newHeight * ratio;
                    }
                    doc.resizeImage(UnitValue(newWidth, "mm"), UnitValue(newHeight, "mm"),
                                   resolution, ResampleMethod.BICUBIC);
                } else {
                    doc.resizeImage(UnitValue(width, "mm"), UnitValue(height, "mm"),
                                   resolution, ResampleMethod.BICUBIC);
                }
            } catch (e) {
                errorDocs.push(doc.name);
                continue;
            }
        }
        // 处理完后统一刷新和状态更新
        if (gStatusText) {
            if (errorDocs.length === 0) {
                gStatusText.text = "所有图像大小调整完成!";
            } else {
                gStatusText.text = "完成，部分文档出错: " + errorDocs.join(", ");
            }
        }
        app.refresh();

        // 恢复原始设置
        app.preferences.rulerUnits = originalRulerUnits;
        app.displayDialogs = originalDialogMode;

        // 更新状态
        if (gStatusText) gStatusText.text = "所有图像大小调整完成!";
        app.refresh();
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalRulerUnits !== 'undefined') {
            app.preferences.rulerUnits = originalRulerUnits;
        }
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }

        // 更新错误状态
        if (gStatusText) gStatusText.text = "调整大小时出错: " + e;
        app.refresh();
    }
}

// 简单的日志函数
function logMessage(message) {
    // 只在控制台输出，不显示对话框
    $.writeln(message);
}

// 安全打开文件函数
function safeOpenFile(filePath) {
    try {
        var fileRef = new File(filePath);
        if (fileRef.exists) {
            app.open(fileRef);
            return true;
        } else {
            debugLog("文件不存在: " + filePath);
            return false;
        }
    } catch (e) {
        debugLog("打开文件出错: " + filePath + ", 错误: " + e);
        return false;
    }
}

// 调试日志函数 - 只在开发模式下输出
function debugLog(message) {
    // 设置为false可以关闭调试输出
    var debugMode = false;
    if (debugMode) {
        $.writeln(message);
    }
}

// 安全打开文件函数 - 处理中文路径问题
function safeOpenFile(filePath) {
    try {
        // 先尝试直接打开
        var fileObj = new File(filePath);
        if (fileObj.exists) {
            // 设置当前对话框模式为无，抑制警告
            var originalDialogMode = app.displayDialogs;
            app.displayDialogs = DialogModes.NO;

            try {
                app.open(fileObj);
                app.displayDialogs = originalDialogMode;
                return true;
            } catch (e) {
                // 如果直接打开失败，尝试使用Action接口
                app.displayDialogs = originalDialogMode;

                var idOpn = charIDToTypeID("Opn ");
                var desc = new ActionDescriptor();
                var idnull = charIDToTypeID("null");
                desc.putPath(idnull, fileObj);
                executeAction(idOpn, desc, DialogModes.NO);
                return true;
            }
        } else {
            debugLog("文件不存在: " + filePath);
            return false;
        }
    } catch (err) {
        debugLog("打开文件时出错: " + err + ", 路径: " + filePath);
        return false;
    }
}

// 打印方法 - 只使用成功的方法
function printDocument(doc) {
    try {
        logMessage("打印文档: " + doc.name);
        doc.print();
        return true;
    } catch(err) {
        logMessage("打印失败: " + err);
        return false;
    }
}

// 只打印当前活动文档
function printCurrentDocument() {
    if (app.documents.length === 0) {
        if (gStatusText) gStatusText.text = "没有打开的文档！";
        app.refresh();
        return;
    }

    // 更新状态
    if (gStatusText) gStatusText.text = "正在打印: " + app.activeDocument.name;
    app.refresh();

    logMessage("打印当前文档: " + app.activeDocument.name);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 打印当前文档
        var success = printDocument(app.activeDocument);

        // 更新统计信息
        gPrintedDocs = 1;
        gSuccessCount = success ? 1 : 0;

        // 更新UI显示
        if (gTotalDocsText) gTotalDocsText.text = "总文档数: 1";
        if (gPrintedDocsText) gPrintedDocsText.text = "已打印文档: 1";
        if (gSuccessRateText) {
            var rate = success ? 100 : 0;
            gSuccessRateText.text = "成功率: " + rate + "%";
        }

        // 更新状态
        if (gStatusText) gStatusText.text = success ? "打印成功!" : "打印失败!";
        app.refresh();

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }

        // 更新错误状态
        if (gStatusText) gStatusText.text = "打印时出错: " + e;
        app.refresh();
    }
}

// Function to batch print all open documents
function batchPrint() {
    if (app.documents.length === 0) {
        if (gStatusText) gStatusText.text = "没有打开的文档！";
        app.refresh();
        return;
    }

    // 重置打印统计
    gTotalDocs = app.documents.length;
    gPrintedDocs = 0;
    gSuccessCount = 0;

    // 更新UI显示
    if (gStatusText) gStatusText.text = "正在准备批量打印...";
    if (gTotalDocsText) gTotalDocsText.text = "总文档数: " + gTotalDocs;
    if (gPrintedDocsText) gPrintedDocsText.text = "已打印文档: 0";


    // 刷新UI
    app.refresh();

    logMessage("开始批量打印操作, 文档数量: " + gTotalDocs);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 处理所有打开的文档
        for (var i = 0; i < app.documents.length; i++) {
            var doc = app.documents[i];
            app.activeDocument = doc;

            // 更新状态
            if (gStatusText) gStatusText.text = "正在打印: " + doc.name + " (" + (i+1) + "/" + gTotalDocs + ")";
            app.refresh();

            // 使用成功的打印方法
            if (printDocument(doc)) {
                gSuccessCount++;
            }

            // 更新打印计数
            gPrintedDocs++;

            // 更新UI显示
            if (gPrintedDocsText) gPrintedDocsText.text = "已打印文档: " + gPrintedDocs;
            // 刷新UI
            app.refresh();
        }

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;

        // 更新最终状态
        if (gStatusText) gStatusText.text = "打印完成! 成功打印: " + gSuccessCount + "/" + gTotalDocs;
        app.refresh();
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }

        // 更新错误状态
        if (gStatusText) gStatusText.text = "打印时出错: " + e;
        app.refresh();
    }
}

// 打开打印对话框函数
function openPrintDialog() {
    if (app.documents.length === 0) {
        if (gStatusText) gStatusText.text = "没有打开的文档！";
        app.refresh();
        return;
    }

    // 更新状态
    if (gStatusText) gStatusText.text = "正在打开打印对话框...";
    app.refresh();

    // 使用最简单的方法直接调用打印命令
    var idPrnt = charIDToTypeID("Prnt");
    executeAction(idPrnt, undefined, DialogModes.ALL);
}

// ========== 高级设置弹窗及功能集成 ===========
function showAdvancedSettingsDialog() {
    // 动态获取当前打开的文档名
    var fileNames = [];
    if (app.documents.length === 0) {
        alert("没有打开的文档！");
        return;
    }
    var widthArr = [], heightArr = [], resArr = [];
    function refreshDocSizeInfo() {
        widthArr = [];
        heightArr = [];
        resArr = [];
        fileNames = [];
        var docCount = app.documents.length;
        if (docCount === 0) return;
        for (var i = 0; i < docCount; i++) {
            var doc = app.documents[i];
            fileNames.push(doc.name);
            widthArr.push(Math.round(doc.width.as('mm')));
            heightArr.push(Math.round(doc.height.as('mm')));
            resArr.push(Math.round(doc.resolution));
        }
    }
    refreshDocSizeInfo();
    var docCount = fileNames.length;
    // 勾选状态数组
    var rotateChecks = [], resizeChecks = [], printChecks = [];
    for (var i = 0; i < docCount; i++) {
        rotateChecks.push(false);
        resizeChecks.push(false);
        printChecks.push(false);
    }
    // 主对话框
    var dlg = new Window("dialog", "高级设置", undefined, {resizeable:true});
    dlg.orientation = "column";
    dlg.alignChildren = ["fill", "top"];
    dlg.spacing = 10;
    dlg.margins = 16;
    // 文件列表区域
    var listWrap = dlg.add("group");
    listWrap.orientation = "row";
    listWrap.alignChildren = ["left", "top"];
    var listPanel = listWrap.add("panel");
    listPanel.text = "文件列表";
    listPanel.orientation = "row";
    listPanel.alignChildren = ["left", "top"];
    listPanel.margins = [10,10,10,10];
    listPanel.preferredSize = [900, 380];
    var listCol = listPanel.add("group");
    listCol.orientation = "column";
    listCol.alignChildren = ["fill", "top"];
    // 1. 标题栏
    var headerRow = listCol.add("group");
    headerRow.orientation = "row";
    headerRow.alignChildren = ["left", "center"];
    headerRow.spacing = 4;
    var headerFileLabel = headerRow.add("statictext", undefined, "文件名");
    headerFileLabel.preferredSize = [180, 24];
    headerFileLabel.justify = "left";
    var headerWidth = headerRow.add("statictext", undefined, "宽度");
    headerWidth.preferredSize = [70, 24];
    var headerHeight = headerRow.add("statictext", undefined, "高度");
    headerHeight.preferredSize = [70, 24];
    var headerRes = headerRow.add("statictext", undefined, "分辨率");
    headerRes.preferredSize = [70, 24];
    var headerDirection = headerRow.add("statictext", undefined, "方向");
    headerDirection.preferredSize = [50, 24];
    // 旋转列头
    var headerRotateGroup = headerRow.add("group");
    headerRotateGroup.orientation = "row";
    headerRotateGroup.alignChildren = ["left", "center"];
    var headerRotateLabel = headerRotateGroup.add("statictext", undefined, "旋转");
    headerRotateLabel.preferredSize = [24, 24];
    var headerRotateCheck = headerRotateGroup.add("checkbox", undefined, "");
    headerRotateCheck.preferredSize = [50, 10];
    // 调整图像大小列头
    var headerResizeGroup = headerRow.add("group");
    headerResizeGroup.orientation = "row";
    headerResizeGroup.alignChildren = ["left", "center"];
    var headerResizeLabel = headerResizeGroup.add("statictext", undefined, "调整大小");
    headerResizeLabel.preferredSize = [48, 24];
    var headerResizeCheck = headerResizeGroup.add("checkbox", undefined, "");
    headerResizeCheck.preferredSize = [48, 18];
    // 打印列头
    var headerPrintGroup = headerRow.add("group");
    headerPrintGroup.orientation = "row";
    headerPrintGroup.alignChildren = ["left", "center"];
    var headerPrintLabel = headerPrintGroup.add("statictext", undefined, "打印");
    headerPrintLabel.preferredSize = [32, 24];
    var headerPrintCheck = headerPrintGroup.add("checkbox", undefined, "");
    headerPrintCheck.preferredSize = [18, 18];
    // 全选逻辑
    headerRotateCheck.onClick = function() {
        for (var i = 0; i < docCount; i++) rotateChecks[i] = headerRotateCheck.value ? true : false;
        renderRows();
    };
    headerResizeCheck.onClick = function() {
        for (var i = 0; i < docCount; i++) resizeChecks[i] = headerResizeCheck.value ? true : false;
        renderRows();
    };
    headerPrintCheck.onClick = function() {
        for (var i = 0; i < docCount; i++) printChecks[i] = headerPrintCheck.value ? true : false;
        renderRows();
    };
    function syncHeaderChecks() {
        // 旋转
        if (headerRotateCheck && rotateChecks && rotateChecks.length) {
            var allChecked = true;
            for (var i = 0; i < rotateChecks.length; i++) {
                if (!rotateChecks[i]) { allChecked = false; break; }
            }
            headerRotateCheck.value = allChecked;
        }
        // 调整图像大小
        if (headerResizeCheck && resizeChecks && resizeChecks.length) {
            var allChecked2 = true;
            for (var i = 0; i < resizeChecks.length; i++) {
                if (!resizeChecks[i]) { allChecked2 = false; break; }
            }
            headerResizeCheck.value = allChecked2;
        }
        // 打印
        if (headerPrintCheck && printChecks && printChecks.length) {
            var allChecked3 = true;
            for (var i = 0; i < printChecks.length; i++) {
                if (!printChecks[i]) { allChecked3 = false; break; }
            }
            headerPrintCheck.value = allChecked3;
        }
    }
    var configTitle = headerRow.add("statictext", undefined, "");
    configTitle.preferredSize = [90, 24];
    // 2. 内容行区 rowsContainer
    var rowsContainer = listCol.add("group");
    rowsContainer.orientation = "column";
    rowsContainer.alignChildren = ["fill", "top"];
    rowsContainer.preferredSize = [760, -1];
    rowsContainer.alignment = 'left';
    // 右侧滚动条
    var scrollbar = listWrap.add('scrollbar', undefined, 'vertical');
    scrollbar.size = [24, 335];
    var rowHeight = 32;
    var visibleRows = Math.min(docCount, 8);
    var scrollIndex = 0;
    var rotateChecksUI = [], resizeChecksUI = [], configBtns = [];
    function renderRows() {
        while (rowsContainer.children.length > 0) rowsContainer.remove(rowsContainer.children[0]);
        for (var i = 0; i < visibleRows && (scrollIndex + i) < docCount; i++) {
            var idx = scrollIndex + i;
            var row = rowsContainer.add("group");
            row.orientation = "row";
            row.alignChildren = ["left", "center"];
            row.spacing = 4;
            var fileLabel = row.add("statictext", undefined, fileNames[idx]);
            fileLabel.preferredSize = [180, 24];
            // 颜色逻辑
            var dirVal = "--", dirColor = null;
            if (widthArr[idx] !== undefined && heightArr[idx] !== undefined) {
                if (widthArr[idx] > heightArr[idx]) {
                    dirVal = "横";
                    dirColor = [0, 180, 0]; // 绿色
                } else if (heightArr[idx] > widthArr[idx]) {
                    dirVal = "竖";
                    dirColor = [255, 140, 0]; // 橙色
                } else if (widthArr[idx] === heightArr[idx]) {
                    dirVal = "相等";
                    dirColor = [255, 0, 0]; // 红色
                }
            }
            var widthText = row.add("statictext", undefined, widthArr[idx] !== undefined ? (widthArr[idx] + " mm") : "--");
            widthText.preferredSize = [70, 24];
            var heightText = row.add("statictext", undefined, heightArr[idx] !== undefined ? (heightArr[idx] + " mm") : "--");
            heightText.preferredSize = [70, 24];
            // 设置宽高颜色
            if (dirColor) {
                try {
                    widthText.graphics.foregroundColor = widthText.graphics.newPen(widthText.graphics.PenType.SOLID_COLOR, [dirColor[0]/255, dirColor[1]/255, dirColor[2]/255], 1);
                    heightText.graphics.foregroundColor = heightText.graphics.newPen(heightText.graphics.PenType.SOLID_COLOR, [dirColor[0]/255, dirColor[1]/255, dirColor[2]/255], 1);
                } catch(e) {}
            }
            var resText = row.add("statictext", undefined, resArr[idx] !== undefined ? resArr[idx] + " dpi" : "--");
            resText.preferredSize = [70, 24];
            // 方向列
            var dirText = row.add("statictext", undefined, dirVal);
            dirText.preferredSize = [50, 24];
            // 设置方向颜色
            if (dirColor) {
                try {
                    dirText.graphics.foregroundColor = dirText.graphics.newPen(dirText.graphics.PenType.SOLID_COLOR, [dirColor[0]/255, dirColor[1]/255, dirColor[2]/255], 1);
                } catch(e) {}
            }
            var rotateGroup = row.add("group");
            rotateGroup.orientation = "row";
            rotateGroup.alignChildren = ["left", "center"];
            var rotateEmpty = rotateGroup.add("statictext", undefined, "");
            rotateEmpty.preferredSize = [24, 24];
            var rotateCheck = rotateGroup.add("checkbox", undefined, "");
            rotateCheck.preferredSize = [74, 24];
            rotateCheck.value = rotateChecks[idx];
            rotateCheck.onClick = (function(i){ return function(){ rotateChecks[i] = this.value; syncHeaderChecks(); }; })(idx);
            rotateChecksUI[idx] = rotateCheck;
            var resizeGroup = row.add("group");
            resizeGroup.orientation = "row";
            resizeGroup.alignChildren = ["left", "center"];
            var resizeEmpty = resizeGroup.add("statictext", undefined, "");
            resizeEmpty.preferredSize = [24, 24];
            var resizeCheck = resizeGroup.add("checkbox", undefined, "");
            resizeCheck.preferredSize = [90, 24];
            resizeCheck.value = resizeChecks[idx];
            resizeCheck.onClick = (function(i){ return function(){ resizeChecks[i] = this.value; syncHeaderChecks(); }; })(idx);
            resizeChecksUI[idx] = resizeCheck;
            var printGroup = row.add("group");
            printGroup.orientation = "row";
            printGroup.alignChildren = ["left", "center"];
            var printCheck = printGroup.add("checkbox", undefined, "");
            printCheck.preferredSize = [100, 24];
            printCheck.value = printChecks[idx];
            printCheck.onClick = (function(i){ return function(){ printChecks[i] = this.value; syncHeaderChecks(); }; })(idx);
            var configBtn = row.add("button", undefined, "打印配置");
            configBtn.preferredSize = [90, 24];
            configBtn.onClick = (function(i){ return function(){
                try {
                    var doc = app.documents.getByName(fileNames[i]);
                    app.activeDocument = doc; // 跳转到该文件
                    openPrintDialog(); // 调用打印界面窗口
                } catch (e) {
                    alert("无法打开文件 '" + fileNames[i] + "': " + e);
                }
            }; })(idx);
            configBtns[idx] = configBtn;
        }
        rowsContainer.layout.layout(true);
        dlg.layout.layout(true);
        dlg.update();
        syncHeaderChecks();
    }
    var totalRows = docCount;
    var maxScrollIndex = Math.max(0, totalRows - visibleRows);
    scrollbar.maxvalue = maxScrollIndex;
    scrollbar.value = 0;
    scrollbar.stepdelta = 7;
    scrollbar.onChanging = function(){
        scrollIndex = Math.round(scrollbar.value);
        renderRows();
    };
    renderRows();
    // 底部操作按钮
    var btnRow = dlg.add("group");
    btnRow.orientation = "row";
    btnRow.alignment = "fill";
    btnRow.spacing = 18;
    var btnPortrait = btnRow.add("button", undefined, "横转竖");
    var btnLandscape = btnRow.add("button", undefined, "竖转横");
    var btnResize = btnRow.add("button", undefined, "调整图像大小");
    var btnPrint = btnRow.add("button", undefined, "打印");
    var btnClose = btnRow.add("button", undefined, "关闭");
    // 横转竖 - 重写版本
    function rotatePortraitSelected() {
        // 获取勾选的文件列表
        var selectedFiles = [];
        for (var i = 0; i < rotateChecks.length; i++) {
            if (rotateChecks[i]) {
                selectedFiles.push(fileNames[i]);
            }
        }
        
        // 检查是否有勾选文件
        if (selectedFiles.length === 0) {
            alert("请先勾选需要旋转的文件！");
            return;
        }
        
        // 只处理勾选的文件
        for (var i = 0; i < selectedFiles.length; i++) {
            try {
                var doc = app.documents.getByName(selectedFiles[i]);
                if (doc.width > doc.height) { // 只旋转横向图片
                    app.activeDocument = doc;
                    app.activeDocument.rotateCanvas(90);
                }
            } catch (e) {}
        }
        
        refreshDocSizeInfo();
        renderRows();
    }
    // 竖转横 - 重写版本
    function rotateLandscapeSelected() {
        // 获取勾选的文件列表
        var selectedFiles = [];
        for (var i = 0; i < rotateChecks.length; i++) {
            if (rotateChecks[i]) {
                selectedFiles.push(fileNames[i]);
            }
        }
        
        // 检查是否有勾选文件
        if (selectedFiles.length === 0) {
            alert("请先勾选需要旋转的文件！");
            return;
        }
        
        // 只处理勾选的文件
        for (var i = 0; i < selectedFiles.length; i++) {
            try {
                var doc = app.documents.getByName(selectedFiles[i]);
                if (doc.height > doc.width) { // 只旋转纵向图片
                    app.activeDocument = doc;
                    app.activeDocument.rotateCanvas(90);
                }
            } catch (e) {}
        }
        
        refreshDocSizeInfo();
        renderRows();
    }
    // 调整图像大小 - 完全重写版本
    function resizeSelectedDialog() {
        var inputDlg = new Window("dialog", "调整图像大小参数输入");
        inputDlg.orientation = "column";
        inputDlg.alignChildren = ["left", "top"];
        var wGroup = inputDlg.add("group");
        wGroup.add("statictext", undefined, "宽度(mm)：");
        var wInput = wGroup.add("edittext", undefined, "210"); // 默认210
        wInput.characters = 8;
        var hGroup = inputDlg.add("group");
        hGroup.add("statictext", undefined, "高度(mm)：");
        var hInput = hGroup.add("edittext", undefined, "297"); // 默认297
        hInput.characters = 8;
        var rGroup = inputDlg.add("group");
        rGroup.add("statictext", undefined, "分辨率(dpi)：");
        var rInput = rGroup.add("edittext", undefined, "300"); // 默认300
        rInput.characters = 8;
        var pGroup = inputDlg.add("group");
        var pCheck = pGroup.add("checkbox", undefined, "按比例调整");
        pCheck.value = true;
        var okBtn = inputDlg.add("button", undefined, "确定");
        var cancelBtn = inputDlg.add("button", undefined, "取消");
        okBtn.onClick = function(){
            var widthVal = parseFloat(wInput.text);
            var heightVal = parseFloat(hInput.text);
            var resVal = parseFloat(rInput.text);
            var keepProportion = pCheck.value;
            if (isNaN(widthVal) || isNaN(heightVal) || widthVal <= 0 || heightVal <= 0) {
                alert("请输入有效的宽度和高度！");
                return;
            }
            if (isNaN(resVal) || resVal <= 0) {
                alert("请输入有效的分辨率！");
                return;
            }
            inputDlg.close();
            
            // 获取勾选的文件列表
            var selectedFiles = [];
            for (var i = 0; i < resizeChecks.length; i++) {
                if (resizeChecks[i]) {
                    selectedFiles.push(fileNames[i]);
                }
            }
            
            // 检查是否有勾选文件
            if (selectedFiles.length === 0) {
                alert("请先勾选需要调整大小的文件！");
                return;
            }
            
            // 直接处理勾选的文件
            var processedCount = 0;
            var originalRulerUnits = app.preferences.rulerUnits;
            var originalDialogMode = app.displayDialogs;
            
            try {
                // 设置为毫米单位并禁用对话框
                app.preferences.rulerUnits = Units.MM;
                app.displayDialogs = DialogModes.NO;
                
                // 只处理勾选的文件
                for (var i = 0; i < selectedFiles.length; i++) {
                    try {
                        // 直接通过名称获取文档
                        var doc = app.documents.getByName(selectedFiles[i]);
                        app.activeDocument = doc;
                        
                        // 直接调整大小，不调用resizeImages函数
                        // 直接使用毫米单位，避免转换误差
                        
                        // 根据是否保持比例决定调整方式
                        if (keepProportion) {
                            var ratio = doc.width / doc.height;
                            if (ratio > 1) { // 横向图片
                                doc.resizeImage(UnitValue(widthVal, "mm"), null, resVal);
                            } else { // 纵向图片
                                doc.resizeImage(null, UnitValue(heightVal, "mm"), resVal);
                            }
                        } else {
                            doc.resizeImage(UnitValue(widthVal, "mm"), UnitValue(heightVal, "mm"), resVal);
                        }
                        
                        processedCount++;
                    } catch (e) {
                        alert("处理文件 '" + selectedFiles[i] + "' 时出错: " + e);
                    }
                }
                
                // 恢复原始设置
                app.preferences.rulerUnits = originalRulerUnits;
                app.displayDialogs = originalDialogMode;
            } catch (e) {
                // 出错时确保恢复设置
                if (typeof originalRulerUnits !== 'undefined') {
                    app.preferences.rulerUnits = originalRulerUnits;
                }
                if (typeof originalDialogMode !== 'undefined') {
                    app.displayDialogs = originalDialogMode;
                }
                alert("调整大小时出错: " + e);
            }
            
            // 刷新文档信息和UI
            refreshDocSizeInfo();
            renderRows();
        }
        cancelBtn.onClick = function(){inputDlg.close();};
        inputDlg.show();
    }
    // 打印 - 重写版本
    function printSelected() {
        // 获取勾选的文件列表
        var selectedFiles = [];
        for (var i = 0; i < printChecks.length; i++) {
            if (printChecks[i]) {
                selectedFiles.push(fileNames[i]);
            }
        }
        
        // 检查是否有勾选文件
        if (selectedFiles.length === 0) {
            alert("请先勾选需要打印的文件！");
            return;
        }
        
        // 只处理勾选的文件
        for (var i = 0; i < selectedFiles.length; i++) {
            try {
                var doc = app.documents.getByName(selectedFiles[i]);
                app.activeDocument = doc;
                printDocument(app.activeDocument);
            } catch (e) {
                alert("打印文件 '" + selectedFiles[i] + "' 时出错");
            }
        }
    }
    // 事件绑定
    btnPortrait.onClick = rotatePortraitSelected;
    btnLandscape.onClick = rotateLandscapeSelected;
    btnResize.onClick = resizeSelectedDialog;
    btnPrint.onClick = printSelected;

    btnClose.onClick = function(){dlg.close();};
    dlg.center();
    dlg.layout.layout(true); // 立即刷新界面，确保底部按钮显示
    dlg.show();
}
// ========== 高级设置弹窗 END ==========

// 直接执行脚本
main();
