// A导出图层.jsx
// 图层导出工具 - 支持多种格式导出图层
// 作者：恒心-perseverance
// 全局变量
var exportOptions = {
    source: "current", // current, all, folder
    format: "jpg", // jpg, png, tif
    exportGroups: false,
    hideHiddenLayers: false,
    cropTransparency: false,
    folderPath: "",
    exportPath: "", // 导出路径
    jpgQuality: 10,
    pngBitDepth: 24,
    pngTransparency: true,
    tifCompression: TIFFEncoding.NONE,
    tifBitDepth: BitsPerChannelType.EIGHT,
    tifTransparency: true
};

// 配置文件路径
var configPath = Folder.userData + "/Adobe/Logs/A恒心Photoshop图层导出选项.json";

// 加载配置文件
function loadConfig() {
    try {
        var configFile = new File(configPath);
        if (configFile.exists) {
            configFile.open("r");
            var configData = configFile.read();
            configFile.close();
            
            var savedOptions = eval("(" + configData + ")");
            for (var key in savedOptions) {
                if (exportOptions.hasOwnProperty(key)) {
                    exportOptions[key] = savedOptions[key];
                }
            }
        }
    } catch (e) {
        // 配置文件加载失败，使用默认设置
    }
}

// 保存配置文件
function saveConfig() {
    try {
        var configFile = new File(configPath);
        var configDir = new Folder(configFile.parent);
        if (!configDir.exists) {
            configDir.create();
        }
        
        // 手动序列化配置对象
        var configStr = "{\n";
        configStr += '    "source": "' + exportOptions.source + '",\n';
        configStr += '    "format": "' + exportOptions.format + '",\n';
        configStr += '    "exportGroups": ' + exportOptions.exportGroups + ',\n';
        configStr += '    "hideHiddenLayers": ' + exportOptions.hideHiddenLayers + ',\n';
        configStr += '    "cropTransparency": ' + exportOptions.cropTransparency + ',\n';
        configStr += '    "folderPath": "' + exportOptions.folderPath.replace(/\\/g, "\\\\") + '",\n';
        configStr += '    "exportPath": "' + exportOptions.exportPath.replace(/\\/g, "\\\\") + '",\n';
        configStr += '    "jpgQuality": ' + exportOptions.jpgQuality + ',\n';
        configStr += '    "pngBitDepth": ' + exportOptions.pngBitDepth + ',\n';
        configStr += '    "pngTransparency": ' + exportOptions.pngTransparency + ',\n';
        configStr += '    "tifCompression": "' + exportOptions.tifCompression + '",\n';
        configStr += '    "tifBitDepth": "' + exportOptions.tifBitDepth + '",\n';
        configStr += '    "tifTransparency": ' + exportOptions.tifTransparency + '\n';
        configStr += "}";
        
        configFile.open("w");
        configFile.write(configStr);
        configFile.close();
    } catch (e) {
        // 配置文件保存失败，忽略错误
    }
}

// 主函数
function main() {
    try {
        // 检查是否有文档打开
        if (app.documents.length === 0) {
            alert("请先打开一个文档！");
            return;
        }

        // 加载配置
        loadConfig();

        // 创建用户界面
        var dialog = createDialog();
        
        // 显示对话框
        if (dialog.show() === 1) {
            // 保存配置
            saveConfig();
            // 用户点击确定，开始导出
            processExport();
        }
    } catch (e) {
        alert("发生错误: " + e);
    }
}

// 创建对话框
function createDialog() {
    var dialog = new Window("dialog", "A导出图层");
    dialog.orientation = "column";
    dialog.alignChildren = ["fill", "top"];
    dialog.spacing = 10;
    dialog.margins = 16;

    // ---- 源文档选择区域 ----
    var sourcePanel = dialog.add("panel", undefined, "处理范围");
    sourcePanel.orientation = "column";
    sourcePanel.alignChildren = ["left", "top"];
    sourcePanel.margins = [10, 15, 10, 10];
    
    // 单选按钮组
    var sourceGroup = sourcePanel.add("group");
    sourceGroup.orientation = "column";
    sourceGroup.alignChildren = ["left", "top"];
    
    var currentDocRadio = sourceGroup.add("radiobutton", undefined, "当前文档");
    currentDocRadio.value = true;
    currentDocRadio.helpTip = "仅处理当前激活的文档";
    
    var allDocsRadio = sourceGroup.add("radiobutton", undefined, "所有打开的文档");
    allDocsRadio.helpTip = "处理所有已打开的文档";
    
    var folderDocsRadio = sourceGroup.add("radiobutton", undefined, "指定文件夹内的文档");
    folderDocsRadio.helpTip = "处理指定文件夹内的所有PSD/PSB文件";
    
    // 文件夹选择组
    var folderGroup = sourcePanel.add("group");
    folderGroup.orientation = "row";
    folderGroup.alignChildren = ["left", "center"];
    folderGroup.enabled = false;
    
    var folderPathText = folderGroup.add("edittext", undefined, "");
    folderPathText.preferredSize.width = 250;
    folderPathText.enabled = false;
    
    var browseButton = folderGroup.add("button", undefined, "浏览...");
    browseButton.preferredSize.width = 80;
    
    // 文件夹选择事件
    folderDocsRadio.onClick = function() {
        folderGroup.enabled = true;
    };
    
    currentDocRadio.onClick = allDocsRadio.onClick = function() {
        folderGroup.enabled = false;
    };
    
    browseButton.onClick = function() {
        var sourceFolder = Folder.selectDialog("选择包含PSD/PSB文件的文件夹");
        if (sourceFolder) {
            folderPathText.text = sourceFolder.fsName;
            exportOptions.folderPath = sourceFolder.fsName;
        }
    };

    // ---- 导出路径选择区域 ----
    var exportPathPanel = dialog.add("panel", undefined, "导出路径");
    exportPathPanel.orientation = "column";
    exportPathPanel.alignChildren = ["left", "top"];
    exportPathPanel.margins = [10, 15, 10, 10];
    
    var exportPathGroup = exportPathPanel.add("group");
    exportPathGroup.orientation = "row";
    exportPathGroup.alignChildren = ["left", "center"];
    
    var exportPathText = exportPathGroup.add("edittext", undefined, exportOptions.exportPath || "");
    exportPathText.preferredSize.width = 250;
    exportPathText.enabled = false;
    exportPathText.helpTip = "选择图层导出的保存路径";
    
    var exportBrowseButton = exportPathGroup.add("button", undefined, "浏览...");
    exportBrowseButton.preferredSize.width = 80;
    
    exportBrowseButton.onClick = function() {
        var targetFolder = Folder.selectDialog("选择图层导出保存路径");
        if (targetFolder) {
            exportPathText.text = targetFolder.fsName;
            exportOptions.exportPath = targetFolder.fsName;
        }
    };

    // ---- 导出格式选择区域 ----
    var formatPanel = dialog.add("panel", undefined, "导出格式");
    formatPanel.orientation = "column";
    formatPanel.alignChildren = ["left", "top"];
    formatPanel.margins = [10, 15, 10, 10];
    
    // 格式单选按钮组
    var formatGroup = formatPanel.add("group");
    formatGroup.orientation = "row";
    formatGroup.alignChildren = ["left", "top"];
    formatGroup.spacing = 20;
    
    var jpgRadio = formatGroup.add("radiobutton", undefined, "JPG");
    jpgRadio.value = true;
    jpgRadio.helpTip = "导出为JPG格式";
    
    var pngRadio = formatGroup.add("radiobutton", undefined, "PNG");
    pngRadio.helpTip = "导出为PNG格式";
    
    var tifRadio = formatGroup.add("radiobutton", undefined, "TIF");
    tifRadio.helpTip = "导出为TIFF格式";
    
    // 格式选项面板（堆栈面板）
    var formatOptionsPanel = formatPanel.add("group");
    formatOptionsPanel.orientation = "stack";
    formatOptionsPanel.alignment = ["fill", "top"];
    
    // JPG选项
    var jpgOptions = formatOptionsPanel.add("group");
    jpgOptions.orientation = "column";
    jpgOptions.alignChildren = ["left", "top"];
    jpgOptions.visible = true;
    
    var jpgQualityGroup = jpgOptions.add("group");
    jpgQualityGroup.add("statictext", undefined, "品质:");
    var jpgQualitySlider = jpgQualityGroup.add("slider", undefined, 10, 0, 12);
    jpgQualitySlider.preferredSize.width = 150;
    var jpgQualityValue = jpgQualityGroup.add("statictext", undefined, "10");
    jpgQualityValue.preferredSize.width = 30;
    
    jpgQualitySlider.onChanging = function() {
        jpgQualityValue.text = Math.round(this.value);
        exportOptions.jpgQuality = Math.round(this.value);
    };
    
    // PNG选项
    var pngOptions = formatOptionsPanel.add("group");
    pngOptions.orientation = "column";
    pngOptions.alignChildren = ["left", "top"];
    pngOptions.visible = false;
    
    var pngBitDepthGroup = pngOptions.add("group");
    pngBitDepthGroup.add("statictext", undefined, "位深度:");
    var png8Radio = pngBitDepthGroup.add("radiobutton", undefined, "8位");
    var png24Radio = pngBitDepthGroup.add("radiobutton", undefined, "24位");
    png24Radio.value = true;
    
    var pngTransparencyCheck = pngOptions.add("checkbox", undefined, "透明背景");
    pngTransparencyCheck.value = true;
    
    png8Radio.onClick = function() {
        exportOptions.pngBitDepth = 8;
    };
    
    png24Radio.onClick = function() {
        exportOptions.pngBitDepth = 24;
    };
    
    pngTransparencyCheck.onClick = function() {
        exportOptions.pngTransparency = this.value;
    };
    
    // TIF选项
    var tifOptions = formatOptionsPanel.add("group");
    tifOptions.orientation = "column";
    tifOptions.alignChildren = ["left", "top"];
    tifOptions.visible = false;
    
    var tifCompressionGroup = tifOptions.add("group");
    tifCompressionGroup.add("statictext", undefined, "压缩方式:");
    var tifCompressionDropdown = tifCompressionGroup.add("dropdownlist", undefined, ["无压缩", "LZW", "ZIP", "JPEG"]);
    tifCompressionDropdown.selection = 0;
    
    var tifBitDepthGroup = tifOptions.add("group");
    tifBitDepthGroup.add("statictext", undefined, "位深度:");
    var tif8Radio = tifBitDepthGroup.add("radiobutton", undefined, "8位");
    var tif16Radio = tifBitDepthGroup.add("radiobutton", undefined, "16位");
    tif8Radio.value = true;
    
    var tifTransparencyCheck = tifOptions.add("checkbox", undefined, "保存透明度");
    tifTransparencyCheck.value = true;
    
    tifCompressionDropdown.onChange = function() {
        switch (this.selection.index) {
            case 0: exportOptions.tifCompression = TIFFEncoding.NONE; break;
            case 1: exportOptions.tifCompression = TIFFEncoding.TIFFLZW; break;
            case 2: exportOptions.tifCompression = TIFFEncoding.TIFFZIP; break;
            case 3: exportOptions.tifCompression = TIFFEncoding.JPEG; break;
        }
    };
    
    tif8Radio.onClick = function() {
        exportOptions.tifBitDepth = BitsPerChannelType.EIGHT;
    };
    
    tif16Radio.onClick = function() {
        exportOptions.tifBitDepth = BitsPerChannelType.SIXTEEN;
    };
    
    tifTransparencyCheck.onClick = function() {
        exportOptions.tifTransparency = this.value;
    };

    // 格式切换事件
    jpgRadio.onClick = function() {
        jpgOptions.visible = true;
        pngOptions.visible = false;
        tifOptions.visible = false;
        exportOptions.format = "jpg";
    };
    
    pngRadio.onClick = function() {
        jpgOptions.visible = false;
        pngOptions.visible = true;
        tifOptions.visible = false;
        exportOptions.format = "png";
    };
    
    tifRadio.onClick = function() {
        jpgOptions.visible = false;
        pngOptions.visible = false;
        tifOptions.visible = true;
        exportOptions.format = "tif";
    };

    // ---- 图层筛选选项 ----
    var layerFilterPanel = dialog.add("panel", undefined, "图层筛选");
    layerFilterPanel.orientation = "column";
    layerFilterPanel.alignChildren = ["left", "top"];
    layerFilterPanel.margins = [10, 15, 10, 10];

    var exportGroupsCheck = layerFilterPanel.add("checkbox", undefined, "按图层组导出（仅导出图层组，不导出单图层）");
    exportGroupsCheck.helpTip = "选中后，将每个最外层组导出为一个文件";

    var hideHiddenLayersCheck = layerFilterPanel.add("checkbox", undefined, "不导出隐藏图层");
    hideHiddenLayersCheck.helpTip = "选中后，将忽略所有隐藏的图层";

    var cropTransparencyCheck = layerFilterPanel.add("checkbox", undefined, "导出前裁剪透明像素");
    cropTransparencyCheck.helpTip = "选中后，将在导出前裁剪掉图层周围的透明区域";

    exportGroupsCheck.onClick = function() {
        exportOptions.exportGroups = this.value;
    };

    hideHiddenLayersCheck.onClick = function() {
        exportOptions.hideHiddenLayers = this.value;
    };

    cropTransparencyCheck.onClick = function() {
        exportOptions.cropTransparency = this.value;
    };

    // ---- 按钮区域 ----
    var buttonGroup = dialog.add("group");
    buttonGroup.orientation = "row";
    buttonGroup.alignChildren = ["center", "center"];
    buttonGroup.alignment = ["center", "top"];

    var cancelButton = buttonGroup.add("button", undefined, "取消", {name: "cancel"});
    var okButton = buttonGroup.add("button", undefined, "确定", {name: "ok"});

    // ---- 作者信息 ----
    var authorGroup = dialog.add("group");
    authorGroup.orientation = "row";
    authorGroup.alignChildren = ["center", "center"];
    authorGroup.alignment = ["center", "bottom"];

    var authorText = authorGroup.add("statictext", undefined, "恒心-perseverance");
    authorText.graphics.foregroundColor = authorText.graphics.newPen(authorText.graphics.PenType.SOLID_COLOR, [1, 0.5, 0], 1);

    // 根据配置设置界面状态
    function updateUIFromConfig() {
        // 设置源文档选项
        switch (exportOptions.source) {
            case "current":
                currentDocRadio.value = true;
                folderGroup.enabled = false;
                break;
            case "all":
                allDocsRadio.value = true;
                folderGroup.enabled = false;
                break;
            case "folder":
                folderDocsRadio.value = true;
                folderGroup.enabled = true;
                break;
        }

        // 设置文件夹路径
        folderPathText.text = exportOptions.folderPath || "";

        // 设置导出路径
        exportPathText.text = exportOptions.exportPath || "";

        // 设置导出格式
        switch (exportOptions.format) {
            case "jpg":
                jpgRadio.value = true;
                jpgOptions.visible = true;
                pngOptions.visible = false;
                tifOptions.visible = false;
                break;
            case "png":
                pngRadio.value = true;
                jpgOptions.visible = false;
                pngOptions.visible = true;
                tifOptions.visible = false;
                break;
            case "tif":
                tifRadio.value = true;
                jpgOptions.visible = false;
                pngOptions.visible = false;
                tifOptions.visible = true;
                break;
        }

        // 设置JPG品质
        jpgQualitySlider.value = exportOptions.jpgQuality;
        jpgQualityValue.text = exportOptions.jpgQuality.toString();

        // 设置PNG选项
        if (exportOptions.pngBitDepth === 8) {
            png8Radio.value = true;
        } else {
            png24Radio.value = true;
        }
        pngTransparencyCheck.value = exportOptions.pngTransparency;

        // 设置TIF选项
        var compressionIndex = 0;
        switch (exportOptions.tifCompression) {
            case TIFFEncoding.NONE: compressionIndex = 0; break;
            case TIFFEncoding.TIFFLZW: compressionIndex = 1; break;
            case TIFFEncoding.TIFFZIP: compressionIndex = 2; break;
            case TIFFEncoding.JPEG: compressionIndex = 3; break;
        }
        tifCompressionDropdown.selection = compressionIndex;

        if (exportOptions.tifBitDepth === BitsPerChannelType.EIGHT) {
            tif8Radio.value = true;
        } else {
            tif16Radio.value = true;
        }
        tifTransparencyCheck.value = exportOptions.tifTransparency;

        // 设置图层筛选选项
        exportGroupsCheck.value = exportOptions.exportGroups;
        hideHiddenLayersCheck.value = exportOptions.hideHiddenLayers;
        cropTransparencyCheck.value = exportOptions.cropTransparency;
    }

    // 初始化界面
    updateUIFromConfig();

    // 获取用户选择
    dialog.onClose = function() {
        // 设置源文档选项
        if (currentDocRadio.value) exportOptions.source = "current";
        else if (allDocsRadio.value) exportOptions.source = "all";
        else if (folderDocsRadio.value) exportOptions.source = "folder";

        // 其他选项已经在各自的onClick事件中设置
    };

    return dialog;
}

// 处理导出
function processExport() {
    try {
        var docs = [];

        // 根据用户选择获取要处理的文档
        switch (exportOptions.source) {
            case "current":
                docs.push(app.activeDocument);
                break;
            case "all":
                for (var i = 0; i < app.documents.length; i++) {
                    docs.push(app.documents[i]);
                }
                break;
            case "folder":
                if (exportOptions.folderPath === "") {
                    alert("请选择一个文件夹！");
                    return;
                }

                var sourceFolder = new Folder(exportOptions.folderPath);
                var files = sourceFolder.getFiles(/(\.psd|\.psb)$/i);

                if (files.length === 0) {
                    alert("所选文件夹中没有找到PSD/PSB文件！");
                    return;
                }

                // 提示用户将打开多个文件
                var proceed = confirm("将打开 " + files.length + " 个文件进行处理。是否继续？");
                if (!proceed) return;

                // 打开文件夹中的所有PSD/PSB文件
                for (var j = 0; j < files.length; j++) {
                    try {
                        var docRef = app.open(files[j]);
                        docs.push(docRef);
                    } catch (e) {
                        alert("无法打开文件 " + files[j].name + ": " + e);
                    }
                }
                break;
        }

        // 检查导出路径
        if (exportOptions.exportPath === "") {
            alert("请选择导出路径！");
            return;
        }

        // 处理每个文档
        for (var k = 0; k < docs.length; k++) {
            var currentDoc = docs[k];
            app.activeDocument = currentDoc;

            // 显示进度
            var progressMsg = "正在处理文档 " + (k + 1) + "/" + docs.length + ": " + currentDoc.name;

            // 创建保存文件夹
            var docName = currentDoc.name.replace(/\.[^\.]+$/, "");
            var saveFolder = new Folder(exportOptions.exportPath + "/" + docName + "_导出");
            if (!saveFolder.exists) {
                saveFolder.create();
            }

            // 导出图层
            exportLayers(currentDoc, saveFolder);

            // 如果是从文件夹打开的，处理完后关闭
            if (exportOptions.source === "folder") {
                currentDoc.close(SaveOptions.DONOTSAVECHANGES);
            }
        }

        alert("导出完成！共处理了 " + docs.length + " 个文档。");
    } catch (e) {
        alert("导出过程中发生错误: " + e);
    }
}

// 导出图层函数
function exportLayers(doc, saveFolder) {
    // 保存当前可见性状态
    var layerVisibility = [];
    for (var i = 0; i < doc.layers.length; i++) {
        layerVisibility.push(doc.layers[i].visible);
    }

    try {
        // 如果选择按图层组导出
        if (exportOptions.exportGroups) {
            for (var j = 0; j < doc.layerSets.length; j++) {
                var layerSet = doc.layerSets[j];

                // 跳过隐藏的图层组（如果选择了不导出隐藏图层）
                if (exportOptions.hideHiddenLayers && !layerSet.visible) continue;

                // 隐藏所有图层
                hideAllLayers(doc);

                // 显示当前图层组
                layerSet.visible = true;

                // 导出图层组
                exportLayer(doc, layerSet, saveFolder);
            }
        } else {
            // 导出所有图层
            for (var k = 0; k < doc.layers.length; k++) {
                var layer = doc.layers[k];

                // 跳过隐藏的图层（如果选择了不导出隐藏图层）
                if (exportOptions.hideHiddenLayers && !layer.visible) continue;

                // 跳过背景图层
                if (layer.isBackgroundLayer) continue;

                // 隐藏所有图层
                hideAllLayers(doc);

                // 显示当前图层
                layer.visible = true;

                // 导出图层
                exportLayer(doc, layer, saveFolder);
            }
        }
    } catch (e) {
        alert("导出图层时发生错误: " + e);
    } finally {
        // 恢复原始可见性状态
        for (var l = 0; l < doc.layers.length; l++) {
            if (l < layerVisibility.length) {
                doc.layers[l].visible = layerVisibility[l];
            }
        }
    }
}

// 隐藏文档中的所有图层
function hideAllLayers(doc) {
    for (var i = 0; i < doc.layers.length; i++) {
        doc.layers[i].visible = false;
    }
}

// 导出单个图层
function exportLayer(doc, layer, saveFolder) {
    try {
        // 复制文档
        var docCopy = doc.duplicate();

        // 如果需要裁剪透明像素
        if (exportOptions.cropTransparency) {
            cropTransparentPixels(docCopy);
        }

        // 构建保存路径
        var savePath = new File(saveFolder + "/" + layer.name);

        // 根据选择的格式导出
        switch (exportOptions.format) {
            case "jpg":
                saveAsJPG(docCopy, savePath);
                break;
            case "png":
                saveAsPNG(docCopy, savePath);
                break;
            case "tif":
                saveAsTIF(docCopy, savePath);
                break;
        }

        // 关闭副本
        docCopy.close(SaveOptions.DONOTSAVECHANGES);
    } catch (e) {
        alert("导出图层 '" + layer.name + "' 时发生错误: " + e);
    }
}

// 裁剪透明像素
function cropTransparentPixels(doc) {
    try {
        // 选择非透明像素
        app.activeDocument = doc;
        doc.selection.selectAll();
        doc.selection.invert();

        // 如果没有选区（即整个图像都是透明的），则返回
        if (doc.selection.bounds[2] - doc.selection.bounds[0] <= 0 ||
            doc.selection.bounds[3] - doc.selection.bounds[1] <= 0) {
            doc.selection.deselect();
            return;
        }

        // 裁剪到选区
        doc.crop(doc.selection.bounds);
        doc.selection.deselect();
    } catch (e) {
        alert("裁剪透明像素时发生错误: " + e);
    }
}

// 保存为JPG
function saveAsJPG(doc, file) {
    var jpgFile = new File(file.toString() + ".jpg");
    var jpgSaveOptions = new JPEGSaveOptions();
    jpgSaveOptions.quality = exportOptions.jpgQuality;
    jpgSaveOptions.embedColorProfile = true;
    jpgSaveOptions.formatOptions = FormatOptions.STANDARDBASELINE;
    jpgSaveOptions.matte = MatteType.NONE;

    doc.saveAs(jpgFile, jpgSaveOptions, true, Extension.LOWERCASE);
}

// 保存为PNG
function saveAsPNG(doc, file) {
    var pngFile = new File(file.toString() + ".png");
    var pngSaveOptions = new PNGSaveOptions();
    pngSaveOptions.interlaced = false;

    // PNG24 透明度设置
    if (exportOptions.pngBitDepth === 24) {
        pngSaveOptions.transparency = exportOptions.pngTransparency;
    }

    doc.saveAs(pngFile, pngSaveOptions, true, Extension.LOWERCASE);
}

// 保存为TIF
function saveAsTIF(doc, file) {
    var tifFile = new File(file.toString() + ".tif");
    var tifSaveOptions = new TiffSaveOptions();
    tifSaveOptions.embedColorProfile = true;
    tifSaveOptions.imageCompression = exportOptions.tifCompression;
    tifSaveOptions.byteOrder = ByteOrder.IBM;
    tifSaveOptions.layerCompression = LayerCompression.RLE;
    tifSaveOptions.saveImagePyramid = false;
    tifSaveOptions.transparency = exportOptions.tifTransparency;

    // 设置位深度
    doc.bitsPerChannel = exportOptions.tifBitDepth;

    doc.saveAs(tifFile, tifSaveOptions, true, Extension.LOWERCASE);
}

// 执行主函数
main();
