/**
 * 智能导出工具 v2.0
 * 计算选中对象的尺寸并按用户参数导出JPEG
 */

// 检查是否有活动文档
if (app.documents.length === 0) {
    alert("错误：请先打开一个文档！");
} else {
    // 检查是否有选中对象
    if (app.activeDocument.selection.length === 0) {
        alert("错误：请选择有效对象！");
    } else {
        smartExportTool();
    }
}

function smartExportTool() {
    try {
        var doc = app.activeDocument;
        var sel = doc.selection;
        
        // 计算选中对象的边界
        var bounds = getSelectionBounds(sel);
        var width_pt = bounds[2] - bounds[0];
        var height_pt = Math.abs(bounds[3] - bounds[1]); // 使用绝对值确保高度为正数
        
        // 转换为毫米 (1pt ≈ 0.3528mm)
        var width_mm = width_pt * 0.3528;
        var height_mm = height_pt * 0.3528;
        
        // 创建对话框
        var dlg = new Window("dialog", "智能导出工具 v2.0 by:恒心-perseverance");
        dlg.orientation = "column";
        dlg.alignChildren = ["fill", "top"];
        
        // 添加尺寸信息面板
        var sizePanel = dlg.add("statictext", undefined, "喷印联盟出品  QQ群:248884685");
        var sizePanel = dlg.add("panel", undefined, "选中对象尺寸");
        sizePanel.orientation = "column";
        sizePanel.alignChildren = ["fill", "top"];
        sizePanel.margins = [10, 15, 10, 10];
        
        // 分别显示宽度和高度信息（毫米和像素）
        var widthInfo = sizePanel.add("statictext", undefined, 
            "宽度: " + width_mm.toFixed(2) + " mm (" + Math.round(width_pt) + " px)"
        );
        widthInfo.characters = 40;
        
        var heightInfo = sizePanel.add("statictext", undefined, 
            "高度: " + height_mm.toFixed(2) + " mm (" + Math.round(height_pt) + " px)"
        );
        heightInfo.characters = 40;
        
        // 导出设置面板
        var settingsPanel = dlg.add("panel", undefined, "导出设置");
        settingsPanel.orientation = "column";
        settingsPanel.alignChildren = ["fill", "top"];
        settingsPanel.margins = [10, 15, 10, 10];
        
        // DPI设置
        var dpiGroup = settingsPanel.add("group");
        dpiGroup.orientation = "row";
        dpiGroup.alignChildren = ["left", "center"];
        dpiGroup.add("statictext", undefined, "DPI:");
        var dpiInput = dpiGroup.add("edittext", undefined, "300");
        dpiInput.characters = 6;
        var dpiSlider = dpiGroup.add("slider", undefined, 300, 72, 1200);
        dpiSlider.preferredSize.width = 150;
        
        // DPI输入和滑块联动
        dpiInput.onChange = function() {
            var value = parseInt(dpiInput.text);
            if (isNaN(value) || value < 72) {
                value = 72;
                dpiInput.text = "72";
            } else if (value > 1200) {
                value = 1200;
                dpiInput.text = "1200";
            }
            dpiSlider.value = value;
            updateExportInfo();
        };
        
        dpiSlider.onChanging = function() {
            dpiInput.text = Math.round(dpiSlider.value).toString();
            updateExportInfo();
        };
        
        // 缩放倍数设置
        var scaleGroup = settingsPanel.add("group");
        scaleGroup.orientation = "row";
        scaleGroup.alignChildren = ["left", "center"];
        scaleGroup.add("statictext", undefined, "缩放倍数:");
        var scaleInput = scaleGroup.add("edittext", undefined, "1.0");
        scaleInput.characters = 6;
        var scaleSlider = scaleGroup.add("slider", undefined, 1.0, 0.1, 10);
        scaleSlider.preferredSize.width = 150;
        
        // 缩放倍数输入和滑块联动
        scaleInput.onChange = function() {
            var value = parseFloat(scaleInput.text);
            if (isNaN(value) || value < 0.1) {
                value = 0.1;
                scaleInput.text = "0.1";
            } else if (value > 100) {
                value = 100;
                scaleInput.text = "100";
            }
            if (value <= 10) {
                scaleSlider.value = value;
            } else {
                scaleSlider.value = 10;
            }
            updateExportInfo();
        };
        
        scaleSlider.onChanging = function() {
            scaleInput.text = scaleSlider.value.toFixed(1);
            updateExportInfo();
        };
        
        // 颜色模式设置
        var colorGroup = settingsPanel.add("group");
        colorGroup.orientation = "row";
        colorGroup.alignChildren = ["left", "center"];
        colorGroup.add("statictext", undefined, "颜色模式:");
        var rgbRadio = colorGroup.add("radiobutton", undefined, "RGB");
        var cmykRadio = colorGroup.add("radiobutton", undefined, "CMYK");
        rgbRadio.value = true; // 默认选择RGB
        
        // 附加选项
        var embedICCCheckbox = settingsPanel.add("checkbox", undefined, "嵌入ICC色彩配置文件");
        embedICCCheckbox.value = true;
        
        // 导出信息预览
        var exportInfoPanel = dlg.add("panel", undefined, "导出信息预览");
        exportInfoPanel.orientation = "column";
        exportInfoPanel.alignChildren = ["fill", "top"];
        exportInfoPanel.margins = [10, 15, 10, 10];
        
        var exportInfo = exportInfoPanel.add("statictext", undefined, "");
        exportInfo.characters = 40;
        
        // 计算并更新导出信息
        function updateExportInfo() {
            var dpi = parseInt(dpiInput.text);
            var scale = parseFloat(scaleInput.text);
            
            // 计算导出尺寸（像素）
            var exportWidth = Math.round(width_pt * scale * dpi / 72);
            var exportHeight = Math.round(height_pt * scale * dpi / 72);
            
            // 检查是否超过最大尺寸限制
            var maxDimension = 30000; // 最大像素限制
            var adjustedDpi = dpi;
            
            if (exportWidth > maxDimension || exportHeight > maxDimension) {
                // 计算需要调整的DPI
                var widthRatio = exportWidth / maxDimension;
                var heightRatio = exportHeight / maxDimension;
                var maxRatio = Math.max(widthRatio, heightRatio);
                
                adjustedDpi = Math.floor(dpi / maxRatio);
                
                // 重新计算调整后的尺寸
                exportWidth = Math.round(width_pt * scale * adjustedDpi / 72);
                exportHeight = Math.round(height_pt * scale * adjustedDpi / 72);
            }
            
            var infoText = "导出尺寸: " + exportWidth + " × " + exportHeight + " 像素\n";
            
            if (adjustedDpi < dpi) {
                infoText += "注意: DPI已自动调整为 " + adjustedDpi + " 以符合最大尺寸限制\n";
            }
            
            var fileSizeMB = (exportWidth * exportHeight * 3 / 1048576).toFixed(2);
            infoText += "预估文件大小: 约 " + fileSizeMB + " MB";
            
            exportInfo.text = infoText;
        }
        
        // 初始化导出信息
        updateExportInfo();
        
        // 按钮组
        var buttonGroup = dlg.add("group");
        buttonGroup.orientation = "row";
        buttonGroup.alignChildren = ["center", "center"];
        
        var cancelButton = buttonGroup.add("button", undefined, "取消");
        var exportButton = buttonGroup.add("button", undefined, "导出", {name: "ok"});
        
        // 设置按钮动作
        cancelButton.onClick = function() {
            dlg.close();
        };
        
        exportButton.onClick = function() {
            dlg.close();
            exportJPEG();
        };
        
        // 显示对话框
        dlg.show();
        
        // 导出JPEG函数
        function exportJPEG() {
            var tempDoc = null;
            try {
                var dpi = parseInt(dpiInput.text);
                var scale = parseFloat(scaleInput.text);
                var isRGB = rgbRadio.value;
                var embedICC = embedICCCheckbox.value;
                
                // 获取选中对象的边界
                var selBounds = getSelectionBounds(sel);
                var selWidth = selBounds[2] - selBounds[0];
                var selHeight = Math.abs(selBounds[3] - selBounds[1]); // 使用绝对值确保高度为正数
                
                // 计算缩放后的尺寸（像素）
                var exportWidth = Math.round(selWidth * scale * dpi / 72);
                var exportHeight = Math.round(selHeight * scale * dpi / 72);
                
                // 直接从当前文档复制
                var oldDoc = app.activeDocument;
                
                // 创建新文档，大小为选中对象的大小
                try {
                    // 方法1：使用预设并设置大小为选中对象大小
                    var newDocumentPreset = new DocumentPreset();
                    newDocumentPreset.width = selWidth;
                    newDocumentPreset.height = selHeight;
                    newDocumentPreset.colorMode = isRGB ? DocumentColorSpace.RGB : DocumentColorSpace.CMYK;
                    newDocumentPreset.units = RulerUnits.Points;
                    newDocumentPreset.resolution = dpi; // 设置文档分辨率
                    newDocumentPreset.title = "TempExport";
                    
                    tempDoc = app.documents.addDocument(newDocumentPreset.colorMode, newDocumentPreset);
                } catch(e1) {
                    try {
                        // 方法2：简化参数
                        tempDoc = app.documents.add(isRGB ? DocumentColorSpace.RGB : DocumentColorSpace.CMYK);
                    } catch(e2) {
                        try {
                            // 方法3：不指定参数
                            tempDoc = app.documents.add();
                        } catch(e3) {
                            // 如果以上方法都失败，使用直接导出当前文档的方式
                            directExport();
                            return;
                        }
                    }
                }
                
                // 复制选中对象到新文档
                app.activeDocument = oldDoc;
                app.copy();
                app.activeDocument = tempDoc;
                app.paste();
                
                // 调整画板大小以精确匹配粘贴的对象
                app.executeMenuCommand('selectall');
                if (tempDoc.selection.length > 0) {
                    var tempBounds = tempDoc.selection[0].visibleBounds;
                    tempDoc.artboards[0].artboardRect = tempBounds;
                }
                
                // 居中对象（确保对象完全在画板内）
                app.executeMenuCommand('selectall');
                for (var i = 0; i < tempDoc.selection.length; i++) {
                    var obj = tempDoc.selection[i];
                    var objBounds = obj.visibleBounds;
                    var objWidth = objBounds[2] - objBounds[0];
                    var objHeight = Math.abs(objBounds[3] - objBounds[1]); // 使用绝对值确保高度为正数
                    
                    // 计算居中位置
                    var deltaX = (tempDoc.artboards[0].artboardRect[2] - tempDoc.artboards[0].artboardRect[0] - objWidth) / 2;
                    var deltaY = (tempDoc.artboards[0].artboardRect[1] - tempDoc.artboards[0].artboardRect[3] - objHeight) / 2;
                    
                    // 移动对象到居中位置
                    obj.position = [tempDoc.artboards[0].artboardRect[0] + deltaX, tempDoc.artboards[0].artboardRect[1] - deltaY];
                }
                
                // 应用缩放
                if (scale != 1.0) {
                    app.executeMenuCommand('selectall');
                    for (var i = 0; i < tempDoc.selection.length; i++) {
                        tempDoc.selection[i].resize(
                            scale * 100, // 水平缩放百分比 
                            scale * 100, // 垂直缩放百分比
                            true, // 缩放线条宽度
                            true, // 缩放填充图案
                            true, // 缩放渐变
                            true, // 缩放文本
                            0,    // 基准点 - 中心
                            Transformation.DOCUMENTORIGIN
                        );
                    }
                    
                    // 重新调整画板大小以精确匹配缩放后的对象
                    app.executeMenuCommand('selectall');
                    if (tempDoc.selection.length > 0) {
                        var scaledBounds = tempDoc.selection[0].visibleBounds;
                        tempDoc.artboards[0].artboardRect = scaledBounds;
                    }
                }
                
                // 获取保存路径
                var saveFile = File.saveDialog("保存JPEG文件", "JPEG:*.jpg");
                
                if (saveFile) {
                    // 确保文件扩展名为.jpg
                    if (!saveFile.name.match(/\.jpe?g$/i)) {
                        saveFile = new File(saveFile.absoluteURI + ".jpg");
                    }
                    
                    // 创建导出选项 - 修复参数
                    var exportOptions = new ExportOptionsJPEG();
                    exportOptions.antiAliasing = true;
                    exportOptions.artBoardClipping = true;  // 确保只导出画板内容
                    exportOptions.qualitySetting = 100;
                    exportOptions.resolution = dpi; // 确保正确设置DPI
                    exportOptions.embedICCProfile = embedICC;
                    
                    // 导出文件
                    tempDoc.exportFile(saveFile, ExportType.JPEG, exportOptions);
                    
                    // 关闭临时文档而不保存
                    tempDoc.close(SaveOptions.DONOTSAVECHANGES);
                    
                    alert("导出成功!\n文件已保存至:\n" + decodeURI(saveFile.fsName));
                } else {
                    // 用户取消，关闭临时文档
                    tempDoc.close(SaveOptions.DONOTSAVECHANGES);
                }
            } catch (e) {
                alert("导出过程中发生错误:\n" + e.toString());
                // 确保临时文档被关闭
                if (tempDoc && tempDoc.typename === "Document") {
                    try {
                        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
                    } catch(e4) {
                        // 忽略关闭错误
                    }
                }
                
                // 如果仍然出错，尝试直接导出
                directExport();
            }
        }
        
        // 直接导出方法，不创建临时文档
        function directExport() {
            try {
                var dpi = parseInt(dpiInput.text);
                var scale = parseFloat(scaleInput.text);
                var embedICC = embedICCCheckbox.value;
                
                // 获取保存路径
                var saveFile = File.saveDialog("保存JPEG文件", "JPEG:*.jpg");
                
                if (saveFile) {
                    // 确保文件扩展名为.jpg
                    if (!saveFile.name.match(/\.jpe?g$/i)) {
                        saveFile = new File(saveFile.absoluteURI + ".jpg");
                    }
                    
                    // 创建导出选项
                    var exportOptions = new ExportOptionsJPEG();
                    exportOptions.antiAliasing = true;
                    exportOptions.artBoardClipping = true;
                    exportOptions.qualitySetting = 100;
                    exportOptions.resolution = dpi;  // 确保正确设置DPI
                    exportOptions.embedICCProfile = embedICC;
                    
                    // 修改当前文档的第一个画板大小以匹配选中对象
                    var originalArtboardRect = doc.artboards[0].artboardRect;
                    var selBounds = getSelectionBounds(sel);
                    
                    // 应用缩放
                    var center = [
                        (selBounds[0] + selBounds[2]) / 2,
                        (selBounds[1] + selBounds[3]) / 2
                    ];
                    
                    var scaledBounds = [
                        center[0] - (center[0] - selBounds[0]) * scale,
                        center[1] - (center[1] - selBounds[1]) * scale,
                        center[0] + (selBounds[2] - center[0]) * scale,
                        center[1] + (selBounds[3] - center[1]) * scale
                    ];
                    
                    // 保存原画板大小，设置新画板大小为选中对象大小
                    doc.artboards[0].artboardRect = scaledBounds;
                    
                    // 直接从当前文档导出
                    doc.exportFile(saveFile, ExportType.JPEG, exportOptions);
                    
                    // 恢复原画板大小
                    doc.artboards[0].artboardRect = originalArtboardRect;
                    
                    alert("导出成功!\n文件已保存至:\n" + decodeURI(saveFile.fsName));
                }
            } catch (e) {
                alert("直接导出也失败:\n" + e.toString() + "\n\n请尝试手动导出。");
                useSimplestExport();
            }
        }
        
        // 最简单的导出方式
        function useSimplestExport() {
            try {
                var saveFile = File.saveDialog("保存JPEG文件", "JPEG:*.jpg");
                
                if (saveFile) {
                    // 确保文件扩展名为.jpg
                    if (!saveFile.name.match(/\.jpe?g$/i)) {
                        saveFile = new File(saveFile.absoluteURI + ".jpg");
                    }
                    
                    // 修改当前文档的第一个画板大小以匹配选中对象
                    var originalArtboardRect = doc.artboards[0].artboardRect;
                    var selBounds = getSelectionBounds(sel);
                    
                    // 保存原画板大小，设置新画板大小为选中对象大小
                    doc.artboards[0].artboardRect = selBounds;
                    
                    // 创建基本导出选项
                    var basicExportOptions = new ExportOptionsJPEG();
                    basicExportOptions.resolution = parseInt(dpiInput.text); // 尝试再次确保DPI设置
                    
                    // 不带参数导出
                    doc.exportFile(saveFile, ExportType.JPEG, basicExportOptions);
                    
                    // 恢复原画板大小
                    doc.artboards[0].artboardRect = originalArtboardRect;
                    
                    alert("使用默认设置导出成功!\n文件已保存至:\n" + decodeURI(saveFile.fsName));
                }
            } catch (e) {
                alert("所有导出方式都失败。错误:\n" + e.toString() + "\n\n请尝试手动导出。");
            }
        }
    } catch (e) {
        alert("错误: " + e.toString());
    }
}

function getSelectionBounds(selection) {
    var bounds = [Infinity, Infinity, -Infinity, -Infinity]; // [left, top, right, bottom]
    
    for (var i = 0; i < selection.length; i++) {
        var objBounds = selection[i].geometricBounds;
        bounds[0] = Math.min(bounds[0], objBounds[0]); // left
        bounds[1] = Math.min(bounds[1], objBounds[1]); // top
        bounds[2] = Math.max(bounds[2], objBounds[2]); // right
        bounds[3] = Math.max(bounds[3], objBounds[3]); // bottom
    }
    
    return bounds;
}