// 兼容 ExtendScript 的 ScriptUI，高级设置弹窗，带滚动条和多控件列
function showAdvancedSettingsDialog() {
    // 动态获取当前打开的文档名
    var fileNames = [];
    if (app.documents.length === 0) {
        alert("没有打开的文档！");
        return;
    }
    var widthArr = [], heightArr = [], resArr = [];
    function refreshDocSizeInfo() {
        widthArr = [];
        heightArr = [];
        resArr = [];
        fileNames = [];
        var docCount = app.documents.length;
        if (docCount === 0) return;
        var activeDoc = app.activeDocument;
        fileNames.push(activeDoc.name);
        widthArr.push(Math.round(activeDoc.width.as('mm')));
        heightArr.push(Math.round(activeDoc.height.as('mm')));
        resArr.push(Math.round(activeDoc.resolution));
        for (var i = 0; i < docCount; i++) {
            var doc = app.documents[i];
            if (doc !== activeDoc) {
                fileNames.push(doc.name);
                widthArr.push(Math.round(doc.width.as('mm')));
                heightArr.push(Math.round(doc.height.as('mm')));
                resArr.push(Math.round(doc.resolution));
            }
        }
    }
    refreshDocSizeInfo();
    var docCount = fileNames.length;
    // 勾选状态数组
    var rotateChecks = [];
    var resizeChecks = [];
    var printChecks = [];
    for (var i = 0; i < docCount; i++) {
        rotateChecks.push(false);
        resizeChecks.push(false);
        printChecks.push(false);
    }
    // 主对话框
    var dlg = new Window("dialog", "高级设置", undefined, {resizeable:true});
    dlg.orientation = "column";
    dlg.alignChildren = ["fill", "top"];
    dlg.spacing = 10;
    dlg.margins = 16;
    // 文件列表区域（唯一标准结构）
    // 用listWrap包裹列表和滚动条
    var listWrap = dlg.add("group");
    listWrap.orientation = "row";
    listWrap.alignChildren = ["left", "top"];
    var listPanel = listWrap.add("panel");
    listPanel.text = "文件列表";
    listPanel.orientation = "row";
    listPanel.alignChildren = ["left", "top"];
    listPanel.margins = [10,10,10,10];
    listPanel.preferredSize = [900, 380];
    // 左侧：纵向group，顶部headerRow，下面rowsContainer
    var listCol = listPanel.add("group");
    listCol.orientation = "column";
    listCol.alignChildren = ["fill", "top"];
    // 1. 标题栏    // 文件列表头部
    var headerRow = listCol.add("group");
    headerRow.orientation = "row";
    headerRow.alignChildren = ["left", "center"];
    headerRow.spacing = 4;
    var headerFileLabel = headerRow.add("statictext", undefined, "文件名");
    headerFileLabel.preferredSize = [180, 24];
    headerFileLabel.justify = "left";
    // 新增：宽度、高度、分辨率、方向列头
    var headerWidth = headerRow.add("statictext", undefined, "宽度");
    headerWidth.preferredSize = [70, 24];
    var headerHeight = headerRow.add("statictext", undefined, "高度");
    headerHeight.preferredSize = [70, 24];
    var headerRes = headerRow.add("statictext", undefined, "分辨率");
    headerRes.preferredSize = [70, 24];
    var headerDirection = headerRow.add("statictext", undefined, "方向");
    headerDirection.preferredSize = [50, 24];
    // 旋转列头
    var headerRotateGroup = headerRow.add("group");
    headerRotateGroup.orientation = "row";
    headerRotateGroup.alignChildren = ["left", "center"];
    var headerRotateLabel = headerRotateGroup.add("statictext", undefined, "旋转");
    headerRotateLabel.preferredSize = [24, 24];
    var headerRotateCheck = headerRotateGroup.add("checkbox", undefined, "");
    headerRotateCheck.preferredSize = [50, 10];
    // 调整图像大小列头
    var headerResizeGroup = headerRow.add("group");
    headerResizeGroup.orientation = "row";
    headerResizeGroup.alignChildren = ["left", "center"];
    var headerResizeLabel = headerResizeGroup.add("statictext", undefined, "调整大小");
    headerResizeLabel.preferredSize = [48, 24];
    var headerResizeCheck = headerResizeGroup.add("checkbox", undefined, "");
    headerResizeCheck.preferredSize = [48, 18];
    // 打印列头
    var headerPrintGroup = headerRow.add("group");
    headerPrintGroup.orientation = "row";
    headerPrintGroup.alignChildren = ["left", "center"];
    var headerPrintLabel = headerPrintGroup.add("statictext", undefined, "打印");
    headerPrintLabel.preferredSize = [32, 24];
    var headerPrintCheck = headerPrintGroup.add("checkbox", undefined, "");
    headerPrintCheck.preferredSize = [18, 18];
    // 全选逻辑
    headerRotateCheck.onClick = function() {
        for (var i = 0; i < docCount; i++) rotateChecks[i] = headerRotateCheck.value ? true : false;
        renderRows();
    };
    headerResizeCheck.onClick = function() {
        for (var i = 0; i < docCount; i++) resizeChecks[i] = headerResizeCheck.value ? true : false;
        renderRows();
    };
    headerPrintCheck.onClick = function() {
        for (var i = 0; i < docCount; i++) printChecks[i] = headerPrintCheck.value ? true : false;
        renderRows();
    };
    // 同步表头checkbox状态
    function syncHeaderChecks() {
        // 旋转
        if (headerRotateCheck && rotateChecks && rotateChecks.length) {
            var allChecked = true;
            for (var i = 0; i < rotateChecks.length; i++) {
                if (!rotateChecks[i]) { allChecked = false; break; }
            }
            headerRotateCheck.value = allChecked;
        }
        // 调整图像大小
        if (headerResizeCheck && resizeChecks && resizeChecks.length) {
            var allChecked2 = true;
            for (var i = 0; i < resizeChecks.length; i++) {
                if (!resizeChecks[i]) { allChecked2 = false; break; }
            }
            headerResizeCheck.value = allChecked2;
        }
        // 打印
        if (headerPrintCheck && printChecks && printChecks.length) {
            var allChecked3 = true;
            for (var i = 0; i < printChecks.length; i++) {
                if (!printChecks[i]) { allChecked3 = false; break; }
            }
            headerPrintCheck.value = allChecked3;
        }
    }
    var configTitle = headerRow.add("statictext", undefined, "");
    configTitle.preferredSize = [90, 24];
    // 2. 内容行区 rowsContainer（纵向）
    var rowsContainer = listCol.add("group");
    rowsContainer.orientation = "column";
    rowsContainer.alignChildren = ["fill", "top"];
    rowsContainer.preferredSize = [760, -1]; // 宽度760，高度自适应
    rowsContainer.alignment = 'left';
    // 右侧滚动条
    var scrollbar = listWrap.add('scrollbar', undefined, 'vertical');
    scrollbar.size = [24, 335];
    // 虚拟渲染参数
    var rowHeight = 32; // 适当减小行高提升兼容性
    var visibleRows = Math.min(docCount, 8); // 最多显示8行，文档少则全部显示
    var scrollIndex = 0;
    // 行控件数组
    var rotateChecksUI = [], resizeChecksUI = [], configBtns = [];
    // 渲染可见行的函数（唯一rowsContainer）
    function renderRows() {
        // 清空rowsContainer
        while (rowsContainer.children.length > 0) rowsContainer.remove(rowsContainer.children[0]);

        for (var i = 0; i < visibleRows && (scrollIndex + i) < docCount; i++) {
            var idx = scrollIndex + i;
            var row = rowsContainer.add("group");
            row.orientation = "row";
            row.alignChildren = ["left", "center"];
            row.spacing = 4;
            // 文件名列
            var fileLabel = row.add("statictext", undefined, fileNames[idx]);
            fileLabel.preferredSize = [180, 24];
            fileLabel.justify = "left";
            // 新增：宽度、高度、分辨率、方向列
            var dirVal = "--", dirColor = null;
            if (widthArr[idx] !== undefined && heightArr[idx] !== undefined) {
                if (widthArr[idx] > heightArr[idx]) {
                    dirVal = "横";
                    dirColor = [0, 180, 0]; // 绿色
                } else if (heightArr[idx] > widthArr[idx]) {
                    dirVal = "竖";
                    dirColor = [255, 140, 0]; // 橙色
                } else if (widthArr[idx] === heightArr[idx]) {
                    dirVal = "相等";
                    dirColor = [255, 0, 0]; // 红色
                }
            }
            var widthText = row.add("statictext", undefined, widthArr[idx] !== undefined ? (widthArr[idx] + " mm") : "--");
            widthText.preferredSize = [70, 24];
            var heightText = row.add("statictext", undefined, heightArr[idx] !== undefined ? (heightArr[idx] + " mm") : "--");
            heightText.preferredSize = [70, 24];
            // 设置宽高颜色
            if (dirColor) {
                try {
                    widthText.graphics.foregroundColor = widthText.graphics.newPen(widthText.graphics.PenType.SOLID_COLOR, [dirColor[0]/255, dirColor[1]/255, dirColor[2]/255], 1);
                    heightText.graphics.foregroundColor = heightText.graphics.newPen(heightText.graphics.PenType.SOLID_COLOR, [dirColor[0]/255, dirColor[1]/255, dirColor[2]/255], 1);
                } catch(e) {}
            }
            var resText = row.add("statictext", undefined, resArr[idx] !== undefined ? resArr[idx] : "--");
            resText.preferredSize = [70, 24];
            // 方向列
            var dirText = row.add("statictext", undefined, dirVal);
            dirText.preferredSize = [50, 24];
            // 设置方向颜色
            if (dirColor) {
                try {
                    dirText.graphics.foregroundColor = dirText.graphics.newPen(dirText.graphics.PenType.SOLID_COLOR, [dirColor[0]/255, dirColor[1]/255, dirColor[2]/255], 1);
                } catch(e) {}
            }
            // 旋转列（group：空statictext+checkbox）
            var rotateGroup = row.add("group");
            rotateGroup.orientation = "row";
            rotateGroup.alignChildren = ["left", "center"];
            var rotateEmpty = rotateGroup.add("statictext", undefined, "");
            rotateEmpty.preferredSize = [24, 24];
            var rotateCheck = rotateGroup.add("checkbox", undefined, "");
            rotateCheck.preferredSize = [74, 24];
            rotateCheck.value = rotateChecks[idx]; // 始终用数据源赋值
            rotateCheck.onClick = (function(i){ return function(){ rotateChecks[i] = this.value; syncHeaderChecks(); }; })(idx);
            rotateChecksUI[idx] = rotateCheck; // 只保存引用
            // 调整图像大小列（group：空statictext+checkbox）
            var resizeGroup = row.add("group");
            resizeGroup.orientation = "row";
            resizeGroup.alignChildren = ["left", "center"];
            var resizeEmpty = resizeGroup.add("statictext", undefined, "");
            resizeEmpty.preferredSize = [24, 24];
            var resizeCheck = resizeGroup.add("checkbox", undefined, "");
            resizeCheck.preferredSize = [90, 24];
            resizeCheck.value = resizeChecks[idx]; // 始终用数据源赋值
            resizeCheck.onClick = (function(i){ return function(){ resizeChecks[i] = this.value; syncHeaderChecks(); }; })(idx);
            resizeChecksUI[idx] = resizeCheck; // 只保存引用
            // 打印列（勾选框）
            var printGroup = row.add("group");
            printGroup.orientation = "row";
            printGroup.alignChildren = ["left", "center"];
            var printCheck = printGroup.add("checkbox", undefined, "");
            printCheck.preferredSize = [100, 24];
            printCheck.value = printChecks[idx]; // 始终用数据源赋值
            printCheck.onClick = (function(i){ return function(){ printChecks[i] = this.value; syncHeaderChecks(); }; })(idx);
            // 打印配置按钮列
            var configBtn = row.add("button", undefined, "打印配置");
            configBtn.preferredSize = [90, 24];
            configBtn.onClick = (function(i){ return function(){ alert("打开打印配置："+fileNames[i]); }; })(idx);
            configBtns[idx] = configBtn;
        }
        rowsContainer.layout.layout(true);
        dlg.layout.layout(true);
        dlg.update();
        syncHeaderChecks(); // 渲染后同步表头checkbox
    }
    // 滚动条逻辑
    var totalRows = docCount;
    var maxScrollIndex = Math.max(0, totalRows - visibleRows);
    scrollbar.maxvalue = maxScrollIndex;
    scrollbar.value = 0;
    scrollbar.stepdelta = 1;
    scrollbar.onChanging = function(){
        scrollIndex = Math.round(scrollbar.value);
        renderRows();
    };
    renderRows();

    // 全选/全不选逻辑
    headerRotateCheck.onClick = function(){
        for(var i=0;i<docCount;i++){
            rotateChecksUI[i].value = headerRotateCheck.value;
        }
    };
    headerResizeCheck.onClick = function(){
        alert("横转竖: " + rotateChecksUI.map(function(c,i){return c.value?fileNames[i]:null;}).filter(Boolean).join(", "));
    };
    // 按钮区
    var buttonRow = dlg.add('group');
    buttonRow.orientation = 'row';
    buttonRow.alignChildren = ['left', 'center'];
    // 竖转横
    var btnLandscape = buttonRow.add('button', undefined, '竖转横');
    btnLandscape.preferredSize = [80, 32];
    // 横转竖
    var btnPortrait = buttonRow.add('button', undefined, '横转竖');
    btnPortrait.preferredSize = [80, 32];
    // 调整图像大小
    var btnResize = buttonRow.add('button', undefined, '调整图像大小');
    btnResize.preferredSize = [120, 32];
    // 打印
    var btnPrint = buttonRow.add('button', undefined, '【 打 印 所 选 】');
    btnPrint.preferredSize = [150, 32];
    // 关闭按钮
    var btnClose = buttonRow.add('button', undefined, '关闭界面');
    btnClose.preferredSize = [150, 32];
    btnClose.alignment = ['left', 'center'];

    // 添加完按钮后强制刷新界面，确保按钮立即可见
    dlg.layout.layout(true);
    dlg.update();

    btnLandscape.onClick = function(){
        // 用数据源数组判断勾选
        var selected = [];
        for(var i=0;i<rotateChecks.length;i++){
            if(rotateChecks[i]) selected.push(fileNames[i]);
        }
        alert(selected.length ? selected.join(", ") : "未选择任何文件");
    };
    btnPortrait.onClick = function(){
        var selected = [];
        for(var i=0;i<rotateChecks.length;i++){
            if(rotateChecks[i]) selected.push(fileNames[i]);
        }
        alert(selected.length ? selected.join(", ") : "未选择任何文件");
    };
    btnResize.onClick = function(){
        var selected = [];
        for(var i=0;i<resizeChecks.length;i++){
            if(resizeChecks[i]) selected.push(fileNames[i]);
        }
        alert(selected.length ? selected.join(", ") : "未选择任何文件");
        // 刷新宽高分辨率并重绘表格
        refreshDocSizeInfo();
        renderRows();
    };

    btnPrint.onClick = function(){
        // 只操作勾选了“打印”列的文件
        var selected = [];
        for(var i=0;i<printChecks.length;i++){
            if(printChecks[i]) selected.push(fileNames[i]);
        }
        alert("打印: " + (selected.length ? selected.join(", ") : "未选择任何文件"));
    };
    btnClose.onClick = function(){
        dlg.close();
    };
    dlg.center();
    dlg.show();
}


showAdvancedSettingsDialog(); // 直接运行弹出最终兼容版界面
