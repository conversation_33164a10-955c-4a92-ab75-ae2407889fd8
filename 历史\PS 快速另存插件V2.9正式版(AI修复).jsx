// --- 配置和常量 ---
var SCRIPT_TITLE = "快速另存脚本 v2.9 (重构版)";
var AUTHOR_INFO = "脚本作者：朋亿广告  微信/QQ:156886680   QQ群:248884685";
var INFO_TEXT = "PS 脚本 | CDR 插件 | 图文 | 广告 | 喷印";
var DEFAULT_JPG_QUALITY = 12;
var PANEL_WIDTH = 420;
var INPUT_WIDTH = 260; // 用于文件名备注输入框
var LOCATION_INPUT_WIDTH = 270; // 用于路径输入框
var DROPDOWN_WIDTH = 200; // 用于文件操作下拉框
var COMPRESSION_DROPDOWN_WIDTH = 62; // 用于 TIF 压缩下拉框

// --- 主函数 ---
function main() {
  // 检查是否有文档打开
  if (app.documents.length === 0) {
    alert("错误：没有打开的文档！");
    return;
  }
  // 创建并显示 UI
  var dialog = buildUI();
  if (dialog.show() === 1) { // 1 表示点击了 OK 按钮
    processSave(dialog.settings);
  }
}

// --- UI 构建函数 ---
function buildUI() {
  // -- 主窗口 --
  var dlg = new Window("dialog", SCRIPT_TITLE);
  dlg.orientation = "column";
  dlg.alignChildren = ["fill", "top"]; // 填充宽度，顶部对齐
  
  // -- 标题和信息 --
  addStaticText(dlg, "【 喷印联盟QQ群:248884685出品 】免费公开版", true, 16, [0.7, 0.7, 0.7]);
  addHelpTip(dlg.children[dlg.children.length - 1], "此脚本2018.3月15日第1版  作者:朋亿 2O.4.27V2.0，2.3.6正式版，增加自定义文件名，2O.6.1 2q.5正式版   喷印联盟QQ群:248884685 图文广告同行交流群，2025.3.26 更新2q.9 修正文件名及增加前缀");
  addSeparator(dlg);
  
  // -- 保存地址 --
  var saveGroup = addGroup(dlg, "fill", "row");
  addStaticText(saveGroup, "保存地址:");
  var saveLocationInput = addEditText(saveGroup, "", LOCATION_INPUT_WIDTH);
  var browseBtn = addButton(saveGroup, "浏  览");
  browseBtn.onClick = function () {
    var folder = Folder.selectDialog("选择保存地址");
    if (folder) {
      saveLocationInput.text = folder.fsName;
    }
  };
  
  // -- 文件操作 --
  var opGroup = addGroup(dlg, "left", "row");
  addStaticText(opGroup, "文件操作:");
  var fileOps = [
    "※ 另存当前文件",
    "   ★ 另存并关闭当前文件",
    "※ 另存全部文件",
    "   ★ 另存全部文件并关闭",
  ];
  var fileOpDropdown = addDropdown(opGroup, fileOps, 0, DROPDOWN_WIDTH);
  addStaticText(opGroup, ""); // Spacer
  addStaticText(opGroup, "<-----  注意选择");
  
  // -- 文件名前缀 --
  var prefixGroup = addGroup(dlg, "left", "row");
  var prefixCheckbox = addCheckbox(prefixGroup, "自定义或前缀备注：");
  var prefixInput = addEditText(prefixGroup, "", INPUT_WIDTH);
  prefixInput.enabled = false;
  prefixCheckbox.onClick = function () {
    prefixInput.enabled = prefixCheckbox.value;
  };
  
  // -- 文件名选项 --
  var dimensionsCheckbox = addCheckbox(dlg, "将文件尺寸加入文件名");
  dimensionsCheckbox.alignment = ["left", "center"];
  
  // -- 格式选择面板 --
  var formatPanel = addPanel(dlg, "选择保存文件格式（可多选）", "center", PANEL_WIDTH);
  
  // -- JPG 设置 --
  var jpgSettings = addFormatPanel(formatPanel, "JPG", "JPG 保存设置");
  var jpgQualitySlider = jpgSettings.group.add(
    "slider", undefined, DEFAULT_JPG_QUALITY, 1, 12
  );
  jpgQualitySlider.preferredSize.width = 100;
  var jpgQualityLabel = addStaticText(jpgSettings.group, DEFAULT_JPG_QUALITY.toString());
  addStaticText(jpgSettings.group, " 质量:");
  jpgQualitySlider.onChanging = function () {
    jpgQualityLabel.text = Math.round(this.value).toString();
  };
  
  // -- TIF 设置 --
  var tifSettings = addFormatPanel(formatPanel, "TIF", "TIF 保存设置");
  addStaticText(tifSettings.group, "选项:");
  var tifMergeCheckbox = addCheckbox(tifSettings.group, "合并图层", true); // 默认合并
  var tifCompressionOptions = ["无", "LZW", "ZIP", "JPEG"];
  var tifCompressionDropdown = addDropdown(tifSettings.group, tifCompressionOptions, 0, COMPRESSION_DROPDOWN_WIDTH);
  addStaticText(tifSettings.group, "图像压缩");
  
  // -- PSD 设置 --
  var psdSettings = addFormatPanel(formatPanel, "PSD", "PSD 保存设置");
  addStaticText(psdSettings.group, "选项:");
  var psdCompatibilityCheckbox = addCheckbox(psdSettings.group, "最大兼容性", true);
  
  // -- PNG 设置 --
  var pngSettings = addFormatPanel(formatPanel, "PNG", "PNG 保存设置");
  addStaticText(pngSettings.group, "选项:");
  var pngInterlaceCheckbox = addCheckbox(pngSettings.group, "交错", false);
  
  // -- 文件名后缀 --
  var suffixGroup = addGroup(dlg, "left", "row");
  var suffixCheckbox = addCheckbox(suffixGroup, "后缀备注：");
  var suffixInput = addEditText(suffixGroup, "", INPUT_WIDTH);
  suffixInput.enabled = false;
  suffixCheckbox.onClick = function () {
    suffixInput.enabled = suffixCheckbox.value;
  };
  
  // -- 底部按钮和信息 --
  addSeparator(dlg, [0.3, 0.3, 0.3]);
  var bottomButtonGroup = addGroup(dlg, "center", "row");
  var okBtn = addButton(bottomButtonGroup, "确 定");
  var cancelBtn = addButton(bottomButtonGroup, "取 消");
  addStaticText(dlg, AUTHOR_INFO, true, null, [0.7, 0.7, 0.7]);
  addStaticText(dlg, INFO_TEXT, true, null, [0.7, 0.7, 0.7]);
  
  // -- 按钮事件 --
  okBtn.onClick = function () {
    // 将所有设置收集到一个对象中
    dlg.settings = {
      savePath: saveLocationInput.text,
      operation: fileOpDropdown.selection.text,
      usePrefix: prefixCheckbox.value,
      prefixText: prefixInput.text,
      addDimensions: dimensionsCheckbox.value,
      formats: [],
      jpg: {
        save: jpgSettings.checkbox.value,
        quality: Math.round(jpgQualitySlider.value)
      },
      tif: {
        save: tifSettings.checkbox.value,
        mergeLayers: tifMergeCheckbox.value,
        compression: tifCompressionDropdown.selection.text
      },
      psd: {
        save: psdSettings.checkbox.value,
        compatibility: psdCompatibilityCheckbox.value
      },
      png: {
        save: pngSettings.checkbox.value,
        interlace: pngInterlaceCheckbox.value
      },
      useSuffix: suffixCheckbox.value,
      suffixText: suffixInput.text
    };
    
    // 收集选中的格式
    if (dlg.settings.jpg.save) dlg.settings.formats.push("JPG");
    if (dlg.settings.tif.save) dlg.settings.formats.push("TIF");
    if (dlg.settings.psd.save) dlg.settings.formats.push("PSD");
    if (dlg.settings.png.save) dlg.settings.formats.push("PNG");
    
    dlg.close(1); // 关闭对话框并返回 1
  };
  
  cancelBtn.onClick = function () {
    dlg.close(2); // 关闭对话框并返回 2
  };
  
  dlg.center();
  return dlg;
}

// --- UI 辅助函数 ---
function addStaticText(parent, text, center, fontSize, color) {
  var st = parent.add("statictext", undefined, text);
  // 修复：检查 st 是否有效，并且只有在 center 为 true 时才设置 alignment
  if (st && center) {
    st.alignment = ["center", "center"]; // 修改为数组形式，更安全
  }
  if (st && fontSize) {
    try {
      st.graphics.font = ScriptUI.newFont("SimHei", "REGULAR", fontSize);
    } catch(e) {
      // 如果黑体不可用，使用默认字体
      try {
        st.graphics.font = ScriptUI.newFont("Dialog", "REGULAR", fontSize);
      } catch(e2) {
        $.writeln("Error setting font: " + e2);
      }
    }
  }
  if (st && color) {
    try {
      st.graphics.foregroundColor = st.graphics.newPen(ScriptUI.PenType.SOLID_COLOR, color, 1);
    } catch(e) {
      $.writeln("Error setting text color: " + e);
    }
  }
  return st;
}

function addEditText(parent, text, width) {
  var et = parent.add("edittext", undefined, text);
  if (et && width) et.preferredSize.width = width;
  if (et) et.preferredSize.height = 20;
  return et;
}

function addButton(parent, text) {
  return parent.add("button", undefined, text);
}

function addCheckbox(parent, text, value) {
  var cb = parent.add("checkbox", undefined, text);
  if (cb && value !== undefined) cb.value = value;
  return cb;
}

function addDropdown(parent, items, selectedIndex, width) {
  var dd = parent.add("dropdownlist", undefined, items);
  if (dd) {
    if (selectedIndex !== undefined && dd.items.length > selectedIndex) {
      dd.selection = selectedIndex;
    }
    if (width) dd.preferredSize.width = width;
  }
  return dd;
}

function addGroup(parent, alignment, orientation) {
  var grp = parent.add("group");
  if (grp) {
    if (alignment) grp.alignment = alignment;
    if (orientation) grp.orientation = orientation;
  }
  return grp;
}

function addPanel(parent, text, alignment, width) {
  var pnl = parent.add("panel", undefined, text);
  if (pnl) {
    if (alignment) pnl.alignment = alignment;
    if (width) pnl.preferredSize.width = width;
  }
  return pnl;
}

function addSeparator(parent, color) {
  var sep = parent.add(
    "statictext",
    undefined,
    "- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -"
  );
  if (sep) {
    sep.alignment = ["center", "center"]; // 修改为数组形式
    if (color) {
      try {
        sep.graphics.foregroundColor = sep.graphics.newPen(ScriptUI.PenType.SOLID_COLOR, color, 1);
      } catch(e) {
        $.writeln("Error setting separator color: " + e);
      }
    }
  }
  return sep;
}

function addHelpTip(control, tip) {
  if (control) control.helpTip = tip;
}

// 创建带复选框的格式设置面板
function addFormatPanel(parent, formatName, panelLabel) {
  var panel = addPanel(parent, panelLabel, "left", PANEL_WIDTH);
  if (panel) {
    try {
      panel.graphics.foregroundColor = panel.graphics.newPen(ScriptUI.PenType.SOLID_COLOR, [0.72, 0.45, 0.2], 1);
    } catch(e) {
      $.writeln("Error setting panel foreground color: " + e);
    }
  }
  
  var group = addGroup(panel, "left", "row");
  // 修正：确保先创建控件再访问 graphics
  var checkbox = addCheckbox(group, "选择 " + formatName + " 格式");
  
  if (group) {
    try {
      group.graphics.foregroundColor = group.graphics.newPen(ScriptUI.PenType.SOLID_COLOR, [0.87, 0.97, 0.49], 1);
    } catch(e) {
      $.writeln("Error setting group foreground color: " + e);
    }
  }
  
  // 返回面板、组和复选框的引用，方便后续添加选项控件
  return { panel: panel, group: group, checkbox: checkbox };
}

// --- 核心保存逻辑 ---
function processSave(settings) {
    if (!settings.savePath) {
      alert("错误：请先选择保存地址！");
      return;
    }
    
    if (settings.formats.length === 0) {
      alert("错误：请至少选择一种保存格式！");
      return;
    }
    
    var saveFolder = new Folder(settings.savePath);
    if (!saveFolder.exists) {
      if (!saveFolder.create()) {
        alert("错误：无法创建保存文件夹：" + settings.savePath);
        return;
      }
    }
    
    if (settings.operation === "   ★ 另存全部文件并关闭") {
      saveAndCloseAllDocuments(settings);
    } else {
      saveSelectedDocuments(settings);
    }
    
    alert("提示：加入喷印联盟QQ群:248884685 图文广告同行交流群 \n \n                                   保  存  成  功！");
    
    try {
      saveFolder.execute(); // 尝试打开文件夹
    } catch (e) { /* 忽略打开文件夹的错误 */ }
  }
  
  // 保存并关闭所有文档
  function saveAndCloseAllDocuments(settings) {
    var docs = app.documents;
    var uniqueIndex = 0; // 用于文件名冲突的字母索引
    
    // 从后往前处理，这样关闭文档不会影响索引
    for (var i = docs.length - 1; i >= 0; i--) {
      var doc = docs[i];
      var baseFilename = constructFilename(doc, settings, uniqueIndex);
      var uniqueFilename = generateUniqueFileName(baseFilename, settings.savePath);
      var savePath = settings.savePath + "/" + uniqueFilename;
      
      if (saveDocumentAsFormats(doc, savePath, settings)) {
        uniqueIndex++; // 只有成功保存才增加索引
      }
      
      // 总是尝试关闭，即使保存失败
      try {
        doc.close(SaveOptions.DONOTSAVECHANGES);
      } catch (e) { 
        $.writeln("Error closing document " + doc.name + ": " + e); 
      }
    }
  }
  
  // 根据选项保存文档
  function saveSelectedDocuments(settings) {
    var docs = app.documents;
    var uniqueIndex = 0;
    
    for (var i = 0; i < docs.length; i++) {
      var doc = docs[i];
      var shouldSave = false;
      var shouldClose = false;
      
      switch (settings.operation) {
        case "※ 另存当前文件":
          if (doc === app.activeDocument) shouldSave = true;
          break;
        case "   ★ 另存并关闭当前文件":
          if (doc === app.activeDocument) {
            shouldSave = true;
            shouldClose = true;
          }
          break;
        case "※ 另存全部文件":
          shouldSave = true;
          break;
        // "   ★ 另存全部文件并关闭" 由 saveAndCloseAllDocuments 处理
        default:
          break;
      }
      
      if (shouldSave) {
        var baseFilename = constructFilename(doc, settings, uniqueIndex);
        var uniqueFilename = generateUniqueFileName(baseFilename, settings.savePath);
        var savePath = settings.savePath + "/" + uniqueFilename;
        
        if (saveDocumentAsFormats(doc, savePath, settings)) {
          uniqueIndex++;
        }
        
        if (shouldClose) {
          try {
            doc.close(SaveOptions.DONOTSAVECHANGES);
          } catch (e) { 
            $.writeln("Error closing document " + doc.name + ": " + e); 
          }
        }
      }
    }
  }
  
  // 构建文件名（不带扩展名）
  function constructFilename(doc, settings, index) {
    var baseName = getBaseName(doc.name);
    var dimensions = settings.addDimensions ? getDocumentDimensions(doc) : "";
    var prefix = (settings.usePrefix && settings.prefixText) ? settings.prefixText + "-" : "";
    var suffix = (settings.useSuffix && settings.suffixText) ? "-" + settings.suffixText : "";
    var filename;
    
    if (settings.addDimensions && settings.useSuffix) {
      filename = prefix + baseName + dimensions + suffix;
    } else if (settings.addDimensions) {
      filename = prefix + dimensions + "-" + baseName + suffix; // 原始逻辑是尺寸在前
    } else {
      filename = prefix + baseName + suffix;
    }
    
    // 原始逻辑似乎在后缀和尺寸同时存在时有特定行为，这里简化为后缀总在最后
    // 如果需要完全匹配原始逻辑，这部分需要根据原始代码更仔细地调整
    return filename;
  }
  
  // 生成唯一的文件名，处理冲突
  function generateUniqueFileName(baseFilename, folderPath) {
    var testPath = folderPath + "/" + baseFilename;
    var counter = 1;
    var alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    var alphaIndex = 0; // 使用字母索引处理第一次冲突
    var uniqueFilename = baseFilename;
    
    // 检查原始文件名是否存在（不带任何后缀）
    // 注意：这里检查的是不带扩展名的基础名，保存时会附加扩展名，
    // 所以真正的冲突检查应该在 saveDocumentAsFormats 内部或调用它之前进行，
    // 或者这里检查所有可能的扩展名。为了简化，暂时只检查基础名。
    // 一个更健壮的方法是检查 baseFilename + .[ext] 是否存在。
    
    // 尝试添加字母后缀 A, B, C...
    var tempFilename = baseFilename + "_" + alphabet[alphaIndex];
    var tempFile = new File(folderPath + "/" + tempFilename + ".jpg"); // 假设检查 .jpg
    
    while (tempFile.exists && alphaIndex < alphabet.length - 1) {
      alphaIndex++;
      tempFilename = baseFilename + "_" + alphabet[alphaIndex];
      tempFile = new File(folderPath + "/" + tempFilename + ".jpg");
    }
    
    // 如果字母后缀用完了或者第一个字母后缀就冲突了，开始用数字
    if (tempFile.exists) {
      tempFilename = baseFilename + "-" + alphabet[alphaIndex] + counter;
      tempFile = new File(folderPath + "/" + tempFilename + ".jpg");
      
      while(tempFile.exists) {
        counter++;
        tempFilename = baseFilename + "-" + alphabet[alphaIndex] + counter;
        tempFile = new File(folderPath + "/" + tempFilename + ".jpg");
      }
    }
    
    // 如果 tempFilename 没有因为冲突而被修改，就用原始 baseFilename 
    if (!tempFile.exists && alphaIndex === 0 && counter === 1 && !(new File(folderPath + "/" + baseFilename + ".jpg")).exists) { //再次检查原始基础名
      uniqueFilename = baseFilename;
    } else {
      uniqueFilename = tempFilename;
    }
    
    return uniqueFilename;
  }
  
  // 获取不带扩展名的文件名
  function getBaseName(fileName) {
    var dotIndex = fileName.lastIndexOf('.');
    if (dotIndex === -1) return fileName; // 没有扩展名
    return fileName.substring(0, dotIndex);
  }
  
  // 获取文档尺寸字符串
  function getDocumentDimensions(doc) {
    try {
      // 确保单位是毫米
      var originalRulerUnits = app.preferences.rulerUnits;
      app.preferences.rulerUnits = Units.MM;
      
      var width = Math.round(doc.width.as('mm'));
      var height = Math.round(doc.height.as('mm'));
      
      // 恢复原始单位
      app.preferences.rulerUnits = originalRulerUnits;
      
      return width + "x" + height + "mm";
    } catch (e) {
      $.writeln("Error getting document dimensions: " + e);
      return "SizeError"; // 返回错误标识
    }
  }
  
  // 保存文档为选定的格式
  function saveDocumentAsFormats(doc, savePathBase, settings) {
    var success = false;
    var docToSave = doc; // 直接操作原文档或副本？原脚本是副本，更安全
    var needsDuplication = settings.tif.save && settings.tif.mergeLayers; // 合并图层需要副本
    
    if (needsDuplication) {
      try {
        docToSave = doc.duplicate(); // 创建副本
      } catch (e) {
        $.writeln("Error duplicating document " + doc.name + ": " + e);
        return false; // 无法创建副本，保存失败
      }
    }
    
    // 确保激活要操作的文档（副本或原文档）
    try {
      app.activeDocument = docToSave;
    } catch (e) {
      $.writeln("Error activating document " + docToSave.name + ": " + e);
      if (needsDuplication) {
        try { 
          docToSave.close(SaveOptions.DONOTSAVECHANGES); 
        } catch(e2) {}
      }
      return false;
    }
    
    // TIF 合并图层操作（如果在副本上）
    if (needsDuplication && settings.tif.save && settings.tif.mergeLayers) {
      try {
        if (docToSave.layers.length > 1) { // 只有多于一个图层时才需要合并
          docToSave.flatten();
        }
      } catch (e) {
        $.writeln("Error flattening document " + docToSave.name + " for TIF: " + e);
        // 不中断，继续尝试保存其他格式
      }
    }
    
    for (var i = 0; i < settings.formats.length; i++) {
      var format = settings.formats[i];
      var saveOptions;
      var extension = "." + format.toLowerCase();
      var file = new File(savePathBase + extension);
      
      try {
        switch (format) {
          case "JPG":
            saveOptions = new JPEGSaveOptions();
            saveOptions.quality = settings.jpg.quality;
            saveOptions.embedColorProfile = true; // 通常需要嵌入配置文件
            docToSave.saveAs(file, saveOptions, true); // 第三个参数为 true 表示作为副本保存
            success = true;
            break;
            
          case "TIF":
            saveOptions = new TiffSaveOptions();
            // TIF 合并逻辑已在前面处理副本
            saveOptions.layers = !(settings.tif.mergeLayers); // 注意反转逻辑
            saveOptions.embedColorProfile = true;
            
            switch (settings.tif.compression) {
              case "LZW": 
                saveOptions.imageCompression = TIFFEncoding.TIFFLZW; 
                break;
              case "ZIP": 
                saveOptions.imageCompression = TIFFEncoding.TIFFZIP; 
                break;
              case "JPEG": 
                saveOptions.imageCompression = TIFFEncoding.JPEG; 
                break;
              default: 
                saveOptions.imageCompression = TIFFEncoding.NONE; 
                break;
            }
            
            docToSave.saveAs(file, saveOptions, true);
            success = true;
            break;
            
          case "PSD":
            saveOptions = new PhotoshopSaveOptions();
            saveOptions.layers = true; // PSD 通常保留图层
            saveOptions.embedColorProfile = true;
            
            // 注意：原脚本将 compatibility 绑定到 embedColorProfile，这里分开处理
            // ExtendScript 没有直接控制旧版 PSD 兼容性的选项，'Maximize Compatibility' 在 PS 保存对话框中控制
            // 这里我们假设用户希望保留图层和颜色信息
            // 如果 psd.compatibility 代表别的意思，需要厘清
            // saveOptions.maximizeCompatibility = settings.psd.compatibility; // Maximize Compatibility 不是标准 API 属性
            
            docToSave.saveAs(file, saveOptions, true);
            success = true;
            break;
            
          case "PNG":
            saveOptions = new PNGSaveOptions();
            saveOptions.interlaced = settings.png.interlace;
            saveOptions.compression = 9; // 0-9，9 是最大压缩，原脚本未设置，这里给个默认值
            
            docToSave.saveAs(file, saveOptions, true);
            success = true;
            break;
        }
        
        $.writeln("Successfully saved: " + file.fsName);
      } catch (e) {
        $.writeln("Error saving " + format + " for " + docToSave.name + ": " + e);
        // 可以选择在这里 alert 错误，或者只记录日志
        // alert("保存 " + format + " 格式失败: " + e);
      }
    }
    
    // 如果创建了副本，关闭它
    if (needsDuplication) {
      try {
        docToSave.close(SaveOptions.DONOTSAVECHANGES);
      } catch (e) { 
        $.writeln("Error closing duplicate document: " + e); 
      }
    }
    
    return success; // 返回是否有任何格式保存成功
  }
  
  // --- 启动脚本 ---
  main();
  
