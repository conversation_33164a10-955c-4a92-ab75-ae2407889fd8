// 创建对话框
var dlg = new Window("dialog", "图层复制工具");
dlg.orientation = "column";

// 创建纵向复制数量和横向复制数量输入
var rowColGroup = dlg.add("group");
rowColGroup.add("statictext", undefined, "横数:", {characters: 12});
var colCount = rowColGroup.add("edittext", undefined, "1");
colCount.characters = 5;
rowColGroup.add("statictext", undefined, "列数:", {characters: 12});
var rowCount = rowColGroup.add("edittext", undefined, "1");
rowCount.characters = 5;

// 创建横向和纵向间距输入
var spaceGroup = dlg.add("group");
spaceGroup.add("statictext", undefined, "横距(mm):", {characters: 12});
var hSpace = spaceGroup.add("edittext", undefined, "0");
hSpace.characters = 5;
spaceGroup.add("statictext", undefined, "列距(mm):", {characters: 12});
var vSpace = spaceGroup.add("edittext", undefined, "0");
vSpace.characters = 5;

// 创建铺满画布选项
var fillCanvasGroup = dlg.add("group");
var fillCanvas = fillCanvasGroup.add("checkbox", undefined, "横距平均排列");

// 创建合并图层选项
var mergeGroup = dlg.add("group");
var mergeLayers = mergeGroup.add("checkbox", undefined, "复制完成后合并图层");

// 创建按钮组
var btnGroup = dlg.add("group");
btnGroup.add("button", undefined, "确定", {name: "ok"});
btnGroup.add("button", undefined, "取消", {name: "cancel"});

fillCanvas.onClick = function() {
    hSpace.enabled = !fillCanvas.value;
}

if(dlg.show() == 1) {
    app.activeDocument.suspendHistory("图层阵列复制", "executeArrayCopy()");
}

function executeArrayCopy() {
    var doc = app.activeDocument;
    var layer = doc.activeLayer;
    var originalRulerUnits = app.preferences.rulerUnits;
    var originalDialogMode = app.displayDialogs;
    
    app.displayDialogs = DialogModes.NO;
    app.preferences.rulerUnits = Units.MM;
    
    var bounds = layer.bounds;
    var startX = bounds[0].value;
    var startY = bounds[1].value;
    layer.translate(-startX, -startY);

    var layerWidth = bounds[2].value - bounds[0].value;
    var layerHeight = bounds[3].value - bounds[1].value;
    var docWidth = doc.width.as('mm');
    
    var rows = Number(rowCount.text);
    var cols = Number(colCount.text);
    
    var spacing = fillCanvas.value ? (docWidth - layerWidth) / (cols - 1) : (layerWidth + Number(hSpace.text));
    var verticalSpacing = layerHeight + Number(vSpace.text);

    var group = doc.layerSets.add();
    group.name = "阵列图层";
    layer.move(group, ElementPlacement.PLACEATEND);

    for(var row = 0; row < rows; row++) {
        for(var col = 0; col < cols; col++) {
            if(row == 0 && col == 0) continue;

            var duplicate = layer.duplicate();
            duplicate.move(group, ElementPlacement.PLACEATEND);
            
            var xOffset = col * spacing;
            var yOffset = row * verticalSpacing;
            
            duplicate.translate(xOffset, yOffset);
        }
    }

    if(mergeLayers.value) {
        group.merge();
    }

    app.preferences.rulerUnits = originalRulerUnits;
    app.displayDialogs = originalDialogMode;
}
