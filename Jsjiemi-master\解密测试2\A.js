const fs = require('fs');
const vm = require('vm');

const targetFilePath = './target.js'; // 目标文件路径

try {
    let code = fs.readFileSync(targetFilePath, 'utf8');
    console.log("--- 正在加载原始代码 ---");

    // --- 第 1 层：解开最外层的 jsjiami.com.v6 和 eval 压缩 ---
    console.log("\n--- 正在解密第 1 层 (外层 jsji<PERSON> + Packer) ---");

    let layer1PackedCode = '';
    // 创建一个沙箱环境来捕获第一次 eval 的参数
    const sandbox1 = {
        console: { // 重定向 console.log 以观察（可选）
            log: (...args) => console.log("沙箱1日志:", ...args)
        },
        // 关键：替换 eval 函数，使其不执行代码，而是存储参数
        eval: (packedCode) => {
            layer1PackedCode = packedCode;
            console.log("已捕获外层 eval() 的参数，长度:", layer1PackedCode.length);
        },
        // 需要模拟原始代码中的 iｉl 和 iｉl_ 变量/函数，否则会报错
        iｉl: 'jsjiami.com.v6',
        iｉl_: () => {} // 空函数即可，内部逻辑通常不需要
    };

    // 在沙箱中运行最外层代码
    vm.runInNewContext(code, sandbox1);

    if (!layer1PackedCode) {
        throw new Error("未能捕获到外层 eval 的参数。检查 target.js 是否正确。");
    }
    // layer1PackedCode 现在包含了 eval(function(p,a,c,k,e,r){...}) 这样的字符串

    // --- 第 2 层：执行 Packer 解压缩 ---
    console.log("\n--- 正在解压第 2 层 (Packer) ---");

    let layer2JsjiamiCode = '';
    // Packer 通常会返回一个包含代码的字符串，或者内部调用 eval
    const sandbox2 = {
       console: { log: (...args) => console.log("沙箱2日志:", ...args) },
       // 再次替换 eval，捕获 Packer 解压后的代码
       eval: (unpackedCode) => {
           layer2JsjiamiCode = unpackedCode;
           console.log("已捕获 Packer 解压后的代码，长度:", layer2JsjiamiCode.length);
       }
    };

    try {
        // 尝试在沙箱中运行 Packer 代码
        vm.runInNewContext(layer1PackedCode, sandbox2);
    } catch(e) {
         console.warn("在沙箱2中运行 Packer 时出错 (可能是因为它不直接调用 eval):", e.message);
         console.log("尝试直接执行 Packer 函数以获取返回值...");
         // 如果 Packer 不调用 eval 而是直接返回值，我们需要捕获这个返回值
         const sandbox2Direct = {
             console: { log: (...args) => console.log("沙箱2直接执行日志:", ...args) },
         };
         layer2JsjiamiCode = vm.runInNewContext(layer1PackedCode, sandbox2Direct);
    }


    if (typeof layer2JsjiamiCode !== 'string' || !layer2JsjiamiCode) {
        throw new Error("未能从 Packer 层解压出代码。");
    }
     console.log("Packer 解压成功，得到内层代码，长度:", layer2JsjiamiCode.length);
    // layer2JsjiamiCode 现在包含了内层的 jsjiami.com.v6 代码

    // --- 第 3 层：解开内层的 jsjiami.com.v6 ---
    console.log("\n--- 正在解密第 3 层 (内层 jsjiami) ---");

    let finalCode = '';
    // 内层的 jsjiami 也会有自己的 eval
    const sandbox3 = {
        console: { log: (...args) => console.log("沙箱3日志:", ...args) },
        // 最终捕获
        eval: (innerEvalCode) => {
            finalCode = innerEvalCode;
            console.log("已捕获内层 eval() 的参数 (最终代码)，长度:", finalCode.length);
        },
        // 内层可能也需要模拟这些变量
        iｉl: 'jsjiami.com.v6', // 假设变量名相同
        iｉl_: () => {}       // 假设变量名相同
    };

     // 运行内层的 jsjiami 代码
    vm.runInNewContext(layer2JsjiamiCode, sandbox3);

    if (!finalCode) {
         // Fallback: 如果沙箱未能捕获 eval（有时因为复杂的上下文依赖）
         // 尝试用正则表达式查找最后那个 eval(...)
         console.warn("沙箱3未能捕获内层 eval 参数，尝试使用正则表达式查找...");
         // 匹配 `eval(...)` 且通常在代码末尾，允许分号和空格
         const finalEvalMatch = layer2JsjiamiCode.match(/eval\((.+)\)\s*;?\s*$/s);
         if (finalEvalMatch && finalEvalMatch[1]) {
             console.log("通过正则表达式找到可能的 eval 参数。");
             // 这个参数本身可能还需要在内层代码的上下文中解析才能得到最终字符串
             const contextForFinalArg = vm.createContext({
                 console: { log: (...args) => console.log("最终参数解析日志:", ...args) },
                 // 可能需要从 layer2JsjiamiCode 中提取一些变量定义来放入这个 context
             });
             // 先执行内层代码设置上下文（不包括最后的eval）
             let setupCode = layer2JsjiamiCode.substring(0, layer2JsjiamiCode.lastIndexOf('eval'));
             vm.runInContext(setupCode, contextForFinalArg);
             // 然后在上下文中解析 eval 的参数
             finalCode = vm.runInContext(finalEvalMatch[1], contextForFinalArg);
             console.log("通过正则+上下文解析得到最终代码，长度:", finalCode.length);
         } else {
            throw new Error("未能解密内层 jsjiami 代码。找不到最终的 eval 调用或无法捕获其参数。");
         }
    }

    console.log("\n--- 最终解密得到的 JSX 代码 ---");
    // 美化一下代码（可选，但推荐）
    try {
        // 尝试使用 prettier 格式化，如果安装了的话
        const prettier = require("prettier");
        finalCode = prettier.format(finalCode, { parser: "babel" }); // 使用 babel 解析器
        console.log("(代码已自动格式化)");
    } catch (formatError) {
        console.warn("\n未能自动格式化代码 (可能未安装 prettier 或代码有语法问题):", formatError.message);
        console.warn("将输出原始未格式化代码。");
    }

    console.log(finalCode);

    // --- 保存结果 ---
    const outputFilePath = './deobfuscated_target.jsx'; // 保存为 .jsx 文件
    fs.writeFileSync(outputFilePath, finalCode, 'utf8');
    console.log(`\n--- 解密后的代码已保存到: ${outputFilePath} ---`);

} catch (error) {
    console.error("\n--- 解密过程中发生错误 ---");
    console.error(error);
}