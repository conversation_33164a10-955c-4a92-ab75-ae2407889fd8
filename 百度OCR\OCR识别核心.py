#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import base64
import requests
import time
import traceback
import logging
from urllib.parse import urlencode
from datetime import datetime
from io import BytesIO
from PIL import Image

# 在Windows系统下隐藏控制台窗口 - 加强版
if sys.platform == 'win32':
    try:
        # 方法1: 使用pywin32
        try:
            import win32gui
            import win32con
            import win32process
            import ctypes
            
            # 尝试隐藏控制台窗口
            hwnd = ctypes.windll.kernel32.GetConsoleWindow()
            if hwnd:
                ctypes.windll.user32.ShowWindow(hwnd, 0)  # SW_HIDE = 0
                logger = logging.getLogger()
                logger.info("成功使用pywin32方法隐藏控制台窗口")
        except Exception as e:
            pass  # 忽略隐藏窗口失败的错误
        
        # 方法2: 使用ctypes
        try:
            import ctypes
            kernel32 = ctypes.WinDLL('kernel32')
            user32 = ctypes.WinDLL('user32')
            hwnd = kernel32.GetConsoleWindow()
            if hwnd:
                user32.ShowWindow(hwnd, 0)
        except:
            pass
            
        # 方法3: 更通用的WIN32 API方法
        try:
            import ctypes
            ctypes.CDLL('kernel32').FreeConsole()
        except:
            pass
            
    except Exception as e:
        # 任何错误都忽略，我们只是尝试隐藏窗口
        pass

# 配置调试日志
def setup_logging():
    log_dir = os.path.join(os.path.expanduser("~"), "Desktop")
    log_file = os.path.join(log_dir, "PhotoshopOCR_Python_Debug.log")
    
    # 移除控制台输出，只保留文件日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8')
            # 移除StreamHandler，避免控制台输出
        ]
    )
    logger = logging.getLogger()
    logger.info("===== OCR识别核心程序启动 =====")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info(f"命令行参数: {sys.argv}")
    return logger

logger = setup_logging()

def convert_rgba_to_rgb(img):
    """将RGBA图像转换为RGB图像，保留透明背景为白色"""
    logger.info("开始将RGBA图像转换为RGB")
    if img.mode == 'RGBA':
        # 创建一个白色背景
        background = Image.new('RGB', img.size, (255, 255, 255))
        # 使用透明度通道(alpha)作为mask复合图像
        background.paste(img, mask=img.split()[3])  # 3是alpha通道
        logger.info("RGBA图像已成功转换为RGB")
        return background
    elif img.mode != 'RGB':
        logger.info(f"检测到{img.mode}模式图像，转换为RGB模式")
        return img.convert('RGB')
    return img

# 压缩图像功能
def compress_image(image_path, max_size=3*1024*1024):
    """压缩图像以满足百度OCR API的要求"""
    logger.info(f"开始压缩图像: {image_path}")
    
    try:
        # 打开图像
        if isinstance(image_path, str):
            img = Image.open(image_path)
        else:  # 假设是BytesIO或类似对象
            img = Image.open(image_path)
            image_path.seek(0)  # 重置流位置
        
        # 保存初始尺寸
        original_width, original_height = img.size
        logger.info(f"原始图像尺寸: {original_width}x{original_height}")
        
        # 处理RGBA模式
        img = convert_rgba_to_rgb(img)
        
        # 创建内存文件对象
        output_buffer = BytesIO()
        
        # 调整尺寸（如果需要）
        max_dim = 2048
        if original_width > max_dim or original_height > max_dim:
            # 计算比例
            ratio = min(max_dim / original_width, max_dim / original_height)
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)
            
            # 调整尺寸
            img = img.resize((new_width, new_height), Image.LANCZOS)
            logger.info(f"调整图像尺寸为: {new_width}x{new_height}")
        
        # 初始质量
        quality = 85
        
        # 保存为JPEG格式并逐步降低质量直到满足大小要求
        while True:
            output_buffer.seek(0)
            output_buffer.truncate(0)
            img.save(output_buffer, format="JPEG", quality=quality, optimize=True)
            size = output_buffer.tell()
            
            if size <= max_size or quality <= 30:
                break
            
            quality -= 10
        
        logger.info(f"压缩后图像质量: {quality}%, 大小: {size/1024/1024:.2f}MB")
        
        # 如果仍然超过最大尺寸，进一步降低分辨率
        if size > max_size and original_width > 1024:
            logger.info("压缩后仍然超过最大尺寸，降低分辨率")
            # 进一步减小尺寸
            max_dim = 1024
            ratio = min(max_dim / img.width, max_dim / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            img = img.resize((new_width, new_height), Image.LANCZOS)
            
            # 重新保存
            output_buffer.seek(0)
            output_buffer.truncate(0)
            img.save(output_buffer, format="JPEG", quality=quality, optimize=True)
            size = output_buffer.tell()
            logger.info(f"进一步压缩后图像尺寸: {new_width}x{new_height}, 大小: {size/1024/1024:.2f}MB")
        
        # 获取压缩后的图像数据
        output_buffer.seek(0)
        return output_buffer.read()
    
    except Exception as e:
        logger.error(f"压缩图像时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return None

# 百度OCR API配置
OCR_API_URLS = {
    'general_basic': 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic',  # 通用文字识别
    'accurate_basic': 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic',  # 通用文字识别（高精度）
    'handwriting': 'https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting',  # 手写文字识别
    'general': 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate',  # 通用文字识别(高精度版)
    'webimage': 'https://aip.baidubce.com/rest/2.0/ocr/v1/webimage',  # 网络图片文字识别
    'numbers': 'https://aip.baidubce.com/rest/2.0/ocr/v1/numbers',  # 数字识别
}

# 获取百度OCR API访问令牌
def get_access_token(api_key, secret_key):
    try:
        logger.info("开始获取百度云访问令牌")
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": api_key,
            "client_secret": secret_key
        }
        response = requests.post(url, params=params)
        result = response.json()
        
        if 'access_token' in result:
            logger.info("成功获取访问令牌")
            return result["access_token"]
        else:
            logger.error(f"获取访问令牌失败: {result}")
            return None
    except Exception as e:
        logger.error(f"获取访问令牌过程中发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return None

# 使用URL方式进行OCR识别（适用于大图像）
def recognize_image_url(image_data, access_token, recognition_type='general'):
    """使用URL方式进行OCR识别，适合大图像"""
    logger.info(f"开始使用URL方式识别图像，类型: {recognition_type}")
    
    # 创建临时压缩文件
    temp_dir = os.path.join(os.path.expanduser("~"), "Desktop", "OCR_TEMP")
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    
    temp_file = os.path.join(temp_dir, "temp_compressed.jpg")
    
    try:
        # 如果输入是文件路径
        if isinstance(image_data, str) and os.path.isfile(image_data):
            img = Image.open(image_data)
        else:
            # 否则假设是二进制数据
            img = Image.open(BytesIO(image_data))
        
        # 调整图像尺寸（如果需要）
        max_dim = 1024  # 减小尺寸到1024像素
        original_width, original_height = img.size
        
        # 计算比例，强制缩小图像
        ratio = min(max_dim / original_width, max_dim / original_height)
        new_width = int(original_width * ratio)
        new_height = int(original_height * ratio)
        
        # 调整尺寸
        img = img.resize((new_width, new_height), Image.LANCZOS)
        logger.info(f"调整图像尺寸为: {new_width}x{new_height}")
        
        # 检查图像模式，如果是RGBA，转换为RGB
        img = convert_rgba_to_rgb(img)
        
        # 保存为JPEG，质量降低到30%
        img.save(temp_file, format="JPEG", quality=30, optimize=True)
        logger.info(f"临时文件保存到: {temp_file}")
        
        # 读取图像内容并检查大小
        file_size = os.path.getsize(temp_file)
        logger.info(f"压缩后的文件大小: {file_size/1024/1024:.2f}MB")
        
        # 如果文件仍然太大，进一步压缩
        if file_size > 1*1024*1024:  # 如果大于1MB
            logger.info("文件仍然太大，进一步压缩")
            
            # 读取已压缩的图像
            img = Image.open(temp_file)
            
            # 进一步减小尺寸
            ratio = 0.7  # 再缩小到70%
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            img = img.resize((new_width, new_height), Image.LANCZOS)
            
            # 以更低质量保存
            img.save(temp_file, format="JPEG", quality=20, optimize=True)
            logger.info(f"进一步压缩后尺寸: {new_width}x{new_height}")
            
            file_size = os.path.getsize(temp_file)
            logger.info(f"进一步压缩后大小: {file_size/1024:.2f}KB")
        
        # 如果图片大小仍然超过900KB，改用图片直接上传方式而不是base64 URL
        if file_size > 900*1024:
            logger.info("文件仍然太大，改用直接上传图像方式")
            return recognize_with_direct_upload(temp_file, access_token, recognition_type)
        
        # 读取图像内容
        with open(temp_file, 'rb') as f:
            image_content = f.read()
        
        # 确保识别类型有效
        if recognition_type not in OCR_API_URLS:
            logger.warning(f"未知的识别类型: {recognition_type}，使用默认类型: general")
            recognition_type = 'general'
        
        # 获取请求URL
        request_url = OCR_API_URLS[recognition_type]
        url = f"{request_url}?access_token={access_token}"
        
        # 使用URL接口方法
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }
        
        # 使用base64编码图像，作为url参数
        image_base64 = base64.b64encode(image_content).decode('utf-8')
        logger.info(f"Base64编码后大小: {len(image_base64)/1024:.2f}KB")
        
        data = {
            'url': f"data:image/jpeg;base64,{image_base64}",
            'detect_direction': 'false',
            'probability': 'false'
        }
        
        # 发送请求
        logger.info(f"使用URL方式发送OCR请求到: {request_url}")
        start_time = time.time()
        response = requests.post(url, headers=headers, data=urlencode(data), timeout=60)
        end_time = time.time()
        
        logger.info(f"OCR请求完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"响应状态码: {response.status_code}")
        
        # 处理响应
        result = response.json()
        
        # 检查错误
        if 'error_code' in result:
            logger.error(f"OCR API返回错误: {result}")
            # 如果错误是URL大小错误，尝试直接上传图像
            if result.get('error_code') == 282114:
                logger.info("URL大小错误，改用直接上传图像方式")
                return recognize_with_direct_upload(temp_file, access_token, recognition_type)
            return result
            
        # 处理成功结果
        logger.info(f"OCR识别成功: {result.get('words_result_num', 0)} 个结果")
        return result
    
    except Exception as e:
        logger.error(f"URL方式OCR识别过程中发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"OCR识别异常: {str(e)}"}
    
    finally:
        # 清理临时文件
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                logger.info(f"临时文件已删除: {temp_file}")
        except:
            pass

# 使用直接上传方式识别图像
def recognize_with_direct_upload(image_path, access_token, recognition_type='general'):
    """直接上传方式识别图像"""
    logger.info(f"开始使用直接上传方式识别图像，类型: {recognition_type}")
    
    try:
        # 读取图像内容
        with open(image_path, 'rb') as f:
            image_content = f.read()
        
        # 获取文件大小
        file_size = len(image_content)
        logger.info(f"上传文件大小: {file_size/1024:.2f}KB")
        
        # 确保识别类型有效
        if recognition_type not in OCR_API_URLS:
            logger.warning(f"未知的识别类型: {recognition_type}，使用默认类型: general")
            recognition_type = 'general'
        
        # 获取请求URL
        request_url = OCR_API_URLS[recognition_type]
        url = f"{request_url}?access_token={access_token}"
        
        # 构建请求参数
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        
        # Base64编码
        image_base64 = base64.b64encode(image_content)
        data = {'image': image_base64}
        
        # 发送请求
        logger.info(f"发送直接上传OCR请求到: {request_url}")
        start_time = time.time()
        response = requests.post(url, headers=headers, data=data, timeout=60)
        end_time = time.time()
        
        logger.info(f"OCR请求完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"响应状态码: {response.status_code}")
        
        # 处理响应
        result = response.json()
        
        # 检查错误
        if 'error_code' in result:
            logger.error(f"OCR API返回错误: {result}")
            # 如果错误是图像太大，尝试更激进的压缩
            if result.get('error_code') == 216101:
                logger.info("图像数据大小错误，尝试灰度图像压缩")
                return recognize_with_grayscale_compression(image_path, access_token, recognition_type)
            return result
            
        # 处理成功结果
        logger.info(f"OCR识别成功: {result.get('words_result_num', 0)} 个结果")
        return result
    
    except Exception as e:
        logger.error(f"直接上传OCR识别过程中发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"OCR识别异常: {str(e)}"}

# 使用灰度图像压缩
def recognize_with_grayscale_compression(image_path, access_token, recognition_type='general'):
    """使用灰度图像极限压缩方式进行OCR识别"""
    logger.info(f"开始使用灰度图像压缩方式识别图像，类型: {recognition_type}")
    
    temp_dir = os.path.join(os.path.expanduser("~"), "Desktop", "OCR_TEMP")
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    
    temp_file = os.path.join(temp_dir, "gray_ultra_compressed.jpg")
    
    try:
        # 打开图像
        img = Image.open(image_path)
        
        # 如果是RGBA模式，先转换为RGB
        if img.mode == 'RGBA':
            img = convert_rgba_to_rgb(img)
        
        # 转换为灰度图像
        logger.info("转换为灰度图像")
        img = img.convert('L')
        
        # 调整尺寸
        max_dim = 800
        original_width, original_height = img.size
        ratio = min(max_dim / original_width, max_dim / original_height)
        new_width = int(original_width * ratio)
        new_height = int(original_height * ratio)
        img = img.resize((new_width, new_height), Image.LANCZOS)
        logger.info(f"调整灰度图像尺寸为: {new_width}x{new_height}")
        
        # 极低质量保存
        img.save(temp_file, format="JPEG", quality=15, optimize=True)
        
        file_size = os.path.getsize(temp_file)
        logger.info(f"灰度压缩后大小: {file_size/1024:.2f}KB")
        
        # 确保识别类型有效
        if recognition_type not in OCR_API_URLS:
            logger.warning(f"未知的识别类型: {recognition_type}，使用默认类型: general")
            recognition_type = 'general'
        
        # 获取请求URL
        request_url = OCR_API_URLS[recognition_type]
        url = f"{request_url}?access_token={access_token}"
        
        # 读取图像内容
        with open(temp_file, 'rb') as f:
            image_content = f.read()
        
        # 构建请求参数
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        
        # Base64编码
        image_base64 = base64.b64encode(image_content)
        data = {'image': image_base64}
        
        # 发送请求
        logger.info(f"发送灰度压缩OCR请求到: {request_url}")
        start_time = time.time()
        response = requests.post(url, headers=headers, data=data, timeout=60)
        end_time = time.time()
        
        logger.info(f"OCR请求完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"响应状态码: {response.status_code}")
        
        # 处理响应
        result = response.json()
        
        # 检查错误
        if 'error_code' in result:
            logger.error(f"OCR API返回错误: {result}")
            return result
            
        # 处理成功结果
        logger.info(f"OCR识别成功: {result.get('words_result_num', 0)} 个结果")
        return result
    
    except Exception as e:
        logger.error(f"灰度压缩OCR识别过程中发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"OCR识别异常: {str(e)}"}
    
    finally:
        # 清理临时文件
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                logger.info(f"临时文件已删除: {temp_file}")
        except:
            pass

# 使用base64方式进行OCR识别
def recognize_image_base64(image_data, access_token, recognition_type='general'):
    """使用base64编码图像数据进行OCR识别"""
    try:
        logger.info(f"开始使用base64识别图像，类型: {recognition_type}")
        
        # 确保识别类型有效
        if recognition_type not in OCR_API_URLS:
            logger.warning(f"未知的识别类型: {recognition_type}，使用默认类型: general")
            recognition_type = 'general'
        
        # 获取请求URL
        request_url = OCR_API_URLS[recognition_type]
        logger.info(f"使用API URL: {request_url}")
        
        # Base64编码
        if isinstance(image_data, str) and os.path.isfile(image_data):
            # 是文件路径，读取文件
            with open(image_data, 'rb') as f:
                image_data = f.read()
                
        # 检查大小
        if len(image_data) > 4*1024*1024:
            logger.warning(f"图像数据过大 ({len(image_data)/1024/1024:.2f}MB)，尝试压缩")
            image_data = compress_image(BytesIO(image_data))
            if image_data is None:
                return {"error": "图像压缩失败"}
            logger.info(f"压缩后大小: {len(image_data)/1024/1024:.2f}MB")
        
        # 如果仍然太大，切换到URL方式
        if len(image_data) > 4*1024*1024:
            logger.info("图像仍然太大，切换到URL方式识别")
            return recognize_image_url(image_data, access_token, recognition_type)
            
        # Base64编码
        image_base64 = base64.b64encode(image_data)
        
        # 构建请求参数
        url = f"{request_url}?access_token={access_token}"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        data = {'image': image_base64}
        
        # 发送请求
        logger.info(f"发送OCR请求")
        start_time = time.time()
        response = requests.post(url, headers=headers, data=data, timeout=60)
        end_time = time.time()
        
        logger.info(f"OCR请求完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"响应状态码: {response.status_code}")
        
        # 处理响应
        result = response.json()
        
        # 检查错误
        if 'error_code' in result:
            logger.error(f"OCR API返回错误: {result}")
            # 如果错误是图像太大，尝试URL方法
            if result.get('error_code') == 216205:
                logger.info("图像过大错误，尝试使用URL方式")
                return recognize_image_url(image_data, access_token, recognition_type)
            return result
        
        # 处理成功结果
        logger.info(f"OCR识别成功: {result.get('words_result_num', 0)} 个结果")
        return result
    
    except Exception as e:
        logger.error(f"OCR识别过程中发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"OCR识别异常: {str(e)}"}

# 提取识别结果中的文本
def extract_text_from_result(ocr_result):
    try:
        text = ""
        if "words_result" in ocr_result:
            words_results = ocr_result["words_result"]
            for result in words_results:
                text += result["words"] + "\n"
        return text.strip()
    except Exception as e:
        logger.error(f"提取文本结果时出错: {str(e)}")
        return f"提取结果错误: {str(e)}"

# 主函数
def main():
    try:
        # 检查命令行参数
        if len(sys.argv) < 3:
            logger.error("参数不足。用法: python OCR识别核心.py 参数文件路径 结果文件路径")
            return
        
        params_file = sys.argv[1]
        results_file = sys.argv[2]
        
        logger.info(f"参数文件: {params_file}")
        logger.info(f"结果文件: {results_file}")
        
        # 确保结果文件目录存在
        results_dir = os.path.dirname(results_file)
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
            logger.info(f"创建结果文件目录: {results_dir}")
        
        # 检查结果文件是否是目录
        if os.path.isdir(results_file):
            # 如果是目录，在该目录下创建results.json文件
            results_file = os.path.join(results_file, "results.json")
            logger.info(f"结果文件是目录，更新为: {results_file}")
            
        # 读取参数文件
        if not os.path.isfile(params_file):
            logger.error(f"参数文件不存在: {params_file}")
            return
        
        try:
            # 尝试不同的编码方式读取参数文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            params_content = None
            
            for encoding in encodings:
                try:
                    with open(params_file, "r", encoding=encoding) as f:
                        params_content = f.read()
                    logger.info(f"成功使用 {encoding} 编码读取参数文件")
                    break
                except UnicodeDecodeError:
                    logger.warning(f"使用 {encoding} 编码读取参数文件失败，尝试下一种编码")
            
            if params_content is None:
                logger.error("所有编码方式都无法读取参数文件内容")
                raise ValueError("无法读取参数文件，所有编码方式都失败")
                
            # 解析JSON内容
            try:
                params = json.loads(params_content)
                logger.info("参数文件JSON解析成功")
            except json.JSONDecodeError as e:
                logger.error(f"参数文件JSON解析失败: {str(e)}")
                
                # 尝试修复可能的编码问题
                try:
                    # 尝试替换可能的非法字符
                    fixed_content = params_content.replace('\\\\', '\\').replace('\\"', '"')
                    params = json.loads(fixed_content)
                    logger.info("修复后JSON解析成功")
                except:
                    # 如果上述修复失败，尝试使用eval函数
                    try:
                        logger.info("尝试使用eval解析JSON")
                        params = eval(params_content)
                        logger.info("使用eval解析JSON成功")
                    except Exception as eval_error:
                        logger.error(f"使用eval解析JSON失败: {str(eval_error)}")
                        raise
            
            # 获取API密钥
            api_key = params.get("api_key", "")
            secret_key = params.get("secret_key", "")
            files = params.get("files", [])
            recognition_type = params.get("recognition_type", "general")
            
            logger.info(f"API Key长度: {len(api_key)}, Secret Key长度: {len(secret_key)}")
            logger.info(f"文件数量: {len(files)}")
            logger.info(f"识别类型: {recognition_type}")
            
            if not api_key or not secret_key:
                logger.error("API密钥未设置")
                results = [{"error": "API密钥未设置"}]
            elif not files:
                logger.error("没有指定要识别的图像文件")
                results = [{"error": "没有指定要识别的图像文件"}]
            else:
                # 获取访问令牌
                access_token = get_access_token(api_key, secret_key)
                if not access_token:
                    logger.error("无法获取访问令牌")
                    results = [{"error": "无法获取百度AI访问令牌，请检查API密钥"}]
                else:
                    # 处理每个文件
                    results = []
                    for index, file_info in enumerate(files):
                        file_path = file_info.get("path", "")
                        layer_name = file_info.get("name", "")
                        
                        logger.info(f"处理文件 {index+1}/{len(files)}: {layer_name} - {file_path}")
                        
                        # 检查文件是否存在
                        if not os.path.isfile(file_path):
                            logger.error(f"文件不存在: {file_path}")
                            results.append({
                                "error": f"文件不存在: {file_path}",
                                "layerName": layer_name
                            })
                            continue
                        
                        # 检查文件大小
                        file_size = os.path.getsize(file_path)
                        logger.info(f"文件大小: {file_size/1024/1024:.2f}MB")
                        
                        # 根据文件大小选择不同的识别方法
                        if file_size > 10*1024*1024:
                            logger.info("文件过大，使用URL方式识别")
                            ocr_result = recognize_image_url(file_path, access_token, recognition_type)
                        else:
                            logger.info("使用base64方式识别")
                            ocr_result = recognize_image_base64(file_path, access_token, recognition_type)
                        
                        # 检查结果
                        if "error_code" in ocr_result:
                            error_msg = f"API错误：{ocr_result.get('error_msg', '未知错误')}"
                            logger.error(error_msg)
                            results.append({
                                "error": error_msg,
                                "text": "",
                                "layerName": layer_name
                            })
                            continue
                        
                        # 提取文本和位置
                        text_result = ""
                        position = None
                        
                        if "words_result" in ocr_result:
                            words_results = ocr_result["words_result"]
                            logger.info(f"识别结果数量: {len(words_results)}")
                            
                            # 处理文本结果
                            for item in words_results:
                                if isinstance(item, dict) and "words" in item:
                                    text_result += item["words"] + "\n"
                                    
                                    # 获取第一个词的位置信息作为文本位置
                                    if not position and "location" in item:
                                        loc = item["location"]
                                        position = {
                                            "x": loc.get("left", 0),
                                            "y": loc.get("top", 0)
                                        }
                                        logger.info(f"获取文本位置: x={position['x']}, y={position['y']}")
                        
                        # 去除最后多余的换行符
                        text_result = text_result.rstrip("\n")
                        logger.info(f"提取文本长度: {len(text_result)}")
                        if text_result:
                            logger.info(f"文本预览: {text_result[:100]}...")
                        
                        results.append({
                            "text": text_result,
                            "position": position,
                            "layerName": layer_name
                        })
                        
                        logger.info(f"文件 {file_path} 处理完成")
            
            # 将结果写入文件
            logger.info(f"将结果写入文件: {results_file}")
            try:
                # 检查并清理目标路径周围的空格
                results_file = results_file.strip()
                logger.info(f"清理后的结果文件路径: {results_file}")
                
                # 检查并确保路径有效
                if '\\' in results_file and '/' in results_file:
                    logger.warning("路径中同时包含正斜杠和反斜杠，尝试统一")
                    results_file = results_file.replace('\\', '/')
                    logger.info(f"统一后的路径: {results_file}")
                
                # 确保目录存在
                results_dir = os.path.dirname(results_file)
                logger.info(f"结果文件目录: {results_dir}")
                
                if not results_dir:
                    logger.warning("结果文件目录为空，使用当前目录")
                    results_dir = os.getcwd()
                    results_file = os.path.join(results_dir, os.path.basename(results_file))
                    logger.info(f"更新后的结果文件路径: {results_file}")
                
                if not os.path.exists(results_dir):
                    os.makedirs(results_dir, exist_ok=True)
                    logger.info(f"创建结果文件目录: {results_dir}")
                else:
                    logger.info(f"结果文件目录已存在")
                
                # 检查目录是否可写
                try:
                    test_file = os.path.join(results_dir, "_test_write_access.tmp")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    logger.info(f"目录可写: {results_dir}")
                except Exception as e:
                    logger.warning(f"目录写入测试失败: {str(e)}，尝试备用方案")
                    # 使用临时目录
                    results_file = os.path.join(os.environ.get('TEMP', os.getcwd()), "OCR_result_backup.json")
                    logger.info(f"改用临时目录写入: {results_file}")
                
                # 确保结果不为空
                if not results:
                    logger.warning("结果为空，创建默认结果")
                    results = [{"warning": "无识别结果"}]
                
                # 准备JSON数据
                json_result = json.dumps(results, ensure_ascii=False, indent=2)
                logger.info(f"JSON结果长度: {len(json_result)} 字符")
                
                # 按不同方式尝试写入
                written = False
                
                # 方法1: 标准写入
                try:
                    logger.info(f"尝试标准写入方式")
                    with open(results_file, "w", encoding="utf-8") as f:
                        f.write(json_result)
                    
                    if os.path.exists(results_file) and os.path.getsize(results_file) > 0:
                        logger.info(f"标准写入成功，文件大小: {os.path.getsize(results_file)} 字节")
                        written = True
                    else:
                        logger.warning("标准写入后文件不存在或为空")
                except Exception as e:
                    logger.error(f"标准写入失败: {str(e)}")
                
                # 方法2: 二进制写入
                if not written:
                    try:
                        logger.info(f"尝试二进制写入方式")
                        with open(results_file, "wb") as f:
                            f.write(json_result.encode('utf-8'))
                        
                        if os.path.exists(results_file) and os.path.getsize(results_file) > 0:
                            logger.info(f"二进制写入成功，文件大小: {os.path.getsize(results_file)} 字节")
                            written = True
                        else:
                            logger.warning("二进制写入后文件不存在或为空")
                    except Exception as e:
                        logger.error(f"二进制写入失败: {str(e)}")
                
                # 方法3: 写入桌面和临时目录
                if not written:
                    try:
                        logger.info("尝试写入多个位置")
                        locations = [
                            os.path.join(os.path.expanduser("~"), "Desktop", "OCR_Results.json"),
                            os.path.join(os.environ.get('TEMP', ''), "OCR_Results.json"),
                            os.path.join(os.getcwd(), "OCR_Results.json")
                        ]
                        
                        for loc in locations:
                            try:
                                loc_dir = os.path.dirname(loc)
                                if not os.path.exists(loc_dir):
                                    os.makedirs(loc_dir, exist_ok=True)
                                
                                logger.info(f"尝试写入位置: {loc}")
                                with open(loc, "w", encoding="utf-8") as f:
                                    f.write(json_result)
                                
                                if os.path.exists(loc) and os.path.getsize(loc) > 0:
                                    logger.info(f"写入成功: {loc}")
                                    
                                    # 尝试复制到原始目标
                                    try:
                                        import shutil
                                        shutil.copy2(loc, results_file)
                                        if os.path.exists(results_file) and os.path.getsize(results_file) > 0:
                                            logger.info(f"成功复制到目标位置: {results_file}")
                                            written = True
                                            break
                                    except Exception as copy_e:
                                        logger.error(f"复制到目标位置失败: {str(copy_e)}")
                            except Exception as loc_e:
                                logger.error(f"写入位置 {loc} 失败: {str(loc_e)}")
                    except Exception as e:
                        logger.error(f"多位置写入失败: {str(e)}")
                
                # 验证最终结果
                if os.path.exists(results_file) and os.path.getsize(results_file) > 0:
                    logger.info(f"结果文件已成功写入: {results_file}, 大小: {os.path.getsize(results_file)} 字节")
                else:
                    logger.critical(f"结果文件写入失败，路径: {results_file}")
                    
                    # 写入错误信息到桌面
                    try:
                        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                        error_file = os.path.join(desktop_path, "OCR_Error.txt")
                        with open(error_file, "w", encoding="utf-8") as f:
                            f.write(f"时间: {datetime.now()}\n")
                            f.write(f"错误: 无法写入结果文件 {results_file}\n")
                            f.write(f"原因: 文件写入后不存在或为空\n")
                        logger.info(f"已将错误信息写入桌面: {error_file}")
                    except Exception as err_e:
                        logger.error(f"写入错误文件也失败: {str(err_e)}")
                    
                    # 最终抛出异常
                    raise Exception(f"结果文件写入失败: {results_file}")
                
            except Exception as e:
                logger.error(f"处理结果文件过程中发生异常: {str(e)}")
                logger.error(traceback.format_exc())
                
                # 写入错误信息
                try:
                    with open(results_file, "w", encoding="utf-8") as f:
                        error_result = [{"error": f"处理过程出错: {str(e)}"}]
                        json.dump(error_result, f, ensure_ascii=False, indent=2)
                    logger.info("错误信息已写入结果文件")
                except Exception as write_error:
                    logger.error(f"写入错误信息也失败: {str(write_error)}")
                
        except Exception as e:
            logger.error(f"处理过程中发生异常: {str(e)}")
            logger.error(traceback.format_exc())
            
            # 写入错误信息
            try:
                with open(results_file, "w", encoding="utf-8") as f:
                    error_result = [{"error": f"处理过程出错: {str(e)}"}]
                    json.dump(error_result, f, ensure_ascii=False, indent=2)
                logger.info("错误信息已写入结果文件")
            except Exception as write_error:
                logger.error(f"写入错误信息也失败: {str(write_error)}")
                
    except Exception as e:
        logger.error(f"主程序执行异常: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    # 通过pyw方式运行或打包成exe时会静默运行
    # 如果您使用PyInstaller打包，请使用以下命令：
    # pyinstaller --noconsole --onefile OCR识别核心.py
    # 或者使用以下命令使用pythonw运行:
    # pythonw OCR识别核心.py 参数文件路径 结果文件路径
    try:
        # 再次尝试隐藏控制台窗口
        if sys.platform == 'win32':
            try:
                import ctypes
                hwnd = ctypes.windll.kernel32.GetConsoleWindow()
                if hwnd:
                    ctypes.windll.user32.ShowWindow(hwnd, 0)
            except:
                pass
        
        main()
    except Exception as e:
        logger.error(f"未捕获的异常: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 保存错误到桌面
        try:
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            error_file = os.path.join(desktop_path, "OCR_Error.txt")
            with open(error_file, "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now()}\n")
                f.write(f"错误: {str(e)}\n\n")
                f.write(traceback.format_exc())
        except:
            pass 