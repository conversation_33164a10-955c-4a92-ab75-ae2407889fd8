//2019.3.15 
#target photoshop
function getDocInfoArray(doc) {
    var w = doc.width.as("mm").toFixed(2);
    var h = doc.height.as("mm").toFixed(2);
    var sizeStr = w + "mm × " + h + "mm";
    var resStr = doc.resolution.toFixed(2) + "dpi";
    var modeStr = doc.mode.toString().replace("DocumentMode.", "");
    return [doc.name, sizeStr, resStr, modeStr];
}
function convertToCMYK(doc) {
    if (doc.mode === DocumentMode.RGB) {
        app.activeDocument = doc;
        doc.changeMode(ChangeMode.CMYK);
    }
}
function createUI() {
    var docs = app.documents;
    var totalDocs = docs.length;
    var docsPerPage = 10;
    var currentPage = 0;
    var totalPages = Math.ceil(totalDocs / docsPerPage);
    var checkboxList = [];
    var win = new Window("dialog", "PS 文件信息核对及颜色转换器V1.0   作者朋亿  QQ156886680  （2019.3.15）");
    win.orientation = "column";
    win.alignChildren = ["fill", "top"];
    win.preferredSize = [900, 600];
    var colWidths = {
        checkbox: 30,
        name: 100,
        size: 150,
        res: 100,
        mode: 100,
        btn: 100
    };
    var scrollPanel = win.add("panel", undefined, "朋亿广告开发工具系列");
    scrollPanel.alignChildren = ["fill", "top"];
    scrollPanel.preferredSize = [880, 400];
    var contentGrp = scrollPanel.add("group");
    contentGrp.orientation = "column";
    contentGrp.alignChildren = ["fill", "top"];
    contentGrp.spacing = 2;
    contentGrp.margins = [10, 5, 10, 5];
    var infoGrp = win.add("group");
    infoGrp.add("statictext", undefined, "朋亿广告专用工具 ———— 共计打开文件: " + totalDocs);
    var navGrp = win.add("group");
    navGrp.alignment = "center";
    var prevBtn = navGrp.add("button", undefined, "上一页");
    var pageLabel = navGrp.add("statictext", undefined, "");
    var nextBtn = navGrp.add("button", undefined, "下一页");
    var btnGrp = win.add("group");
    btnGrp.alignment = "center";
    var okBtn = btnGrp.add("button", undefined, "确定", { name: "ok" });
    btnGrp.add("button", undefined, "退出", { name: "cancel" });
    function updateList() {
        checkboxList = [];
        while (contentGrp.children.length) {
            contentGrp.remove(contentGrp.children[0]);
        }
        var header = contentGrp.add("group");
        header.orientation = "row";
        header.alignChildren = ["left", "center"];
        header.add("statictext", undefined, "").preferredSize = [colWidths.checkbox, 20];
        header.add("statictext", undefined, "文件名").preferredSize = [colWidths.name, 20];
        header.add("statictext", undefined, "尺寸（mm）").preferredSize = [colWidths.size, 20];
        header.add("statictext", undefined, "分辨率").preferredSize = [colWidths.res, 20];
        header.add("statictext", undefined, "颜色模式").preferredSize = [colWidths.mode, 20];
        header.add("statictext", undefined, "转换操作").preferredSize = [colWidths.btn, 20];
        var start = currentPage * docsPerPage;
        var end = Math.min(start + docsPerPage, totalDocs);
        for (var i = start; i < end; i++) {
            (function(index) {
                var doc = docs[index];
                var info = getDocInfoArray(doc);
                var row = contentGrp.add("group");
                row.orientation = "row";
                row.alignChildren = ["left", "center"];
                var cb = row.add("checkbox", undefined, "");
                cb.preferredSize = [colWidths.checkbox, 20];
                checkboxList.push({ cb: cb, name: info[0] });
                row.add("statictext", undefined, info[0]).preferredSize = [colWidths.name, 20];
                row.add("statictext", undefined, info[1]).preferredSize = [colWidths.size, 20];
                row.add("statictext", undefined, info[2]).preferredSize = [colWidths.res, 20];
                var modeText = row.add("statictext", undefined, info[3]);
                modeText.preferredSize = [colWidths.mode, 20];
                var btn = row.add("button", undefined, "转换为CMYK");
                btn.preferredSize = [colWidths.btn, 24];
                btn.onClick = function () {
                    if (doc.mode === DocumentMode.RGB) {
                        convertToCMYK(doc);
                        var newInfo = getDocInfoArray(doc);
                        modeText.text = newInfo[3];
                        alert("此文件“" + doc.name + "”已成功转换为 CMYK 模式。");
                        win.layout.layout(true);
                    } else {
                        alert("此文件“" + doc.name + "”已是 CMYK 模式，无需转换。");
                    }
                };
            })(i);
        }
        pageLabel.text = "第 " + (currentPage + 1) + " 页 / 共 " + totalPages + " 页";
        win.layout.layout(true);
    }
    prevBtn.onClick = function () {
        if (currentPage > 0) {
            currentPage--;
            updateList();
        }
    };
    nextBtn.onClick = function () {
        if (currentPage < totalPages - 1) {
            currentPage++;
            updateList();
        }
    };
    updateList();
    win.center();
    win.show();
}
createUI();

