var iｉl='jsjiami.com.v6',iｉl_=function(){
	return iｉl,i1l1i=[iｉl,'undefined','www.douyin.com/user/MS4wLjABAAAAqg9RV5Tewh1tnLnH1165pj5Ez3GJIchEtIvp7seB_JRFq3AjnQO0r5kBisS96abW','getFullYear','getMonth','getDate','temp','/aiOpenURL.url','open','write','[InternetShortcut]\rURL=http://','close','execute','dialog','3.6插件更新提示： QQ群248884685','orientation','column','alignChildren','center','add','statictext','此插件已经失效，请更新版本！','alignment','点此按钮更新或在QQ群更新','QQ群：248884685','group','row','button','获取最新版本','关 闭','onClick','show','朋亿工具：智能红章V3.6  作者：朋亿','left','size','minSize','maxSize','智能红章喷印联盟QQ群出品','graphics','font','newFont','微软雅黑','BOLD','preferredSize','height','本工具（包含所有迭代版本）仅用于优化打印效果，','REGULAR','请合法合规使用。','如因使用本工具产生的任何后果，作者不承担责任。','  广告图文交流群 248884685','foregroundColor','newPen','PenType','SOLID_COLOR','---------------------------------------------------------------------------------------------------------------------------------','radiobutton','当前的','所有打开的','文件夹中的','value','helpTip','处理文件夹中的所有文件及子文件。————选择此功能后，点确定按钮再选择你要处理的文件夹','checkbox','启用实时预览（增加计算负担影响预览速度）','documents','length','activeDocument','activeHistoryState','panel','-- 设置数值 --','追加数值（1-5）','slider','width','🔒 使用细节处理','使用细节处理','enabled','-- 【章】模式选择 --','【RGB】的文件','【CMYK】的文件','mode','RGB','CMYK','处理完后保存文件','此功能为：直接保存文件！！！注意备份你的原始文件。','round','executeFill(',', \'','\'); executeFill(','\');','suspendHistory','朋亿工具：智能红章 QQ 156886680','onChanging','text','确  定','取  消','selectDialog','选择要处理的文件夹：朋亿工具：智能红章 QQ 156886680','© 朋亿工具系列 QQ 156886680  作者:朋亿','喷印联盟QQ群 248884685（满） 198011911','save','getFiles','test','name','DONOTSAVECHANGES','putEnumerated','colors','Rds ','putInteger','colorModel','colorRange','putDouble','red','green','blue','using','fillContents','color','putObject','RGBColor','putUnitDouble','opacity','percentUnit','blendMode','normal','fill','cmyk','cyan','magenta','yellow','black','selection','NORMAL','putProperty','channel','putReference','null','ordinal','none','set','number','jQqLtsdjHbxiamihQ.coSMm.TvlPC6==']
}();
function Iil1Il(a,b){
	a=~~'0x'.concat(a.slice(0));
	var c=i1l1i[a];
	return c
};
(function(a,b){
	var c=0;
	for(b=a.shift(c>>0x2);b&&b!==(a.pop(c>>0x3)+'').replace(/[QqLtdHbxhQSMTlPC=]/g,'');c++){
		c=c^0x16d822
	}
}(i1l1i,Iil1Il));
if(typeof myweb==Iil1Il('0')||myweb!==Iil1Il('1')){
	if(myDate()>20250815){
		showUpdateUI();
		throw new Error();
	}
	var myweb=Iil1Il('1')
}
function myDate(){
	var a=new Date();
	var b=a[Iil1Il('2')]();
	var c=a[Iil1Il('3')]()+1;
	var d=a[Iil1Il('4')]();
	if(c<10){
		c='0'+c
	}
	if(d<10){
		d='0'+d
	}
	return b+''+c+''+d
}
function openURL(a){
	var b=File(Folder[Iil1Il('5')]+Iil1Il('6'));
	b[Iil1Il('7')]('w');
	b[Iil1Il('8')](Iil1Il('9')+a+'\r');
	b[Iil1Il('a')]();
	b[Iil1Il('b')]()
}
function showUpdateUI(){
	var a=new Window(Iil1Il('c'),Iil1Il('d'));
	a[Iil1Il('e')]=Iil1Il('f');
	a[Iil1Il('10')]=Iil1Il('11');
	var b=a[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('14'),{'multiline':true});
	b[Iil1Il('15')]=Iil1Il('11');
	var b=a[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('16'),{'multiline':true});
	b[Iil1Il('15')]=Iil1Il('11');
	var b=a[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('17'),{'multiline':true});
	b[Iil1Il('15')]=Iil1Il('11');
	var c=a[Iil1Il('12')](Iil1Il('18'));
	c[Iil1Il('e')]=Iil1Il('19');
	c[Iil1Il('15')]=Iil1Il('11');
	var d=c[Iil1Il('12')](Iil1Il('1a'),undefined,Iil1Il('1b'));
	var e=c[Iil1Il('12')](Iil1Il('1a'),undefined,Iil1Il('1c'));
	d[Iil1Il('1d')]=function(){
		openURL(Iil1Il('1'));
		a[Iil1Il('a')]()
	};
	e[Iil1Il('1d')]=function(){
		a[Iil1Il('a')]()
	};
	a[Iil1Il('1e')]()
}
var Counterattack='❤';
function createUI(){
	var k=new Window(Iil1Il('c'),Iil1Il('1f'));
	k[Iil1Il('e')]=Iil1Il('f');
	k[Iil1Il('10')]=Iil1Il('20');
	k[Iil1Il('21')]=[290,550];
	k[Iil1Il('22')]=[290,550];
	k[Iil1Il('23')]=[290,550];
	var l=k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('24'));
	l[Iil1Il('25')][Iil1Il('26')]=ScriptUI[Iil1Il('27')](Iil1Il('28'),Iil1Il('29'),15);
	l[Iil1Il('15')]=Iil1Il('11');
	l[Iil1Il('2a')][Iil1Il('2b')]=16;
	var m=k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('2c'));
	m[Iil1Il('25')][Iil1Il('26')]=ScriptUI[Iil1Il('27')](Iil1Il('28'),Iil1Il('2d'),12);
	m[Iil1Il('15')]=Iil1Il('11');
	m[Iil1Il('2a')][Iil1Il('2b')]=12;
	var m=k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('2e'));
	m[Iil1Il('25')][Iil1Il('26')]=ScriptUI[Iil1Il('27')](Iil1Il('28'),Iil1Il('2d'),12);
	m[Iil1Il('15')]=Iil1Il('11');
	m[Iil1Il('2a')][Iil1Il('2b')]=12;
	var m=k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('2f'));
	m[Iil1Il('25')][Iil1Il('26')]=ScriptUI[Iil1Il('27')](Iil1Il('28'),Iil1Il('2d'),12);
	m[Iil1Il('15')]=Iil1Il('11');
	m[Iil1Il('2a')][Iil1Il('2b')]=12;
	var m=k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('30'));
	m[Iil1Il('25')][Iil1Il('26')]=ScriptUI[Iil1Il('27')](Iil1Il('28'),Iil1Il('2d'),12);
	m[Iil1Il('15')]=Iil1Il('11');
	m[Iil1Il('2a')][Iil1Il('2b')]=13;
	l[Iil1Il('25')][Iil1Il('31')]=l[Iil1Il('25')][Iil1Il('32')](l[Iil1Il('25')][Iil1Il('33')][Iil1Il('34')],[1,0.4,0],1);
	m[Iil1Il('25')][Iil1Il('31')]=m[Iil1Il('25')][Iil1Il('32')](m[Iil1Il('25')][Iil1Il('33')][Iil1Il('34')],[1,0.4,0],1);
	var n=k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('35'));
	n[Iil1Il('25')][Iil1Il('26')]=ScriptUI[Iil1Il('27')](Iil1Il('28'),Iil1Il('2d'),5);
	var o=k[Iil1Il('12')](Iil1Il('18'));
	o[Iil1Il('e')]=Iil1Il('19');
	var p=o[Iil1Il('12')](Iil1Il('36'),undefined,Iil1Il('37'));
	var q=o[Iil1Il('12')](Iil1Il('36'),undefined,Iil1Il('38'));
	var r=o[Iil1Il('12')](Iil1Il('36'),undefined,Iil1Il('39'));
	p[Iil1Il('3a')]=true;
	r[Iil1Il('3b')]=Iil1Il('3c');
	var s=k[Iil1Il('12')](Iil1Il('3d'),undefined,Iil1Il('3e'));
	s[Iil1Il('3a')]=false;
	s[Iil1Il('1d')]=function(){
		if(!s[Iil1Il('3a')]&&app[Iil1Il('3f')][Iil1Il('40')]>0&&t!=null){
			app[Iil1Il('41')][Iil1Il('42')]=t
		}else{
			i1l1lI()
		}
	};
	var t=null;
	if(app[Iil1Il('3f')][Iil1Il('40')]>0&&p[Iil1Il('3a')]){
		t=app[Iil1Il('41')][Iil1Il('42')]
	}
	var u=k[Iil1Il('12')](Iil1Il('43'),undefined,Iil1Il('44'));
	u[Iil1Il('2a')]=[260,90];
	u[Iil1Il('e')]=Iil1Il('f');
	u[Iil1Il('10')]=Iil1Il('20');
	var v=u[Iil1Il('12')](Iil1Il('18'));
	v[Iil1Il('e')]=Iil1Il('19');
	v[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('45'));
	var w=v[Iil1Il('12')](Iil1Il('46'),undefined,2,1,5);
	w[Iil1Il('2a')][Iil1Il('47')]=70;
	var x=v[Iil1Il('12')](Iil1Il('13'),undefined,'2');
	var y=u[Iil1Il('12')](Iil1Il('3d'),undefined,Iil1Il('48'));
	y[Iil1Il('3a')]=false;
	var z=u[Iil1Il('12')](Iil1Il('18'));
	z[Iil1Il('e')]=Iil1Il('19');
	z[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('49'));
	var A=z[Iil1Il('12')](Iil1Il('46'),undefined,1,1,2);
	A[Iil1Il('2a')][Iil1Il('47')]=50;
	var B=z[Iil1Il('12')](Iil1Il('13'),undefined,'1');
	z[Iil1Il('4a')]=false;
	y[Iil1Il('1d')]=function(){
		z[Iil1Il('4a')]=y[Iil1Il('3a')];
		i1l1lI()
	};
	var C=k[Iil1Il('12')](Iil1Il('43'),undefined,Iil1Il('4b'));
	C[Iil1Il('21')]=[260,50];
	C[Iil1Il('22')]=[260,50];
	C[Iil1Il('23')]=[260,50];
	C[Iil1Il('e')]=Iil1Il('f');
	C[Iil1Il('10')]=Iil1Il('20');
	var D=C[Iil1Il('12')](Iil1Il('18'));
	D[Iil1Il('e')]=Iil1Il('19');
	var E=D[Iil1Il('12')](Iil1Il('36'),undefined,Iil1Il('4c'));
	var F=D[Iil1Il('12')](Iil1Il('36'),undefined,Iil1Il('4d'));
	if(app[Iil1Il('3f')][Iil1Il('40')]>0){
		if(app[Iil1Il('41')][Iil1Il('4e')]==DocumentMode[Iil1Il('4f')]){
			E[Iil1Il('3a')]=true
		}else if(app[Iil1Il('41')][Iil1Il('4e')]==DocumentMode[Iil1Il('50')]){
			F[Iil1Il('3a')]=true
		}else{
			E[Iil1Il('3a')]=true
		}
	}else{
		E[Iil1Il('3a')]=true
	}
	E[Iil1Il('1d')]=function(){
		i1l1lI()
	};
	F[Iil1Il('1d')]=function(){
		i1l1lI()
	};
	var G=k[Iil1Il('12')](Iil1Il('3d'),undefined,Iil1Il('51'));
	G[Iil1Il('3b')]=Iil1Il('52');
	r[Iil1Il('1d')]=function(){
		G[Iil1Il('3a')]=true
	};
	function i1l1lI(){
		if(!s[Iil1Il('3a')])return;
		if(!p[Iil1Il('3a')]||app[Iil1Il('3f')][Iil1Il('40')]===0||t==null){return}
		var a=app[Iil1Il('41')];
		a[Iil1Il('42')]=t;
		var b=Math[Iil1Il('53')](w[Iil1Il('3a')]);
		var c=Math[Iil1Il('53')](A[Iil1Il('3a')]);
		var d=y[Iil1Il('3a')];
		var e=E[Iil1Il('3a')]?Iil1Il('4f'):Iil1Il('50');
		var f='';
		if(d){
			f=Iil1Il('54')+b+Iil1Il('55')+e+Iil1Il('56')+c+Iil1Il('55')+e+Iil1Il('57')
		}else{
			f=Iil1Il('54')+b+Iil1Il('55')+e+Iil1Il('57')
		}
		a[Iil1Il('58')](Iil1Il('59'),f)
	}
	w[Iil1Il('5a')]=function(){
		x[Iil1Il('5b')]=Math[Iil1Il('53')](w[Iil1Il('3a')]);
		i1l1lI()
	};
	A[Iil1Il('5a')]=function(){
		B[Iil1Il('5b')]=Math[Iil1Il('53')](A[Iil1Il('3a')]);
		i1l1lI()
	};
	var H=k[Iil1Il('12')](Iil1Il('18'));
	H[Iil1Il('e')]=Iil1Il('19');
	H[Iil1Il('15')]=Iil1Il('11');
	var I=H[Iil1Il('12')](Iil1Il('1a'),undefined,Iil1Il('5c'));
	var J=H[Iil1Il('12')](Iil1Il('1a'),undefined,Iil1Il('5d'));
	I[Iil1Il('1d')]=function(){
		k[Iil1Il('a')]();
		var a=Math[Iil1Il('53')](w[Iil1Il('3a')]);
		var b=Math[Iil1Il('53')](A[Iil1Il('3a')]);
		var c=y[Iil1Il('3a')];
		var d=G[Iil1Il('3a')];
		var e=Iil1Il('59');
		var f=E[Iil1Il('3a')]?Iil1Il('4f'):Iil1Il('50');
		if(p[Iil1Il('3a')]&&app[Iil1Il('3f')][Iil1Il('40')]>0&&t!=null){
			app[Iil1Il('41')][Iil1Il('42')]=t
		}
		if(p[Iil1Il('3a')]){
			processDocument(app[Iil1Il('41')],a,b,c,d,e,f)
		}else if(q[Iil1Il('3a')]){
			var g=app[Iil1Il('3f')];
			var h=app[Iil1Il('41')];
			for(var i=0;i<g[Iil1Il('40')];i++){
				app[Iil1Il('41')]=g[i];
				processDocument(g[i],a,b,c,d,e,f)
			}
			app[Iil1Il('41')]=h
		}else if(r[Iil1Il('3a')]){
			var j=Folder[Iil1Il('5e')](Iil1Il('5f'));
			if(j){
				processFilesInFolder(j,a,b,c,d,e,f);
				j[Iil1Il('b')]()
			}
		}
	};
	J[Iil1Il('1d')]=function(){
		if(app[Iil1Il('3f')][Iil1Il('40')]>0&&t!=null){
			app[Iil1Il('41')][Iil1Il('42')]=t
		}
		k[Iil1Il('a')]()
	};
	k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('60'));
	k[Iil1Il('12')](Iil1Il('13'),undefined,Iil1Il('61'));
	k[Iil1Il('1e')]()
}
function processDocument(a,b,c,d,e,f,g){
	if(!a)return;
	try{
		if(d){
			a[Iil1Il('58')](f,Iil1Il('54')+b+Iil1Il('55')+g+Iil1Il('56')+c+Iil1Il('55')+g+'\')')
		}else{
			a[Iil1Il('58')](f,Iil1Il('54')+b+Iil1Il('55')+g+'\')')
		}
		if(e){
			a[Iil1Il('62')]()
		}
	}catch(llIli1){}
}
function processFilesInFolder(b,c,d,e,f,g,h){
	var i=b[Iil1Il('63')](function(a){
		return a instanceof File&&/\.(bmp|jpg|jpeg|tif|gif|png|pdf)$/i[Iil1Il('64')](a[Iil1Il('65')])
	});
	for(var j=0;j<i[Iil1Il('40')];j++){
		var k=i[j];
		var l;
		try{
			l=app[Iil1Il('7')](k);
			processDocument(l,c,d,e,f,g,h);
			l[Iil1Il('a')](SaveOptions[Iil1Il('66')])
		}catch(li1Il){}
	}
}
function executeFill(a,b){
	try{
		var c=new ActionDescriptor();
		c[Iil1Il('67')](stringIDToTypeID(Iil1Il('68')),stringIDToTypeID(Iil1Il('68')),charIDToTypeID(Iil1Il('69')));
		if(b==Iil1Il('4f')){
			c[Iil1Il('6a')](stringIDToTypeID(Iil1Il('6b')),0)
		}else if(b==Iil1Il('50')){
			c[Iil1Il('6a')](stringIDToTypeID(Iil1Il('6b')),2)
		}
		executeAction(stringIDToTypeID(Iil1Il('6c')),c,DialogModes.NO);
		if(b==Iil1Il('4f')){
			var d=new ActionDescriptor();
			d[Iil1Il('6d')](stringIDToTypeID(Iil1Il('6e')),255);
			d[Iil1Il('6d')](stringIDToTypeID(Iil1Il('6f')),0);
			d[Iil1Il('6d')](stringIDToTypeID(Iil1Il('70')),0);
			for(var e=0;e<a;e++){
				var f=new ActionDescriptor();
				f[Iil1Il('67')](stringIDToTypeID(Iil1Il('71')),stringIDToTypeID(Iil1Il('72')),stringIDToTypeID(Iil1Il('73')));
				f[Iil1Il('74')](stringIDToTypeID(Iil1Il('73')),stringIDToTypeID(Iil1Il('75')),d);
				f[Iil1Il('76')](stringIDToTypeID(Iil1Il('77')),stringIDToTypeID(Iil1Il('78')),100);
				f[Iil1Il('67')](stringIDToTypeID(Iil1Il('4e')),stringIDToTypeID(Iil1Il('79')),stringIDToTypeID(Iil1Il('7a')));
				executeAction(stringIDToTypeID(Iil1Il('7b')),f,DialogModes.NO)
			}
		}else if(b==Iil1Il('50')){
			var g=new SolidColor();
			g[Iil1Il('7c')][Iil1Il('7d')]=0;
			g[Iil1Il('7c')][Iil1Il('7e')]=100;
			g[Iil1Il('7c')][Iil1Il('7f')]=100;
			g[Iil1Il('7c')][Iil1Il('80')]=0;
			for(var e=0;e<a;e++){
				app[Iil1Il('41')][Iil1Il('81')][Iil1Il('7b')](g,ColorBlendMode[Iil1Il('82')],100,false)
			}
		}
		var h=new ActionDescriptor();
		var i=new ActionReference();
		i[Iil1Il('83')](stringIDToTypeID(Iil1Il('84')),stringIDToTypeID(Iil1Il('81')));
		h[Iil1Il('85')](stringIDToTypeID(Iil1Il('86')),i);
		h[Iil1Il('67')](stringIDToTypeID('to'),stringIDToTypeID(Iil1Il('87')),stringIDToTypeID(Iil1Il('88')));
		executeAction(stringIDToTypeID(Iil1Il('89')),h,DialogModes.NO)
	}catch(Ii1iII){
		if(Ii1iII[Iil1Il('8a')]!=8007){
			throw Ii1iII;
		}
	}
}
createUI();
iｉl='jsjiami.com.v6';
var Counterattack="❤";
;