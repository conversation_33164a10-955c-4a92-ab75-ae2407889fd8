const fs = require('fs');

// 尝试修复不完整的代码
function fixIncompleteCode(code) {
  // 确保代码以分号结束
  let fixedCode = code.trim();
  if (!fixedCode.endsWith(';') && !fixedCode.endsWith('}')) {
    fixedCode += ';';
  }
  
  // 尝试平衡括号
  const openParens = (fixedCode.match(/\(/g) || []).length;
  const closeParens = (fixedCode.match(/\)/g) || []).length;
  if (openParens > closeParens) {
    fixedCode += ')'.repeat(openParens - closeParens);
  }
  
  return fixedCode;
}

// 提取eval中的内容而不执行它
function extractEvalContent(code) {
  try {
    // 查找eval模式
    const evalPattern = /eval\s*\(\s*(function\s*\([^)]*\)\s*\{[\s\S]*?\})\s*\(\s*([^)]*)\s*\)\s*\)/g;
    let match;
    let extractedCode = code;
    
    // 循环替换所有eval
    while ((match = evalPattern.exec(extractedCode)) !== null) {
      const fullMatch = match[0];
      const functionPart = match[1];
      const paramsPart = match[2];
      
      // 替换eval为函数内容
      const replacement = `/* EVAL提取 */ (${functionPart})(${paramsPart})`;
      extractedCode = extractedCode.replace(fullMatch, replacement);
    }
    
    // 如果没有找到eval模式，尝试更宽松的模式
    if (extractedCode === code && code.includes('eval(')) {
      const looseEvalPattern = /eval\s*\(\s*([\s\S]*?)\s*\)/g;
      while ((match = looseEvalPattern.exec(extractedCode)) !== null) {
        const fullMatch = match[0];
        const content = match[1];
        
        // 替换eval为内容
        const replacement = `/* EVAL内容 */ ${content}`;
        extractedCode = extractedCode.replace(fullMatch, replacement);
      }
    }
    
    return extractedCode;
  } catch (error) {
    console.error(`提取eval内容时出错: ${error.message}`);
    return code;
  }
}

// 提取JSJiami V6特征
function extractJSJiamiV6(code) {
  try {
    // 查找JSJiami V6特征模式
    if (code.includes('jsjiami.com.v6') || code.includes('JiaMi')) {
      console.log('检测到JSJiami V6特征');
      
      // 提取字符串数组
      const stringArrayPattern = /['"]([^'"]*?)['"],\s*['"]([^'"]*?)['"],\s*['"]([^'"]*?)['"],/g;
      let stringArrayMatch;
      let stringArray = [];
      
      while ((stringArrayMatch = stringArrayPattern.exec(code)) !== null) {
        for (let i = 1; i < stringArrayMatch.length; i++) {
          if (stringArrayMatch[i]) {
            stringArray.push(stringArrayMatch[i]);
          }
        }
      }
      
      if (stringArray.length > 0) {
        console.log(`提取到${stringArray.length}个字符串`);
        fs.writeFileSync('string_array.json', JSON.stringify(stringArray, null, 2));
      }
      
      // 提取解密函数
      const decryptFunctionPattern = /function\s+([a-zA-Z0-9_$]+)\s*\([^)]*\)\s*\{\s*[^}]*?return[^}]*?\}/g;
      let decryptFunctionMatch;
      let decryptFunctions = [];
      
      while ((decryptFunctionMatch = decryptFunctionPattern.exec(code)) !== null) {
        decryptFunctions.push(decryptFunctionMatch[0]);
      }
      
      if (decryptFunctions.length > 0) {
        console.log(`提取到${decryptFunctions.length}个可能的解密函数`);
        fs.writeFileSync('decrypt_functions.js', decryptFunctions.join('\n\n'));
      }
    }
    
    return code;
  } catch (error) {
    console.error(`提取JSJiami V6特征时出错: ${error.message}`);
    return code;
  }
}

// 主函数处理文件输入/输出
function main() {
  if (process.argv.length < 3) {
    console.log('用法: node robust_decoder.js <输入文件> [输出文件]');
    return;
  }

  const inputFile = process.argv[2];
  const outputFile = process.argv[3] || 'decoded.js';

  try {
    console.log(`读取文件: ${inputFile}`);
    let code = fs.readFileSync(inputFile, 'utf8');
    
    // 1. 修复不完整的代码
    console.log('修复不完整的代码...');
    code = fixIncompleteCode(code);
    
    // 2. 提取eval内容
    console.log('提取eval内容...');
    const extractedCode = extractEvalContent(code);
    
    // 3. 提取JSJiami V6特征
    console.log('提取JSJiami V6特征...');
    extractJSJiamiV6(extractedCode);
    
    // 4. 保存处理后的代码
    fs.writeFileSync(outputFile, extractedCode);
    console.log(`处理后的代码已保存到 ${outputFile}`);
    
    // 5. 保存原始代码的美化版本
    const beautifiedCode = extractedCode
      .replace(/\\x([0-9A-Fa-f]{2})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)))
      .replace(/\\u([0-9A-Fa-f]{4})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)));
    
    fs.writeFileSync('beautified_' + outputFile, beautifiedCode);
    console.log(`美化后的代码已保存到 beautified_${outputFile}`);
  } catch (error) {
    console.error(`错误: ${error.message}`);
  }
}

main();
