// 创建初始快照
createSnapshot("Original State");

// 创建对话框
var win = new Window("dialog", "填充颜色");

// 添加单选按钮组
var group = win.add("group");
var radioNone = group.add("radiobutton", undefined, "无");
var radioRed = group.add("radiobutton", undefined, "红色");
var radioBlue = group.add("radiobutton", undefined, "蓝色");
var radioGreen = group.add("radiobutton", undefined, "绿色");

// 设置默认选择为“无”
radioNone.value = true;

// 为每个单选按钮绑定点击事件
radioNone.onClick = function() {
    revertToSnapshot("Original State");
    app.refresh(); // 强制刷新视图
};

radioRed.onClick = function() {
    fillWithColor(255, 0, 0);
    app.refresh(); // 强制刷新视图
};

radioBlue.onClick = function() {
    fillWithColor(0, 0, 255);
    app.refresh(); // 强制刷新视图
};

radioGreen.onClick = function() {
    fillWithColor(0, 255, 0);
    app.refresh(); // 强制刷新视图
};

// 添加“确定”按钮
var btnOK = win.add("button", undefined, "确定");
btnOK.onClick = function() {
    win.close();
};

// 显示对话框
win.center(); // 居中显示
win.show();

// 函数定义
// 创建快照
function createSnapshot(name) {
    var idMk = charIDToTypeID("Mk  ");
    var desc = new ActionDescriptor();
    var idnull = charIDToTypeID("null");
    var ref = new ActionReference();
    var idSnpS = charIDToTypeID("SnpS");
    ref.putClass(idSnpS);
    desc.putReference(idnull, ref);
    var idFrom = charIDToTypeID("From");
    var ref2 = new ActionReference();
    var idHstS = charIDToTypeID("HstS");
    var idCrnH = charIDToTypeID("CrnH");
    ref2.putProperty(idHstS, idCrnH);
    desc.putReference(idFrom, ref2);
    var idNm = charIDToTypeID("Nm  ");
    desc.putString(idNm, name);
    executeAction(idMk, desc, DialogModes.NO);
}

// 恢复到指定快照
function revertToSnapshot(name) {
    var idslct = charIDToTypeID("slct");
    var desc = new ActionDescriptor();
    var idnull = charIDToTypeID("null");
    var ref = new ActionReference();
    var idSnpS = charIDToTypeID("SnpS");
    ref.putName(idSnpS, name);
    desc.putReference(idnull, ref);
    executeAction(idslct, desc, DialogModes.NO);
}

// 填充当前图层为指定颜色
function fillWithColor(r, g, b) {
    var idFl = charIDToTypeID("Fl  ");
    var desc = new ActionDescriptor();
    var idUsng = charIDToTypeID("Usng");
    var idFlCn = charIDToTypeID("FlCn");
    var idClr = charIDToTypeID("Clr ");
    var desc2 = new ActionDescriptor();
    var idRd = charIDToTypeID("Rd  ");
    desc2.putDouble(idRd, r);
    var idGrn = charIDToTypeID("Grn ");
    desc2.putDouble(idGrn, g);
    var idBl = charIDToTypeID("Bl  ");
    desc2.putDouble(idBl, b);
    var idRGBC = charIDToTypeID("RGBC");
    desc.putObject(idClr, idRGBC, desc2);
    desc.putEnumerated(idUsng, idFlCn, idClr);
    executeAction(idFl, desc, DialogModes.NO);
}