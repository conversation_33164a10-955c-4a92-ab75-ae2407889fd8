var _0x362fe9 = _0x747a;
(function (_0x5c0692, _0x495e13) {
    var _0x2ffa34 = _0x747a, _0x19893e = _0x5c0692();
    while (!![]) {
        try {
            var _0x2deb1d = parseInt(_0x2ffa34(0x2cf)) / (0x25e7 + -0x417 + -0x21cf) + -parseInt(_0x2ffa34(0x19b)) / (0x9 * -0x3c5 + 0x1 * -0x23d1 + 0x45c0) * (parseInt(_0x2ffa34(0x1a7)) / (-0x22d9 + -0x37 * -0xb3 + -0x399 * 0x1)) + parseInt(_0x2ffa34(0x248)) / (0x138 * -0xe + -0x49 * -0x20 + -0x7f4 * -0x1) * (-parseInt(_0x2ffa34(0x28c)) / (0x2588 + -0x8c6 * -0x4 + 0x489b * -0x1)) + -parseInt(_0x2ffa34(0x25d)) / (0x1793 + 0x79a * 0x3 + -0x2e5b) * (-parseInt(_0x2ffa34(0x1b7)) / (-0x25a1 + 0x2 * 0xb1b + 0xf72)) + -parseInt(_0x2ffa34(0x1a5)) / (-0x1875 + 0x1 * -0x150d + -0x2 * -0x16c5) + -parseInt(_0x2ffa34(0x13e)) / (-0x1a79 + -0x15e * -0x13 + 0x8 * 0x11) + -parseInt(_0x2ffa34(0x1e5)) / (0x1882 + -0x33c + -0x153c) * (-parseInt(_0x2ffa34(0x16c)) / (0x663 + 0xb3 * -0xf + -0x425 * -0x1));
            if (_0x2deb1d === _0x495e13)
                break;
            else
                _0x19893e['push'](_0x19893e['shift']());
        } catch (_0x2369f8) {
            _0x19893e['push'](_0x19893e['shift']());
        }
    }
}(_0x4a86, -0x3 * 0x6df3 + 0x1 * 0x3f4c3 + -0x1 * -0x18235));
function _0x4a86() {
    var _0x3dec0e = [
        'CLhfZ',
        'name',
        'layer',
        'MCvYa',
        'kind',
        '替换完成:\x20',
        'djrYx',
        'qhuau',
        'join',
        'main()',
        'rance出品',
        'left',
        'eETwX',
        '此操作将影响\x20',
        'Cjilp',
        'split',
        'QlbYf',
        '验证失败，程序将退出',
        'zZbAP',
        'zBUZX',
        'CrDTI',
        'pqQNB',
        '290572MrPhxt',
        'dialog',
        'tGYpi',
        'text',
        'KdPCJ',
        'cCkJM',
        'AZzyS',
        'cked',
        'open',
        '开头于',
        'abuEJ',
        '正则表达式',
        'FZsQm',
        'putIdentif',
        'yEHCH',
        'write',
        'pzBTT',
        'lggLI',
        'successCou',
        'temp',
        'vHvdK',
        '6MBTQpE',
        'DLESk',
        '请先打开一个文档！',
        'wUile',
        'Djjpl',
        'OBeUw',
        'TEkhD',
        '8884685',
        'spacing',
        'PJPIc',
        '文本批量替换\x20V1.',
        '0|7|6|8|5|',
        'pzLBI',
        'kfLLo',
        'nwDdE',
        '84685】中更新版',
        'q/mtN87EDP',
        'width',
        '：248884685',
        'kefxc',
        'column',
        'zCFQW',
        'pJSRS',
        'FcpSh',
        'checkbox',
        '文本查找替换',
        '替换部分完成。成功:',
        'Lyr\x20',
        'bftWF',
        'iHDDW',
        'gSjXF',
        'zmHmW',
        'YcHJB',
        'WkMdI',
        '操作选项:',
        'XayNk',
        'row',
        '...\x20以及其他\x20',
        '/aiOpenURL',
        'tsHsq',
        'yuDYP',
        'TeUyn',
        'PHWKy',
        'gkNzk',
        'bZpIG',
        'VdpWJ',
        'edittext',
        '15xsloUH',
        'KIYKw',
        'BAPjt',
        'oNXqy',
        'tPHse',
        '\x20个文档中的\x20',
        'getObjectV',
        '隐藏图层',
        'rqETt',
        'perseveran',
        'TEXT',
        'guvLB',
        '喷印联盟验证信息',
        'ENjGG',
        'rABfZ',
        'RIQuV',
        '处理图层\x20',
        'selection',
        'cimaP',
        'orientatio',
        'VZZbn',
        'LtPkK',
        '错误信息:\x0a',
        'Lfxru',
        '248884685',
        'pumNS',
        'YThyh',
        'replace',
        'bwzUi',
        'xfENl',
        'read',
        '喷印联盟★广告图文技',
        'tPixelsLoc',
        'totalDocs',
        'getFullYea',
        'zxDZu',
        'ohWsE',
        'lYIGl',
        'MzxjT',
        '文本替换操作',
        'indexOf',
        'aPOfP',
        'center',
        'Tglix',
        'WSbMx',
        'EXnnx',
        '。请查看日志了解详情',
        'MNFBm',
        'bLmCE',
        '全词匹配',
        'docIndex',
        'JQEvV',
        'active',
        'getDate',
        '术与开发者探索交流群',
        '成功替换数:\x20',
        'skippedLay',
        'onClick',
        'matchedLay',
        'HgNMl',
        '未找到匹配项，无需替',
        '查找:',
        'allLocked',
        'PdDPc',
        '密码错误，请重试！',
        'everance',
        'FiOzk',
        '292468UUnsWc',
        'contents',
        'bXScn',
        'ment',
        '正则表达式错误:\x20',
        'SOgRd',
        'test',
        'GWrOS',
        'kTtHN',
        'OxAZW',
        'YJUda',
        'bKImn',
        'AMTNs',
        '喷印联盟QQ群:24',
        '/TextPhoto',
        'ybDpi',
        'lnzLV',
        'fHEJj',
        'by\x20恒心-pers',
        'tKBJS',
        'group',
        'qzAUz',
        'LayerSet',
        'RL=http://',
        'locked',
        'length',
        '0|4',
        '替换成功完成！共替换',
        'NeOqu',
        '3543417wucIvo',
        '操作日志',
        'qm.qq.com/',
        'gWmep',
        'Ktfpp',
        'FjaAO',
        'userData',
        'visible',
        'tUmcz',
        'QKDQS',
        'documents',
        '5|1|2|3|6|',
        '3|1|4|0|2',
        'top',
        '查找和替换',
        '\x20处文本吗？\x0a',
        'pixelsLock',
        '\x5c$&',
        'RluyY',
        '4|9|3|1|2',
        'rVKja',
        'yKKan',
        'panel',
        'YnfdV',
        'multiline',
        'fuwgU',
        'fill',
        'PrXVx',
        'create',
        'exists',
        'eBzXe',
        '无法修改图层文本:\x20',
        '0|2|3|1|4',
        'textItem',
        'push',
        'iplCj',
        'show',
        'vGUJg',
        'dZNvR',
        'Asrdi',
        'JXEnf',
        'dkeKA',
        'IzlKg',
        '创建验证文件失败:\x20',
        'Adobe\x20Phot',
        '请使用Photosh',
        '1782egcfuh',
        'CwMDk',
        'LrHMs',
        'VXZLp',
        'pvVsJ',
        'QQ群',
        'DCCES',
        'MchUF',
        'min',
        'bAsxS',
        '扫描图层数:\x20',
        '失败替换数:\x20',
        'ZtfUN',
        '处理范围:',
        'hasKey',
        'transparen',
        'oqciY',
        'close',
        'FupSt',
        'string',
        'erty',
        'fzgDJ',
        'height',
        'gdSIq',
        'preferredS',
        'YmVyt',
        'EwHbV',
        'activeDocu',
        'alignment',
        'LnYCZ',
        'ZZXAg',
        'return\x20',
        '恒心-perseve',
        'NJIJJ',
        ',\x20失败:\x20',
        '\x20>\x20',
        '如没有密钥可进QQ群',
        'oshop',
        '排除隐藏图层',
        '\x20时出错:\x20',
        'ePhAI',
        '处理文档数:\x20',
        'errors',
        'dropdownli',
        'GPjNR',
        '0|1|3|4|2',
        'qdtem',
        '46yiLqfd',
        'AFlSn',
        '密码:',
        'BZtLS',
        'hortcut]\x0dU',
        'suspendHis',
        'lastIndexO',
        'characters',
        '\x20→\x20',
        '请在【QQ群2488',
        '669432hRcSmn',
        'ers',
        '16797ZOvUiX',
        '12|2|7|1|5',
        'cMrVI',
        'EMgaf',
        'ier',
        '所有文档',
        'ren',
        'gljfR',
        '区分大小写',
        'SOxDl',
        'YJNIE',
        'COrSd',
        '请输入密码验证',
        '[InternetS',
        'TmnRD',
        '此脚本插件已经失效，',
        '34181zVWrUE',
        '匹配图层数:\x20',
        'WAIhz',
        'shop.json',
        'zEvmk',
        '当前文档',
        '处理文档\x20',
        'ATrlE',
        'RgUyp',
        'object',
        '替换:',
        '\x20个图层。',
        'rRyNB',
        'IawiJ',
        '输入密钥方可使用此工',
        '预览结果:\x20',
        'LoAns',
        'getBoolean',
        'DMEnS',
        'nyASd',
        '1|6|5',
        '1|3|5|0|4|',
        'BxUdn',
        'uqlyt',
        'BoNip',
        'op运行此脚本！',
        'alignChild',
        '|3|11|0|13',
        'cPkCE',
        'add',
        'qsBsT',
        'IEAIT',
        'tory',
        'hasOwnProp',
        'rUkDq',
        '2|7|3|4|0|',
        'fVlKd',
        '跳过图层数:\x20',
        'lEjnS',
        'layerLocki',
        '.url',
        'zKOWU',
        'WyBTw',
        'radiobutto',
        'failCount',
        'KFawP',
        '49410sQeYPf',
        'substring',
        'statictext',
        'alue',
        '请输入要查找的文本！',
        'value',
        'POwZu',
        'OdeHE',
        'getMonth',
        '\x20个图层\x0a',
        'UnUox',
        'poZTQ',
        'QeugR',
        'margins',
        'MlvmU',
        'cGbsH',
        '未找到匹配项。\x0a',
        'execute',
        'JfQBO',
        'pfAtf',
        'protectAll',
        'nnhvt',
        'yXpoY',
        'LWJsz',
        '\x20个错误\x0a',
        'OsZec',
        'ked',
        'layers',
        'docName',
        'ednLh',
        'LnHsZ',
        'beFjp',
        'kxeEz',
        'rduAn',
        'IPpyJ',
        'ize',
        'hdeni',
        '\x20处文本。',
        'YpfNa',
        'yVRWM',
        'idtby',
        '无法解锁图层:\x20',
        'ncDlo',
        'typename',
        'ijKJv',
        'MhLZk',
        'FEpGP',
        'lSTJN',
        '|4|10|9|8|',
        'index',
        '/Adobe/Log',
        'AZDdw',
        'VHesC',
        '2.4\x20by\x20恒心-',
        'totalLayer',
        '匹配图层:\x0a',
        'jxoQO',
        'LBdaE',
        'JsJZL',
        '结尾于',
        'qyLMi',
        '\x20[正则表达式错误]',
        'OlUFD',
        'toLowerCas',
        'NkRGN',
        'message',
        'button',
        '匹配模式:',
        'apgKk',
        'wBsDw',
        'cVKaq',
        'xCddD',
        '\x20-\x20',
        'BoQpe',
        'uwJLM',
        '确认替换\x20',
        'positionLo'
    ];
    _0x4a86 = function () {
        return _0x3dec0e;
    };
    return _0x4a86();
}
function verifyUser() {
    var _0x36a83e = _0x747a, _0xd62678 = {
            'lSTJN': function (_0x1ca7c3, _0x55724e) {
                return _0x1ca7c3 < _0x55724e;
            },
            'yVRWM': _0x36a83e(0x171),
            'Ktfpp': _0x36a83e(0x2a4),
            'iHDDW': _0x36a83e(0x295) + 'ce',
            'CLhfZ': function (_0x4a3865, _0x21e906) {
                return _0x4a3865 !== _0x21e906;
            },
            'TEkhD': _0x36a83e(0x149) + _0x36a83e(0x13b),
            'COrSd': function (_0xf7ef6c, _0x1e53d) {
                return _0xf7ef6c + _0x1e53d;
            },
            'gSjXF': _0x36a83e(0x2dd) + _0x36a83e(0x1ba),
            'ednLh': function (_0x430f4a, _0x33e2a0) {
                return _0x430f4a(_0x33e2a0);
            },
            'MNFBm': _0x36a83e(0x18c) + _0x36a83e(0x23c),
            'VdpWJ': function (_0xed4357, _0x54e686) {
                return _0xed4357(_0x54e686);
            },
            'Lfxru': function (_0x2d65fa, _0x1b47e5) {
                return _0x2d65fa + _0x1b47e5;
            },
            'ZZXAg': _0x36a83e(0x169),
            'ENjGG': _0x36a83e(0x2cc),
            'MCvYa': _0x36a83e(0x16a) + _0x36a83e(0x191),
            'nnhvt': function (_0x260513, _0x4c97b0) {
                return _0x260513(_0x4c97b0);
            },
            'OlUFD': _0x36a83e(0x16b) + _0x36a83e(0x1d0),
            'idtby': function (_0x422100, _0x525de8) {
                return _0x422100 > _0x525de8;
            },
            'BAPjt': function (_0x21a3fc) {
                return _0x21a3fc();
            },
            'rABfZ': _0x36a83e(0x1b6) + _0x36a83e(0x1a4) + _0x36a83e(0x26c) + '本！',
            'PHWKy': _0x36a83e(0x140) + _0x36a83e(0x26d) + 'PM',
            'zBUZX': _0x36a83e(0x217) + 's',
            'apgKk': _0x36a83e(0x298),
            'gljfR': function (_0x2abb21, _0xc26a04) {
                return _0x2abb21 === _0xc26a04;
            },
            'RIQuV': _0x36a83e(0x2dc) + _0x36a83e(0x264),
            'cGbsH': _0x36a83e(0x249),
            'kefxc': _0x36a83e(0x1b3),
            'FjaAO': _0x36a83e(0x271),
            'CrDTI': _0x36a83e(0x2b6),
            'OxAZW': _0x36a83e(0x2e3),
            'yXpoY': _0x36a83e(0x281),
            'wBsDw': _0x36a83e(0x1e7),
            'fuwgU': _0x36a83e(0x19d),
            'LWJsz': function (_0x533f90, _0x17c404) {
                return _0x533f90 < _0x17c404;
            },
            'eBzXe': _0x36a83e(0x28b),
            'pzBTT': _0x36a83e(0x227),
            'nwDdE': _0x36a83e(0x1c5) + '具',
            'xCddD': _0x36a83e(0x190) + '获得'
        };
    if (_0xd62678[_0x36a83e(0x232)](app[_0x36a83e(0x233)], _0xd62678[_0x36a83e(0x235)]))
        return _0xd62678[_0x36a83e(0x1fa)](alert, _0xd62678[_0x36a83e(0x223)]), ![];
    if (_0xd62678[_0x36a83e(0x20d)](_0xd62678[_0x36a83e(0x28e)](myDate), -0x1 * -0x11fb085 + -0x3ca022 * -0x8 + -0x1cfaf3c))
        return _0xd62678[_0x36a83e(0x202)](alert, _0xd62678[_0x36a83e(0x29a)]), _0xd62678[_0x36a83e(0x28a)](openURL, _0xd62678[_0x36a83e(0x287)]), ![];
    var _0x257d73 = _0xd62678[_0x36a83e(0x1b2)](Folder[_0x36a83e(0x144)], _0xd62678[_0x36a83e(0x245)]), _0x5cc878 = new File(_0xd62678[_0x36a83e(0x2a3)](_0x257d73, _0xd62678[_0x36a83e(0x27b)]));
    if (_0x5cc878[_0x36a83e(0x15b)]) {
        _0x5cc878[_0x36a83e(0x250)]('r');
        var _0x2c9aff = _0x5cc878[_0x36a83e(0x2aa)]();
        _0x5cc878[_0x36a83e(0x17d)]();
        try {
            var _0x128d45 = _0xd62678[_0x36a83e(0x202)](parseJSON, _0x2c9aff);
            if (_0x128d45[_0xd62678[_0x36a83e(0x229)]] && _0xd62678[_0x36a83e(0x1ae)](_0x128d45[_0xd62678[_0x36a83e(0x229)]][_0xd62678[_0x36a83e(0x29b)]], _0xd62678[_0x36a83e(0x2bb)]))
                return !![];
        } catch (_0x9da590) {
        }
    }
    var _0x48db05 = new Window(_0xd62678[_0x36a83e(0x1f4)], _0xd62678[_0x36a83e(0x270)], undefined);
    _0x48db05[_0x36a83e(0x29f) + 'n'] = _0xd62678[_0x36a83e(0x143)], _0x48db05[_0x36a83e(0x1d1) + _0x36a83e(0x1ad)] = _0xd62678[_0x36a83e(0x246)];
    var _0x44dc45 = _0x48db05[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x2d8)]);
    _0x44dc45[_0x36a83e(0x29f) + 'n'] = _0xd62678[_0x36a83e(0x1fb)], _0x44dc45[_0x36a83e(0x1d1) + _0x36a83e(0x1ad)] = _0xd62678[_0x36a83e(0x246)], _0x44dc45[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x22a)], undefined, _0xd62678[_0x36a83e(0x157)]);
    var _0x3a757a = [];
    for (var _0x19385d = -0x915 + -0x22c + -0x43 * -0x2b; _0xd62678[_0x36a83e(0x1fc)](_0x19385d, -0x235c + -0x3cd * -0x3 + 0x17fa); _0x19385d++) {
        var _0x51d04c = _0x44dc45[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x15c)], undefined, '', { 'password': !![] });
        _0x51d04c[_0x36a83e(0x1a2)] = -0x1293 + -0x359 + 0x74f * 0x3, _0x51d04c[_0x36a83e(0x184) + _0x36a83e(0x208)][_0x36a83e(0x26e)] = -0x1552 + -0x2534 + 0x3aa4, _0x3a757a[_0x36a83e(0x160)](_0x51d04c);
    }
    var _0x397e2a = _0x48db05[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x2d8)]), _0x121eca = _0x397e2a[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x258)], undefined, '取消'), _0x594504 = _0x397e2a[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x258)], undefined, '确定', { 'name': 'ok' });
    _0x48db05[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x22a)], undefined, _0xd62678[_0x36a83e(0x26b)]), _0x48db05[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x22a)], undefined, _0xd62678[_0x36a83e(0x22c)]), _0x48db05[_0x36a83e(0x1d4)](_0xd62678[_0x36a83e(0x22a)], undefined, _0xd62678[_0x36a83e(0x29b)]);
    var _0x143e06 = ![];
    return _0x594504[_0x36a83e(0x2c5)] = function () {
        var _0x30cfce = _0x36a83e, _0x53afc2 = '';
        for (var _0xf6fe80 = 0x214f * -0x1 + -0xc68 + -0xf3d * -0x3; _0xd62678[_0x30cfce(0x214)](_0xf6fe80, _0x3a757a[_0x30cfce(0x13a)]); _0xf6fe80++) {
            _0x53afc2 += _0x3a757a[_0xf6fe80][_0x30cfce(0x24b)];
        }
        var _0xaa7af = [
                '喷印',
                '联盟',
                _0xd62678[_0x30cfce(0x20c)],
                _0xd62678[_0x30cfce(0x142)],
                _0xd62678[_0x30cfce(0x27a)]
            ], _0xf8de93 = !![];
        for (var _0xf6fe80 = 0x23 * -0x84 + -0x4 * 0x499 + -0x6a * -0x58; _0xd62678[_0x30cfce(0x214)](_0xf6fe80, _0xaa7af[_0x30cfce(0x13a)]); _0xf6fe80++) {
            if (_0xd62678[_0x30cfce(0x232)](_0x3a757a[_0xf6fe80][_0x30cfce(0x24b)], _0xaa7af[_0xf6fe80])) {
                _0xf8de93 = ![];
                break;
            }
        }
        if (_0xf8de93) {
            _0x143e06 = !![];
            try {
                var _0x144857 = _0xd62678[_0x30cfce(0x263)][_0x30cfce(0x241)]('|'), _0x4dc019 = 0x1298 + 0x2 * 0xd4c + -0x2d30;
                while (!![]) {
                    switch (_0x144857[_0x4dc019++]) {
                    case '0':
                        _0x4198ad[_0x30cfce(0x257)](_0x239dc7);
                        continue;
                    case '1':
                        !_0x43ad7d[_0x30cfce(0x15b)] && _0x43ad7d[_0x30cfce(0x15a)]();
                        continue;
                    case '2':
                        var _0x4198ad = new File(_0xd62678[_0x30cfce(0x1b2)](_0x257d73, _0xd62678[_0x30cfce(0x27b)]));
                        continue;
                    case '3':
                        _0x4198ad[_0x30cfce(0x250)]('w');
                        continue;
                    case '4':
                        _0x4198ad[_0x30cfce(0x17d)]();
                        continue;
                    case '5':
                        var _0x43ad7d = new Folder(_0x257d73);
                        continue;
                    case '6':
                        var _0x239dc7 = _0xd62678[_0x30cfce(0x202)](stringifyJSON, { '喷印联盟验证信息': { '喷印联盟QQ群:248884685': _0xd62678[_0x30cfce(0x2bb)] } });
                        continue;
                    }
                    break;
                }
            } catch (_0x518602) {
                _0xd62678[_0x30cfce(0x28a)](alert, _0xd62678[_0x30cfce(0x2a3)](_0xd62678[_0x30cfce(0x18a)], _0x518602));
            }
            _0x48db05[_0x30cfce(0x17d)]();
        } else
            _0xd62678[_0x30cfce(0x28a)](alert, _0xd62678[_0x30cfce(0x299)]);
    }, _0x121eca[_0x36a83e(0x2c5)] = function () {
        var _0x4c069e = _0x36a83e;
        _0x48db05[_0x4c069e(0x17d)]();
    }, _0x48db05[_0x36a83e(0x2b6)](), _0x48db05[_0x36a83e(0x162)](), _0x143e06;
}
function myDate() {
    var _0x1d66ca = _0x747a, _0x541e3c = {
            'abuEJ': _0x1d66ca(0x15e),
            'oNXqy': function (_0x2020da, _0x57c16b) {
                return _0x2020da + _0x57c16b;
            },
            'uqlyt': function (_0x1a7137, _0x3fc39b) {
                return _0x1a7137 + _0x3fc39b;
            },
            'KFawP': function (_0x2cd425, _0x3f94d2) {
                return _0x2cd425 * _0x3f94d2;
            },
            'KdPCJ': function (_0xbee173, _0x53ee16) {
                return _0xbee173 * _0x53ee16;
            }
        }, _0x459989 = _0x541e3c[_0x1d66ca(0x252)][_0x1d66ca(0x241)]('|'), _0x28a6bd = -0x3d5 * -0x9 + -0x270a + 0x48d;
    while (!![]) {
        switch (_0x459989[_0x28a6bd++]) {
        case '0':
            var _0x481d81 = new Date();
            continue;
        case '1':
            var _0x44f808 = _0x481d81[_0x1d66ca(0x2c1)]();
            continue;
        case '2':
            var _0x384610 = _0x481d81[_0x1d66ca(0x2ae) + 'r']();
            continue;
        case '3':
            var _0x113b64 = _0x541e3c[_0x1d66ca(0x28f)](_0x481d81[_0x1d66ca(0x1ed)](), -0xf3d * -0x1 + -0x6d * -0x1 + -0xfa9);
            continue;
        case '4':
            return _0x541e3c[_0x1d66ca(0x28f)](_0x541e3c[_0x1d66ca(0x1ce)](_0x541e3c[_0x1d66ca(0x1e4)](_0x384610, -0x459c + -0x1087 * -0x1 + -0x3 * -0x1eb7), _0x541e3c[_0x1d66ca(0x24c)](_0x113b64, 0xc67 + -0x421 + 0x1 * -0x7e2)), _0x44f808);
        }
        break;
    }
}
function openURL(_0x2f24b8) {
    var _0x3c736c = _0x747a, _0x2f6878 = {
            'djrYx': _0x3c736c(0x199),
            'JXEnf': function (_0x1a29a8, _0xad2e04) {
                return _0x1a29a8(_0xad2e04);
            },
            'kxeEz': function (_0x4bd5cf, _0x1cc17c) {
                return _0x4bd5cf + _0x1cc17c;
            },
            'TeUyn': _0x3c736c(0x283) + _0x3c736c(0x1df),
            'zZbAP': function (_0x13a67f, _0x74919e) {
                return _0x13a67f + _0x74919e;
            },
            'NeOqu': function (_0x39c997, _0x2d4dbd) {
                return _0x39c997 + _0x2d4dbd;
            },
            'Tglix': _0x3c736c(0x1b4) + _0x3c736c(0x19f) + _0x3c736c(0x2e6)
        }, _0x5128cc = _0x2f6878[_0x3c736c(0x238)][_0x3c736c(0x241)]('|'), _0x8228a2 = -0x26b1 + 0x7ae + -0x11 * -0x1d3;
    while (!![]) {
        switch (_0x5128cc[_0x8228a2++]) {
        case '0':
            var _0x48a471 = _0x2f6878[_0x3c736c(0x166)](File, _0x2f6878[_0x3c736c(0x205)](Folder[_0x3c736c(0x25b)], _0x2f6878[_0x3c736c(0x286)]));
            continue;
        case '1':
            _0x48a471[_0x3c736c(0x250)]('w');
            continue;
        case '2':
            _0x48a471[_0x3c736c(0x1f6)]();
            continue;
        case '3':
            _0x48a471[_0x3c736c(0x257)](_0x2f6878[_0x3c736c(0x244)](_0x2f6878[_0x3c736c(0x13d)](_0x2f6878[_0x3c736c(0x2b7)], _0x2f24b8), '\x0d'));
            continue;
        case '4':
            _0x48a471[_0x3c736c(0x17d)]();
            continue;
        }
        break;
    }
}
function parseJSON(_0x3b41cb) {
    var _0x11d39c = _0x747a, _0x2ef361 = {
            'KIYKw': function (_0x53e31a, _0x37d17c) {
                return _0x53e31a + _0x37d17c;
            },
            'uwJLM': _0x11d39c(0x18b)
        };
    return new Function(_0x2ef361[_0x11d39c(0x28d)](_0x2ef361[_0x11d39c(0x22f)], _0x3b41cb))();
}
function stringifyJSON(_0x4aa481) {
    var _0x430f18 = _0x747a, _0x32d104 = {
            'zxDZu': function (_0x5d4825, _0x1baada) {
                return _0x5d4825 === _0x1baada;
            },
            'tUmcz': _0x430f18(0x1c0),
            'dkeKA': function (_0x51c544, _0x5015f3) {
                return _0x51c544(_0x5015f3);
            },
            'rRyNB': _0x430f18(0x17f),
            'wUile': function (_0x39d78c, _0x460cff) {
                return _0x39d78c + _0x460cff;
            },
            'cCkJM': function (_0x5f5b65, _0x2d9d79) {
                return _0x5f5b65 + _0x2d9d79;
            },
            'fVlKd': function (_0x2bca92, _0x19206b) {
                return _0x2bca92 + _0x19206b;
            }
        }, _0x4453ae = [];
    for (var _0x5b889c in _0x4aa481) {
        if (_0x4aa481[_0x430f18(0x1d8) + _0x430f18(0x180)](_0x5b889c)) {
            var _0x344e08 = _0x4aa481[_0x5b889c];
            if (_0x32d104[_0x430f18(0x2af)](typeof _0x344e08, _0x32d104[_0x430f18(0x146)]))
                _0x344e08 = _0x32d104[_0x430f18(0x167)](stringifyJSON, _0x344e08);
            else
                _0x32d104[_0x430f18(0x2af)](typeof _0x344e08, _0x32d104[_0x430f18(0x1c3)]) && (_0x344e08 = _0x32d104[_0x430f18(0x260)](_0x32d104[_0x430f18(0x260)]('\x22', _0x344e08[_0x430f18(0x2a7)](/"/g, '\x5c\x22')), '\x22'));
            _0x4453ae[_0x430f18(0x160)](_0x32d104[_0x430f18(0x260)](_0x32d104[_0x430f18(0x260)](_0x32d104[_0x430f18(0x260)]('\x22', _0x5b889c), '\x22:'), _0x344e08));
        }
    }
    return _0x32d104[_0x430f18(0x24d)](_0x32d104[_0x430f18(0x1db)]('{', _0x4453ae[_0x430f18(0x23a)](',')), '}');
}
if (!verifyUser())
    alert(_0x362fe9(0x243) + '！');
else
    try {
        app[_0x362fe9(0x187) + _0x362fe9(0x2d2)][_0x362fe9(0x1a0) + _0x362fe9(0x1d7)](_0x362fe9(0x276), _0x362fe9(0x23b));
    } catch (_0x1ca109) {
        main();
    }
function _0x747a(_0xc6efd, _0x5d5aa5) {
    var _0x3f675c = _0x4a86();
    return _0x747a = function (_0x1576c9, _0x23ad9d) {
        _0x1576c9 = _0x1576c9 - (-0x73 * 0x2 + 0xae3 * 0x1 + -0x8c4);
        var _0x28755f = _0x3f675c[_0x1576c9];
        return _0x28755f;
    }, _0x747a(_0xc6efd, _0x5d5aa5);
}
function main() {
    var _0xe175e4 = _0x362fe9, _0x4bd370 = {
            'pzLBI': _0xe175e4(0x268) + _0xe175e4(0x151),
            'tPHse': function (_0x476b25) {
                return _0x476b25();
            },
            'OBeUw': function (_0x5b2482, _0x1f8f32) {
                return _0x5b2482 > _0x1f8f32;
            },
            'ijKJv': _0xe175e4(0x21c),
            'OsZec': function (_0xc93ba7, _0x136447) {
                return _0xc93ba7 < _0x136447;
            },
            'zKOWU': function (_0x2e8fd6, _0x313765) {
                return _0x2e8fd6(_0x313765);
            },
            'FupSt': function (_0x46538c, _0x3fe854) {
                return _0x46538c + _0x3fe854;
            },
            'vHvdK': function (_0x1b4419, _0x254424) {
                return _0x1b4419 + _0x254424;
            },
            'LrHMs': function (_0x174925, _0x413e68) {
                return _0x174925 + _0x413e68;
            },
            'WkMdI': function (_0x5ddc7c, _0x20fc61) {
                return _0x5ddc7c + _0x20fc61;
            },
            'LtPkK': _0xe175e4(0x18f),
            'poZTQ': _0xe175e4(0x1a3),
            'pvVsJ': function (_0x5b0c3f, _0x4c70a1) {
                return _0x5b0c3f > _0x4c70a1;
            },
            'cPkCE': function (_0x5a68bc, _0x55abd0) {
                return _0x5a68bc + _0x55abd0;
            },
            'guvLB': _0xe175e4(0x282),
            'yuDYP': function (_0x23aa20, _0x531a0b) {
                return _0x23aa20 - _0x531a0b;
            },
            'rVKja': _0xe175e4(0x1ee),
            'Asrdi': _0xe175e4(0x1f5),
            'EwHbV': function (_0x8991b4, _0x277296) {
                return _0x8991b4 + _0x277296;
            },
            'ePhAI': _0xe175e4(0x1dc),
            'LBdaE': function (_0x5551f9, _0x15b3ff) {
                return _0x5551f9 + _0x15b3ff;
            },
            'BoNip': _0xe175e4(0x176),
            'EXnnx': function (_0x183d6e, _0x4dfdae) {
                return _0x183d6e + _0x4dfdae;
            },
            'nyASd': _0xe175e4(0x195),
            'JsJZL': _0xe175e4(0x1c6),
            'bftWF': function (_0x4506be, _0x39e456) {
                return _0x4506be + _0x39e456;
            },
            'aPOfP': function (_0x5c830f, _0xd24430) {
                return _0x5c830f + _0xd24430;
            },
            'WAIhz': _0xe175e4(0x1b8),
            'pqQNB': _0xe175e4(0x1da) + _0xe175e4(0x1cb),
            'YnfdV': function (_0x372bf6, _0x2f1704) {
                return _0x372bf6 === _0x2f1704;
            },
            'JfQBO': function (_0x3bcdea, _0x496465) {
                return _0x3bcdea + _0x496465;
            },
            'Cjilp': _0xe175e4(0x222),
            'LnYCZ': function (_0x135111, _0x29c9c6) {
                return _0x135111 + _0x29c9c6;
            },
            'bAsxS': function (_0xb9b691, _0x43764a) {
                return _0xb9b691 + _0x43764a;
            },
            'GPjNR': function (_0x16ba6d, _0x126f83) {
                return _0x16ba6d(_0x126f83);
            },
            'qdtem': function (_0x5cf364, _0x30ad6a) {
                return _0x5cf364 === _0x30ad6a;
            },
            'ohWsE': function (_0x57036a, _0x2b1f5f) {
                return _0x57036a + _0x2b1f5f;
            },
            'IEAIT': function (_0x5d029c, _0x325241) {
                return _0x5d029c === _0x325241;
            },
            'RluyY': function (_0x1c1cbb, _0x1a92b9) {
                return _0x1c1cbb - _0x1a92b9;
            },
            'ATrlE': function (_0x2e0a6f, _0xd71bf0) {
                return _0x2e0a6f - _0xd71bf0;
            },
            'FZsQm': _0xe175e4(0x1a8) + _0xe175e4(0x1d2) + _0xe175e4(0x215) + '6',
            'Djjpl': _0xe175e4(0x237),
            'fHEJj': function (_0x3cc937, _0x1b4c21) {
                return _0x3cc937 + _0x1b4c21;
            },
            'ZtfUN': function (_0x51289f, _0x705ba3) {
                return _0x51289f + _0x705ba3;
            },
            'eETwX': _0xe175e4(0x230),
            'UnUox': _0xe175e4(0x14d),
            'GWrOS': function (_0x512ff3, _0x22ccee) {
                return _0x512ff3 === _0x22ccee;
            },
            'LnHsZ': function (_0x4ce0ce, _0x1d63df) {
                return _0x4ce0ce + _0x1d63df;
            },
            'gdSIq': _0xe175e4(0x2c3),
            'beFjp': _0xe175e4(0x23f),
            'CwMDk': _0xe175e4(0x291),
            'qhuau': _0xe175e4(0x1c2),
            'YcHJB': function (_0x18aef4, _0x40e05b) {
                return _0x18aef4 === _0x40e05b;
            },
            'vGUJg': _0xe175e4(0x13c) + '\x20',
            'cVKaq': _0xe175e4(0x20a),
            'pfAtf': _0xe175e4(0x277) + '\x20',
            'bXScn': _0xe175e4(0x18e),
            'BxUdn': _0xe175e4(0x2ba) + '。',
            'bZpIG': _0xe175e4(0x2c8) + '换。',
            'pumNS': function (_0x4a53db, _0x3b2424) {
                return _0x4a53db > _0x3b2424;
            },
            'LoAns': _0xe175e4(0x2a2),
            'iplCj': function (_0x589074, _0x448804) {
                return _0x589074 + _0x448804;
            },
            'MlvmU': function (_0x2d1eba, _0x567c42) {
                return _0x2d1eba + _0x567c42;
            },
            'YThyh': function (_0x15d937, _0x830b16) {
                return _0x15d937 > _0x830b16;
            },
            'ncDlo': function (_0x1d9f84, _0x227229) {
                return _0x1d9f84 + _0x227229;
            },
            'gWmep': _0xe175e4(0x1fd),
            'EMgaf': function (_0x4ab3a4, _0x1063a6) {
                return _0x4ab3a4 + _0x1063a6;
            },
            'qzAUz': _0xe175e4(0x177),
            'NJIJJ': function (_0x211436, _0xe59c45) {
                return _0x211436 + _0xe59c45;
            },
            'bLmCE': function (_0x505d93, _0x4e53a4) {
                return _0x505d93 === _0x4e53a4;
            },
            'NkRGN': function (_0x468759, _0x444265) {
                return _0x468759(_0x444265);
            },
            'WSbMx': _0xe175e4(0x1e9),
            'rqETt': function (_0x45d68f, _0x47983f) {
                return _0x45d68f(_0x47983f);
            },
            'FcpSh': _0xe175e4(0x278),
            'DCCES': function (_0xc3f036, _0x5c6f7d) {
                return _0xc3f036(_0x5c6f7d);
            },
            'yKKan': _0xe175e4(0x1de) + 'ng',
            'kTtHN': function (_0x271d0e, _0xb9bf1a) {
                return _0x271d0e(_0xb9bf1a);
            },
            'jxoQO': _0xe175e4(0x1f9),
            'MchUF': function (_0x485879, _0x35e829) {
                return _0x485879 !== _0x35e829;
            },
            'dZNvR': _0xe175e4(0x14a),
            'lnzLV': _0xe175e4(0x1cc) + '2',
            'rUkDq': function (_0xd8e5a2) {
                return _0xd8e5a2();
            },
            'FiOzk': function (_0x49bca3, _0x29ace3) {
                return _0x49bca3 < _0x29ace3;
            },
            'zEvmk': function (_0x24cc66, _0x5a6242) {
                return _0x24cc66 < _0x5a6242;
            },
            'YmVyt': function (_0x571e6a, _0xa6a886, _0x32a217) {
                return _0x571e6a(_0xa6a886, _0x32a217);
            },
            'IPpyJ': function (_0x31ed24, _0x444b17) {
                return _0x31ed24 < _0x444b17;
            },
            'pJSRS': _0xe175e4(0x293),
            'DLESk': function (_0x409a2f, _0xe1dcbe) {
                return _0x409a2f == _0xe1dcbe;
            },
            'ybDpi': function (_0x42e020, _0x1fe074) {
                return _0x42e020(_0x1fe074);
            },
            'BZtLS': function (_0x52e9d9, _0x4ad718) {
                return _0x52e9d9 + _0x4ad718;
            },
            'AZDdw': _0xe175e4(0x2d3),
            'hdeni': function (_0x5b58b2, _0x37b4c9) {
                return _0x5b58b2 + _0x37b4c9;
            },
            'PJPIc': function (_0x3daf26, _0x4a6b2f) {
                return _0x3daf26(_0x4a6b2f);
            },
            'VHesC': function (_0x328b2c, _0x7d9e7d) {
                return _0x328b2c !== _0x7d9e7d;
            },
            'rduAn': function (_0x392de0, _0x440b1f) {
                return _0x392de0(_0x440b1f);
            },
            'zmHmW': function (_0x23e90f, _0x3b5dac) {
                return _0x23e90f + _0x3b5dac;
            },
            'tsHsq': _0xe175e4(0x1bd),
            'lggLI': _0xe175e4(0x193),
            'YJUda': function (_0x4b3637, _0x2dbbdf) {
                return _0x4b3637 < _0x2dbbdf;
            },
            'WyBTw': _0xe175e4(0x2b3),
            'TmnRD': function (_0x1bda77, _0x161ac4) {
                return _0x1bda77(_0x161ac4);
            },
            'bwzUi': function (_0x3bab7c, _0x2f991b) {
                return _0x3bab7c + _0x2f991b;
            },
            'MhLZk': _0xe175e4(0x20e),
            'SOxDl': function (_0x12053a, _0x515e54) {
                return _0x12053a + _0x515e54;
            },
            'zCFQW': _0xe175e4(0x15d),
            'DMEnS': _0xe175e4(0x22d),
            'SOgRd': _0xe175e4(0x29c),
            'VXZLp': _0xe175e4(0x14f),
            'IawiJ': function (_0x551e5e, _0x25b6a3) {
                return _0x551e5e === _0x25b6a3;
            },
            'tGYpi': _0xe175e4(0x2e5),
            'JQEvV': function (_0x150138, _0x4fc37d) {
                return _0x150138(_0x4fc37d);
            },
            'qsBsT': _0xe175e4(0x25f),
            'gkNzk': _0xe175e4(0x249),
            'PrXVx': _0xe175e4(0x267) + _0xe175e4(0x21a) + _0xe175e4(0x295) + 'ce',
            'MzxjT': _0xe175e4(0x271),
            'oqciY': _0xe175e4(0x158),
            'PdDPc': _0xe175e4(0x154),
            'kfLLo': _0xe175e4(0x14c),
            'qyLMi': _0xe175e4(0x23d),
            'FEpGP': _0xe175e4(0x2e3),
            'RgUyp': _0xe175e4(0x281),
            'XayNk': _0xe175e4(0x2b6),
            'bKImn': _0xe175e4(0x1e7),
            'IzlKg': _0xe175e4(0x2c9),
            'yEHCH': _0xe175e4(0x28b),
            'HgNMl': _0xe175e4(0x1c1),
            'AFlSn': _0xe175e4(0x14b),
            'BoQpe': _0xe175e4(0x179),
            'QlbYf': _0xe175e4(0x1e2) + 'n',
            'lEjnS': _0xe175e4(0x1bc),
            'cimaP': _0xe175e4(0x1ac),
            'YpfNa': _0xe175e4(0x228),
            'VZZbn': _0xe175e4(0x197) + 'st',
            'QKDQS': _0xe175e4(0x251),
            'AMTNs': _0xe175e4(0x220),
            'cMrVI': _0xe175e4(0x253),
            'tKBJS': _0xe175e4(0x27f),
            'QeugR': _0xe175e4(0x275),
            'AZzyS': _0xe175e4(0x1af),
            'lYIGl': _0xe175e4(0x2bd),
            'fzgDJ': _0xe175e4(0x192),
            'xfENl': _0xe175e4(0x13f),
            'YJNIE': _0xe175e4(0x227),
            'OdeHE': _0xe175e4(0x2ab) + _0xe175e4(0x2c2) + _0xe175e4(0x26f),
            'POwZu': _0xe175e4(0x2e1) + _0xe175e4(0x2cd)
        };
    if (_0x4bd370[_0xe175e4(0x155)](documents[_0xe175e4(0x13a)], 0x1 * 0x137b + -0xbe + -0x12bd)) {
        _0x4bd370[_0xe175e4(0x2bf)](alert, _0x4bd370[_0xe175e4(0x1d5)]);
        return;
    }
    var _0x13c43a = new Window(_0x4bd370[_0xe175e4(0x288)], _0x4bd370[_0xe175e4(0x159)], undefined, {
        'resizeable': !![],
        'closeButton': !![]
    });
    _0x13c43a[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x13c43a[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x17c)];
    var _0x17e163 = _0x13c43a[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2cb)], undefined, _0x4bd370[_0xe175e4(0x26a)]);
    _0x17e163[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x17e163[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x221)], _0x17e163[_0xe175e4(0x1f2)] = -0xf97 + 0x9 * -0x10d + 0x191b * 0x1;
    var _0x210b5a = _0x17e163[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x210b5a[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x1bf)], _0x210b5a[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x280)], _0x210b5a[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2da)], undefined, _0x4bd370[_0xe175e4(0x168)]);
    var _0x3cf53f = _0x210b5a[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x256)], undefined, '', { 'multiline': !![] });
    _0x3cf53f[_0xe175e4(0x184) + _0xe175e4(0x208)][_0xe175e4(0x26e)] = -0x1 * -0x249b + -0x184f + 0x1 * -0xa8a, _0x3cf53f[_0xe175e4(0x184) + _0xe175e4(0x208)][_0xe175e4(0x182)] = -0x21c7 * 0x1 + 0xf6d * 0x2 + 0x33d;
    var _0x15f834 = _0x17e163[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x15f834[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x1bf)], _0x15f834[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x280)], _0x15f834[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2da)], undefined, _0x4bd370[_0xe175e4(0x2c7)]);
    var _0x59ab18 = _0x15f834[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x256)], undefined, '', { 'multiline': !![] });
    _0x59ab18[_0xe175e4(0x184) + _0xe175e4(0x208)][_0xe175e4(0x26e)] = 0x9a * 0x6 + -0x11d1 * -0x1 + -0x13ab * 0x1, _0x59ab18[_0xe175e4(0x184) + _0xe175e4(0x208)][_0xe175e4(0x182)] = -0xc * -0x188 + 0x1107 + 0xd * -0x2b3;
    var _0x2824f4 = _0x13c43a[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2cb)], undefined, '选项');
    _0x2824f4[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x2824f4[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x17c)], _0x2824f4[_0xe175e4(0x1f2)] = 0x8b9 + -0x2149 + 0x189f;
    var _0x1e2cfc = _0x2824f4[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x1e2cfc[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x1bf)], _0x1e2cfc[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x19c)], _0x1e2cfc[_0xe175e4(0x265)] = -0x3ad * 0x2 + 0x4f + -0x1 * -0x71f;
    var _0x1df62c = _0x1e2cfc[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x1df62c[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x1df62c[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x221)], _0x1df62c[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2da)], undefined, _0x4bd370[_0xe175e4(0x22e)]);
    var _0x240eb3 = _0x1df62c[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x240eb3[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x1bf)];
    var _0x4bb6fd = _0x240eb3[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x242)], undefined, _0x4bd370[_0xe175e4(0x1dd)]), _0x2d9fee = _0x240eb3[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x242)], undefined, _0x4bd370[_0xe175e4(0x29e)]);
    _0x4bb6fd[_0xe175e4(0x1ea)] = !![];
    var _0x3318f9 = _0x1e2cfc[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x3318f9[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x3318f9[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x221)], _0x3318f9[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2da)], undefined, _0x4bd370[_0xe175e4(0x20b)]);
    var _0x252e07 = _0x3318f9[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2a0)], undefined, [
        '包含',
        _0x4bd370[_0xe175e4(0x147)],
        _0x4bd370[_0xe175e4(0x2db)],
        _0x4bd370[_0xe175e4(0x1a9)]
    ]);
    _0x252e07[_0xe175e4(0x29d)] = 0x3bd + 0x6 * -0x40 + -0x23d * 0x1;
    var _0x183f39 = _0x2824f4[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x183f39[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x1bf)], _0x183f39[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x19c)], _0x183f39[_0xe175e4(0x265)] = 0x1 * -0x4be + -0x784 + 0xc56;
    var _0x2e0ddf = _0x183f39[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x2e0ddf[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x2e0ddf[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x221)], _0x2e0ddf[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2da)], undefined, _0x4bd370[_0xe175e4(0x2e2)]);
    var _0x33fa0c = _0x2e0ddf[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x33fa0c[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x1bf)], _0x33fa0c[_0xe175e4(0x265)] = 0x1ff9 * 0x1 + -0x2b * -0x3d + 0x1 * -0x2a2e;
    var _0x2cf4d6 = _0x33fa0c[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x1f1)], undefined, _0x4bd370[_0xe175e4(0x24e)]), _0x14aec0 = _0x33fa0c[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x1f1)], undefined, _0x4bd370[_0xe175e4(0x2b1)]), _0x294123 = _0x33fa0c[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x1f1)], undefined, _0x4bd370[_0xe175e4(0x181)]);
    _0x294123[_0xe175e4(0x1ea)] = !![];
    var _0x17fdf5 = _0x13c43a[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2cb)], undefined, _0x4bd370[_0xe175e4(0x2a9)]);
    _0x17fdf5[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x17fdf5[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x17c)], _0x17fdf5[_0xe175e4(0x1f2)] = -0x17 * -0x18d + -0x266b + -0x1 * -0x2cf;
    var _0x9e0a6d = _0x17fdf5[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x256)], undefined, '', {
        'multiline': !![],
        'readonly': !![]
    });
    _0x9e0a6d[_0xe175e4(0x184) + _0xe175e4(0x208)][_0xe175e4(0x26e)] = -0x1 * 0x1958 + -0x142 * 0xb + -0x418 * -0xa, _0x9e0a6d[_0xe175e4(0x184) + _0xe175e4(0x208)][_0xe175e4(0x182)] = -0x608 + 0x561 + 0xf7 * 0x1;
    var _0x26f35f = _0x13c43a[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x26f35f[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x1bf)], _0x26f35f[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x280)];
    var _0x1cb803 = _0x26f35f[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x1b1)], undefined, '取消'), _0x656fea = _0x26f35f[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x1b1)], undefined, '预览'), _0x223335 = _0x26f35f[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x1b1)], undefined, '替换'), _0x5c2c1d = _0x13c43a[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x213)]);
    _0x5c2c1d[_0xe175e4(0x29f) + 'n'] = _0x4bd370[_0xe175e4(0x2b2)], _0x5c2c1d[_0xe175e4(0x188)] = _0x4bd370[_0xe175e4(0x280)], _0x5c2c1d[_0xe175e4(0x1d1) + _0xe175e4(0x1ad)] = _0x4bd370[_0xe175e4(0x280)];
    var _0x1659a5 = _0x5c2c1d[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2da)], undefined, _0x4bd370[_0xe175e4(0x1ec)]);
    _0x1659a5[_0xe175e4(0x156)] = !![], _0x1659a5[_0xe175e4(0x188)] = _0x4bd370[_0xe175e4(0x280)];
    var _0x5ca692 = _0x5c2c1d[_0xe175e4(0x1d4)](_0x4bd370[_0xe175e4(0x2da)], undefined, _0x4bd370[_0xe175e4(0x1eb)]);
    _0x5ca692[_0xe175e4(0x188)] = _0x4bd370[_0xe175e4(0x280)];
    var _0x2023e4 = {
        'totalDocs': 0x0,
        'totalLayers': 0x0,
        'matchedLayers': [],
        'skippedLayers': []
    };
    _0x656fea[_0xe175e4(0x2c5)] = function () {
        var _0x142882 = _0xe175e4, _0x295383 = _0x4bd370[_0x142882(0x269)][_0x142882(0x241)]('|'), _0x1c2588 = -0x25d7 + 0x47 * -0x36 + 0x34d1 * 0x1;
        while (!![]) {
            switch (_0x295383[_0x1c2588++]) {
            case '0':
                if (!_0x4bd370[_0x142882(0x290)](_0x143938))
                    return;
                continue;
            case '1':
                if (_0x4bd370[_0x142882(0x262)](_0x2023e4[_0x142882(0x2c6) + _0x142882(0x1a6)][_0x142882(0x13a)], 0x2b * -0x9d + -0x2654 + 0x1591 * 0x3)) {
                    _0x226069 += _0x4bd370[_0x142882(0x211)];
                    for (var _0x14fef7 = -0x37e * -0x3 + -0x1 * -0x131c + 0x1d96 * -0x1; _0x4bd370[_0x142882(0x1fe)](_0x14fef7, Math[_0x142882(0x174)](_0x2023e4[_0x142882(0x2c6) + _0x142882(0x1a6)][_0x142882(0x13a)], 0x137 * -0x5 + 0x2311 + -0x1cf9)); _0x14fef7++) {
                        var _0x399b0b = _0x2023e4[_0x142882(0x2c6) + _0x142882(0x1a6)][_0x14fef7], _0x472c06 = _0x399b0b[_0x142882(0x24b)], _0x4eae81 = _0x4bd370[_0x142882(0x1e0)](_0x2e10cc, _0x472c06);
                        _0x226069 += _0x4bd370[_0x142882(0x17e)](_0x4bd370[_0x142882(0x25c)](_0x4bd370[_0x142882(0x17e)](_0x4bd370[_0x142882(0x16e)](_0x4bd370[_0x142882(0x27e)](_0x4bd370[_0x142882(0x25c)]('-\x20', _0x399b0b[_0x142882(0x201)]), _0x4bd370[_0x142882(0x2a1)]), _0x472c06), _0x4bd370[_0x142882(0x1f0)]), _0x4eae81), '\x0a');
                    }
                    _0x4bd370[_0x142882(0x170)](_0x2023e4[_0x142882(0x2c6) + _0x142882(0x1a6)][_0x142882(0x13a)], -0x3 * 0xba7 + -0x1fdf + -0x6d * -0x9d) && (_0x226069 += _0x4bd370[_0x142882(0x1d3)](_0x4bd370[_0x142882(0x25c)](_0x4bd370[_0x142882(0x297)], _0x4bd370[_0x142882(0x285)](_0x2023e4[_0x142882(0x2c6) + _0x142882(0x1a6)][_0x142882(0x13a)], -0x247f * 0x1 + -0x6ed + 0x2b71)), _0x4bd370[_0x142882(0x152)]));
                } else
                    _0x226069 += _0x4bd370[_0x142882(0x165)];
                continue;
            case '2':
                _0x9e0a6d[_0x142882(0x24b)] = _0x226069;
                continue;
            case '3':
                _0x226069 += _0x4bd370[_0x142882(0x186)](_0x4bd370[_0x142882(0x186)](_0x4bd370[_0x142882(0x194)], _0x2023e4[_0x142882(0x2c4) + _0x142882(0x1a6)][_0x142882(0x13a)]), '\x0a\x0a');
                continue;
            case '4':
                _0x226069 += _0x4bd370[_0x142882(0x21e)](_0x4bd370[_0x142882(0x21e)](_0x4bd370[_0x142882(0x1cf)], _0x2023e4[_0x142882(0x21b) + 's']), ',\x20');
                continue;
            case '5':
                _0x226069 += _0x4bd370[_0x142882(0x2b9)](_0x4bd370[_0x142882(0x16e)](_0x4bd370[_0x142882(0x1ca)], _0x2023e4[_0x142882(0x2ad)]), ',\x20');
                continue;
            case '6':
                _0x4bd370[_0x142882(0x1e0)](_0x530c45, !![]);
                continue;
            case '7':
                _0x2023e4 = {
                    'totalDocs': 0x0,
                    'totalLayers': 0x0,
                    'matchedLayers': [],
                    'skippedLayers': []
                };
                continue;
            case '8':
                var _0x226069 = _0x4bd370[_0x142882(0x21f)];
                continue;
            case '9':
                _0x226069 += _0x4bd370[_0x142882(0x279)](_0x4bd370[_0x142882(0x2b5)](_0x4bd370[_0x142882(0x1b9)], _0x2023e4[_0x142882(0x2c6) + _0x142882(0x1a6)][_0x142882(0x13a)]), ',\x20');
                continue;
            }
            break;
        }
    };
    function _0x2e10cc(_0x3adfdc) {
        var _0x2320e0 = _0xe175e4, _0x49778d = _0x4bd370[_0x2320e0(0x247)][_0x2320e0(0x241)]('|'), _0x51a736 = 0x25ab + -0x86 + -0x2525;
        while (!![]) {
            switch (_0x49778d[_0x51a736++]) {
            case '0':
                var _0x21f51c = _0x14aec0[_0x2320e0(0x1ea)];
                continue;
            case '1':
                var _0x3d7e28 = _0x3adfdc;
                continue;
            case '2':
                var _0x12c991 = _0x3cf53f[_0x2320e0(0x24b)];
                continue;
            case '3':
                var _0x4d811e = _0x252e07[_0x2320e0(0x29d)][_0x2320e0(0x216)];
                continue;
            case '4':
                var _0x479860 = _0x2cf4d6[_0x2320e0(0x1ea)];
                continue;
            case '5':
                return _0x3d7e28;
            case '6':
                if (_0x4bd370[_0x2320e0(0x155)](_0x4d811e, -0x13 * 0x27 + -0xc5 * 0x2c + 0x24c4))
                    try {
                        var _0x581ed0 = _0x4bd370[_0x2320e0(0x2b5)](_0x479860 ? '' : 'i', 'g'), _0x2eaecd = new RegExp(_0x12c991, _0x581ed0);
                        _0x3d7e28 = _0x3adfdc[_0x2320e0(0x2a7)](_0x2eaecd, _0x4063d9);
                    } catch (_0x34dd28) {
                        return _0x4bd370[_0x2320e0(0x1f7)](_0x3adfdc, _0x4bd370[_0x2320e0(0x240)]);
                    }
                else {
                    if (_0x21f51c) {
                        var _0x3e4d0e = new RegExp(_0x4bd370[_0x2320e0(0x189)](_0x4bd370[_0x2320e0(0x279)]('\x5cb', _0x4bd370[_0x2320e0(0x1e0)](_0xa7f481, _0x12c991)), '\x5cb'), _0x4bd370[_0x2320e0(0x175)](_0x479860 ? '' : 'i', 'g'));
                        _0x3d7e28 = _0x3adfdc[_0x2320e0(0x2a7)](_0x3e4d0e, _0x4063d9);
                    } else {
                        if (_0x4bd370[_0x2320e0(0x155)](_0x4d811e, -0x1 * 0x195e + 0x1c65 + -0x307)) {
                            var _0x581ed0 = _0x479860 ? 'g' : 'gi', _0x2eaecd = new RegExp(_0x4bd370[_0x2320e0(0x198)](_0xa7f481, _0x12c991), _0x581ed0);
                            _0x3d7e28 = _0x3adfdc[_0x2320e0(0x2a7)](_0x2eaecd, _0x4063d9);
                        } else {
                            if (_0x4bd370[_0x2320e0(0x155)](_0x4d811e, -0x5 * -0x78a + 0x547 + -0x2af8))
                                _0x4bd370[_0x2320e0(0x19a)]((_0x479860 ? _0x3adfdc : _0x3adfdc[_0x2320e0(0x224) + 'e']())[_0x2320e0(0x2b4)](_0x479860 ? _0x12c991 : _0x12c991[_0x2320e0(0x224) + 'e']()), 0xab + -0x2682 + 0x25d7) && (_0x3d7e28 = _0x4bd370[_0x2320e0(0x2b0)](_0x4063d9, _0x3adfdc[_0x2320e0(0x1e6)](_0x12c991[_0x2320e0(0x13a)])));
                            else {
                                if (_0x4bd370[_0x2320e0(0x1d6)](_0x4d811e, -0x4a9 + 0x20c4 + -0x1c19)) {
                                    var _0x16adb7 = _0x479860 ? _0x3adfdc : _0x3adfdc[_0x2320e0(0x224) + 'e'](), _0x2ee42d = _0x479860 ? _0x12c991 : _0x12c991[_0x2320e0(0x224) + 'e']();
                                    _0x4bd370[_0x2320e0(0x1d6)](_0x16adb7[_0x2320e0(0x1a1) + 'f'](_0x2ee42d), _0x4bd370[_0x2320e0(0x150)](_0x16adb7[_0x2320e0(0x13a)], _0x2ee42d[_0x2320e0(0x13a)])) && (_0x3d7e28 = _0x4bd370[_0x2320e0(0x2b9)](_0x3adfdc[_0x2320e0(0x1e6)](-0x1178 * 0x1 + -0x2 * -0x2c9 + 0xbe6, _0x4bd370[_0x2320e0(0x1be)](_0x3adfdc[_0x2320e0(0x13a)], _0x12c991[_0x2320e0(0x13a)])), _0x4063d9));
                                }
                            }
                        }
                    }
                }
                continue;
            case '7':
                var _0x4063d9 = _0x59ab18[_0x2320e0(0x24b)];
                continue;
            }
            break;
        }
    }
    _0x223335[_0xe175e4(0x2c5)] = function () {
        var _0x3787a9 = _0xe175e4, _0x43ad6a = _0x4bd370[_0x3787a9(0x254)][_0x3787a9(0x241)]('|'), _0x459305 = -0x1065 + -0x1c86 + 0x1 * 0x2ceb;
        while (!![]) {
            switch (_0x43ad6a[_0x459305++]) {
            case '0':
                var _0x7e23ee = _0x4bd370[_0x3787a9(0x261)];
                continue;
            case '1':
                var _0x20d036 = _0x4bd370[_0x3787a9(0x2e0)](_0x4bd370[_0x3787a9(0x178)](_0x4bd370[_0x3787a9(0x23e)], _0x2023e4[_0x3787a9(0x2c6) + _0x3787a9(0x1a6)][_0x3787a9(0x13a)]), _0x4bd370[_0x3787a9(0x1ef)]);
                continue;
            case '2':
                _0x4bd370[_0x3787a9(0x2d6)](_0x2023e4[_0x3787a9(0x2c6) + _0x3787a9(0x1a6)][_0x3787a9(0x13a)], 0xaef + -0x220 * -0x11 + -0x2f0f) && _0x4bd370[_0x3787a9(0x198)](_0x530c45, !![]);
                continue;
            case '3':
                if (!_0x4bd370[_0x3787a9(0x198)](confirm, _0x20d036))
                    return;
                continue;
            case '4':
                _0x7e23ee += _0x4bd370[_0x3787a9(0x2e0)](_0x4bd370[_0x3787a9(0x203)](_0x4bd370[_0x3787a9(0x183)], _0x370e6d[_0x3787a9(0x25a) + 'nt']), ',\x20');
                continue;
            case '5':
                _0x20d036 += _0x4bd370[_0x3787a9(0x1d3)](_0x4bd370[_0x3787a9(0x16e)](_0x4bd370[_0x3787a9(0x279)](_0x4bd370[_0x3787a9(0x189)](_0x4bd370[_0x3787a9(0x204)], _0x2023e4[_0x3787a9(0x2ad)]), _0x4bd370[_0x3787a9(0x16d)]), _0x2023e4[_0x3787a9(0x2c6) + _0x3787a9(0x1a6)][_0x3787a9(0x13a)]), _0x4bd370[_0x3787a9(0x239)]);
                continue;
            case '6':
                _0x4bd370[_0x3787a9(0x27d)](_0x370e6d[_0x3787a9(0x1e3)], -0xd * -0x233 + -0xde4 + -0xeb3) ? _0x4bd370[_0x3787a9(0x198)](alert, _0x4bd370[_0x3787a9(0x1d3)](_0x4bd370[_0x3787a9(0x279)](_0x4bd370[_0x3787a9(0x163)], _0x370e6d[_0x3787a9(0x25a) + 'nt']), _0x4bd370[_0x3787a9(0x22b)])) : _0x4bd370[_0x3787a9(0x1e0)](alert, _0x4bd370[_0x3787a9(0x21e)](_0x4bd370[_0x3787a9(0x2b9)](_0x4bd370[_0x3787a9(0x27e)](_0x4bd370[_0x3787a9(0x1f7)](_0x4bd370[_0x3787a9(0x1f8)], _0x370e6d[_0x3787a9(0x25a) + 'nt']), _0x4bd370[_0x3787a9(0x2d1)]), _0x370e6d[_0x3787a9(0x1e3)]), _0x4bd370[_0x3787a9(0x1cd)]));
                continue;
            case '7':
                if (_0x4bd370[_0x3787a9(0x19a)](_0x2023e4[_0x3787a9(0x2c6) + _0x3787a9(0x1a6)][_0x3787a9(0x13a)], 0x1 * 0x10a9 + 0x17bb + -0x2864)) {
                    _0x4bd370[_0x3787a9(0x1e0)](alert, _0x4bd370[_0x3787a9(0x289)]);
                    return;
                }
                continue;
            case '8':
                _0x9e0a6d[_0x3787a9(0x24b)] = _0x7e23ee;
                continue;
            case '9':
                if (_0x4bd370[_0x3787a9(0x2a5)](_0x370e6d[_0x3787a9(0x196)][_0x3787a9(0x13a)], 0x24ba + -0x7 * -0x2db + -0x1 * 0x38b7)) {
                    _0x7e23ee += _0x4bd370[_0x3787a9(0x1c7)];
                    for (var _0x52e44d = 0x1728 + -0x62f * 0x4 + 0x194; _0x4bd370[_0x3787a9(0x1fe)](_0x52e44d, Math[_0x3787a9(0x174)](_0x370e6d[_0x3787a9(0x196)][_0x3787a9(0x13a)], -0x5f7 + -0x2 * 0xceb + 0x1fd0 * 0x1)); _0x52e44d++) {
                        _0x7e23ee += _0x4bd370[_0x3787a9(0x161)](_0x4bd370[_0x3787a9(0x1f3)]('-\x20', _0x370e6d[_0x3787a9(0x196)][_0x52e44d]), '\x0a');
                    }
                    _0x4bd370[_0x3787a9(0x2a6)](_0x370e6d[_0x3787a9(0x196)][_0x3787a9(0x13a)], -0x1953 + 0x1455 + 0x3d * 0x15) && (_0x7e23ee += _0x4bd370[_0x3787a9(0x16e)](_0x4bd370[_0x3787a9(0x20f)](_0x4bd370[_0x3787a9(0x297)], _0x4bd370[_0x3787a9(0x285)](_0x370e6d[_0x3787a9(0x196)][_0x3787a9(0x13a)], 0x2203 + 0x1812 + 0x2 * -0x1d09)), _0x4bd370[_0x3787a9(0x141)]));
                }
                continue;
            case '10':
                _0x7e23ee += _0x4bd370[_0x3787a9(0x1f7)](_0x4bd370[_0x3787a9(0x1aa)](_0x4bd370[_0x3787a9(0x2e4)], _0x370e6d[_0x3787a9(0x1e3)]), '\x0a\x0a');
                continue;
            case '11':
                var _0x370e6d = _0x4bd370[_0x3787a9(0x290)](_0x1f6d6c);
                continue;
            case '12':
                if (!_0x4bd370[_0x3787a9(0x290)](_0x143938))
                    return;
                continue;
            case '13':
                _0x7e23ee += _0x4bd370[_0x3787a9(0x20f)](_0x4bd370[_0x3787a9(0x18d)](_0x4bd370[_0x3787a9(0x1ca)], _0x370e6d[_0x3787a9(0x2ad)]), ',\x20');
                continue;
            }
            break;
        }
    }, _0x1cb803[_0xe175e4(0x2c5)] = function () {
        var _0x58c0c0 = _0xe175e4;
        _0x13c43a[_0x58c0c0(0x17d)]();
    };
    function _0x143938() {
        var _0x4de55b = _0xe175e4;
        if (_0x4bd370[_0x4de55b(0x2bc)](_0x3cf53f[_0x4de55b(0x24b)], ''))
            return _0x4bd370[_0x4de55b(0x225)](alert, _0x4bd370[_0x4de55b(0x2b8)]), _0x3cf53f[_0x4de55b(0x2c0)] = !![], ![];
        return !![];
    }
    function _0x2650e8(_0x501051) {
        var _0x703aa8 = _0xe175e4;
        if (_0x501051[_0x703aa8(0x2ca)] || _0x501051[_0x703aa8(0x14e) + 'ed'] || _0x501051[_0x703aa8(0x231) + _0x703aa8(0x24f)] || _0x501051[_0x703aa8(0x17b) + _0x703aa8(0x2ac) + _0x703aa8(0x1ff)])
            return !![];
        try {
            var _0x551795 = new ActionReference();
            _0x551795[_0x703aa8(0x255) + _0x703aa8(0x1ab)](_0x4bd370[_0x703aa8(0x294)](charIDToTypeID, _0x4bd370[_0x703aa8(0x274)]), _0x501051['id']);
            var _0x27a018 = _0x4bd370[_0x703aa8(0x1e0)](executeActionGet, _0x551795);
            if (_0x27a018[_0x703aa8(0x17a)](_0x4bd370[_0x703aa8(0x172)](stringIDToTypeID, _0x4bd370[_0x703aa8(0x153)]))) {
                var _0x252f84 = _0x27a018[_0x703aa8(0x292) + _0x703aa8(0x1e8)](_0x4bd370[_0x703aa8(0x2d7)](stringIDToTypeID, _0x4bd370[_0x703aa8(0x153)])), _0x206f4f = _0x252f84[_0x703aa8(0x1c8)](_0x4bd370[_0x703aa8(0x198)](stringIDToTypeID, _0x4bd370[_0x703aa8(0x21d)]));
                if (_0x206f4f)
                    return !![];
            }
        } catch (_0x42c444) {
            return !![];
        }
        return ![];
    }
    function _0x1a6672(_0x110a0f) {
        var _0x534c67 = _0xe175e4;
        try {
            return _0x110a0f[_0x534c67(0x2ca)] && (_0x110a0f[_0x534c67(0x2ca)] = ![]), _0x4bd370[_0x534c67(0x173)](_0x110a0f[_0x534c67(0x236)], LayerKind[_0x534c67(0x296)]) && (_0x110a0f[_0x534c67(0x14e) + 'ed'] && (_0x110a0f[_0x534c67(0x14e) + 'ed'] = ![]), _0x110a0f[_0x534c67(0x17b) + _0x534c67(0x2ac) + _0x534c67(0x1ff)] && (_0x110a0f[_0x534c67(0x17b) + _0x534c67(0x2ac) + _0x534c67(0x1ff)] = ![])), _0x110a0f[_0x534c67(0x231) + _0x534c67(0x24f)] && (_0x110a0f[_0x534c67(0x231) + _0x534c67(0x24f)] = ![]), !![];
        } catch (_0x377961) {
            return ![];
        }
    }
    function _0x55dcff(_0x19c800, _0x2dc154) {
        var _0x36d25e = _0xe175e4, _0x2efb9f = _0x4bd370[_0x36d25e(0x164)][_0x36d25e(0x241)]('|'), _0x1fe543 = 0x193e * -0x1 + -0x19f + -0x211 * -0xd;
        while (!![]) {
            switch (_0x2efb9f[_0x1fe543++]) {
            case '0':
                var _0x4e5925 = _0x19c800[_0x36d25e(0x17b) + _0x36d25e(0x2ac) + _0x36d25e(0x1ff)];
                continue;
            case '1':
                var _0x38b665 = _0x19c800[_0x36d25e(0x14e) + 'ed'];
                continue;
            case '2':
                try {
                    var _0x4ab1fb = _0x4bd370[_0x36d25e(0x2df)][_0x36d25e(0x241)]('|'), _0x473245 = 0x394 * 0x9 + -0x9e * -0x2d + -0x3bfa;
                    while (!![]) {
                        switch (_0x4ab1fb[_0x473245++]) {
                        case '0':
                            _0x19c800[_0x36d25e(0x17b) + _0x36d25e(0x2ac) + _0x36d25e(0x1ff)] = ![];
                            continue;
                        case '1':
                            _0x19c800[_0x36d25e(0x2ca)] = ![];
                            continue;
                        case '2':
                            return !![];
                        case '3':
                            _0x19c800[_0x36d25e(0x14e) + 'ed'] = ![];
                            continue;
                        case '4':
                            _0x4bd370[_0x36d25e(0x1d9)](_0x2dc154);
                            continue;
                        case '5':
                            _0x19c800[_0x36d25e(0x231) + _0x36d25e(0x24f)] = ![];
                            continue;
                        }
                        break;
                    }
                } catch (_0xc68c5c) {
                    return ![];
                } finally {
                    try {
                        _0x19c800[_0x36d25e(0x2ca)] = _0x15e6e2, _0x19c800[_0x36d25e(0x14e) + 'ed'] = _0x38b665, _0x19c800[_0x36d25e(0x231) + _0x36d25e(0x24f)] = _0x51d0df, _0x19c800[_0x36d25e(0x17b) + _0x36d25e(0x2ac) + _0x36d25e(0x1ff)] = _0x4e5925;
                    } catch (_0x14ce01) {
                    }
                }
                continue;
            case '3':
                var _0x15e6e2 = _0x19c800[_0x36d25e(0x2ca)];
                continue;
            case '4':
                var _0x51d0df = _0x19c800[_0x36d25e(0x231) + _0x36d25e(0x24f)];
                continue;
            }
            break;
        }
    }
    function _0x530c45(_0x54e46a) {
        var _0x1aab8a = _0xe175e4, _0x4fa13d = _0x3cf53f[_0x1aab8a(0x24b)], _0x378422 = _0x2cf4d6[_0x1aab8a(0x1ea)], _0x5d8a08 = _0x14aec0[_0x1aab8a(0x1ea)], _0x34354a = _0x252e07[_0x1aab8a(0x29d)][_0x1aab8a(0x216)], _0x4ddf5a = _0x294123[_0x1aab8a(0x1ea)], _0x55c010 = [];
        if (_0x4bb6fd[_0x1aab8a(0x1ea)])
            _0x55c010[_0x1aab8a(0x160)](app[_0x1aab8a(0x187) + _0x1aab8a(0x2d2)]);
        else {
            if (_0x2d9fee[_0x1aab8a(0x1ea)])
                for (var _0xe9a2d9 = 0x18a6 + -0xd * 0x132 + -0x91c; _0x4bd370[_0x1aab8a(0x2ce)](_0xe9a2d9, app[_0x1aab8a(0x148)][_0x1aab8a(0x13a)]); _0xe9a2d9++) {
                    _0x55c010[_0x1aab8a(0x160)](app[_0x1aab8a(0x148)][_0xe9a2d9]);
                }
        }
        _0x2023e4[_0x1aab8a(0x2ad)] = _0x55c010[_0x1aab8a(0x13a)];
        for (var _0x45345b = 0x21ee + -0x69e * 0x2 + 0x373 * -0x6; _0x4bd370[_0x1aab8a(0x1bb)](_0x45345b, _0x55c010[_0x1aab8a(0x13a)]); _0x45345b++) {
            var _0x52a02 = _0x55c010[_0x45345b], _0xd753ff = app[_0x1aab8a(0x187) + _0x1aab8a(0x2d2)];
            try {
                app[_0x1aab8a(0x187) + _0x1aab8a(0x2d2)] = _0x52a02;
                var _0x1ee686 = [];
                _0x4bd370[_0x1aab8a(0x185)](_0x108d7d, _0x52a02[_0x1aab8a(0x200)], _0x1ee686), _0x2023e4[_0x1aab8a(0x21b) + 's'] += _0x1ee686[_0x1aab8a(0x13a)];
                for (var _0x5517a0 = -0x216d + 0x1f1a + 0x253; _0x4bd370[_0x1aab8a(0x207)](_0x5517a0, _0x1ee686[_0x1aab8a(0x13a)]); _0x5517a0++) {
                    var _0x2c15e2 = _0x1ee686[_0x5517a0];
                    if (_0x4ddf5a && !_0x2c15e2[_0x1aab8a(0x145)]) {
                        _0x2023e4[_0x1aab8a(0x2c4) + _0x1aab8a(0x1a6)][_0x1aab8a(0x160)]({
                            'name': _0x2c15e2[_0x1aab8a(0x233)],
                            'docName': _0x52a02[_0x1aab8a(0x233)],
                            'reason': _0x4bd370[_0x1aab8a(0x273)]
                        });
                        continue;
                    }
                    var _0x7ea00e = _0x4bd370[_0x1aab8a(0x2d7)](_0x2650e8, _0x2c15e2);
                    if (_0x4bd370[_0x1aab8a(0x25e)](_0x2c15e2[_0x1aab8a(0x236)], LayerKind[_0x1aab8a(0x296)])) {
                        var _0x5db0ed = _0x2c15e2[_0x1aab8a(0x15f)][_0x1aab8a(0x2d0)], _0x5ea5fc = ![];
                        if (_0x4bd370[_0x1aab8a(0x2bc)](_0x34354a, 0x1c5a + 0x20eb + 0x1ea1 * -0x2))
                            try {
                                var _0xd839e9 = _0x378422 ? '' : 'i', _0x583b7a = new RegExp(_0x4fa13d, _0xd839e9);
                                _0x5ea5fc = _0x583b7a[_0x1aab8a(0x2d5)](_0x5db0ed);
                            } catch (_0x5e2a9b) {
                                _0x4bd370[_0x1aab8a(0x2de)](alert, _0x4bd370[_0x1aab8a(0x19e)](_0x4bd370[_0x1aab8a(0x218)], _0x5e2a9b[_0x1aab8a(0x226)]));
                                return;
                            }
                        else {
                            var _0x49cc57 = _0x378422 ? _0x5db0ed : _0x5db0ed[_0x1aab8a(0x224) + 'e'](), _0x5535a3 = _0x378422 ? _0x4fa13d : _0x4fa13d[_0x1aab8a(0x224) + 'e']();
                            if (_0x5d8a08) {
                                var _0x4965f1 = new RegExp(_0x4bd370[_0x1aab8a(0x1f3)](_0x4bd370[_0x1aab8a(0x209)]('\x5cb', _0x4bd370[_0x1aab8a(0x266)](_0xa7f481, _0x5535a3)), '\x5cb'), _0x378422 ? '' : 'i');
                                _0x5ea5fc = _0x4965f1[_0x1aab8a(0x2d5)](_0x49cc57);
                            } else
                                switch (_0x34354a) {
                                case -0x3 * -0x40b + -0x1e53 + 0x1232:
                                    _0x5ea5fc = _0x4bd370[_0x1aab8a(0x219)](_0x49cc57[_0x1aab8a(0x2b4)](_0x5535a3), -(-0x10a3 + -0x2072 + 0x3116));
                                    break;
                                case -0x2 * -0xc07 + 0x1c64 + 0x37f * -0xf:
                                    _0x5ea5fc = _0x4bd370[_0x1aab8a(0x2d6)](_0x49cc57[_0x1aab8a(0x2b4)](_0x5535a3), 0xb * -0x171 + 0x2 * 0xb47 + -0x6b3);
                                    break;
                                case 0x1dc8 + -0x1efa + 0x7 * 0x2c:
                                    _0x5ea5fc = _0x4bd370[_0x1aab8a(0x19a)](_0x49cc57[_0x1aab8a(0x1a1) + 'f'](_0x5535a3), _0x4bd370[_0x1aab8a(0x285)](_0x49cc57[_0x1aab8a(0x13a)], _0x5535a3[_0x1aab8a(0x13a)]));
                                    break;
                                }
                        }
                        _0x5ea5fc && _0x2023e4[_0x1aab8a(0x2c6) + _0x1aab8a(0x1a6)][_0x1aab8a(0x160)]({
                            'layer': _0x2c15e2,
                            'name': _0x2c15e2[_0x1aab8a(0x233)],
                            'docName': _0x52a02[_0x1aab8a(0x233)],
                            'docIndex': _0x45345b,
                            'text': _0x5db0ed,
                            'locked': _0x7ea00e
                        });
                    }
                }
            } catch (_0x588e51) {
                _0x4bd370[_0x1aab8a(0x206)](alert, _0x4bd370[_0x1aab8a(0x2b0)](_0x4bd370[_0x1aab8a(0x27c)](_0x4bd370[_0x1aab8a(0x2e0)](_0x4bd370[_0x1aab8a(0x284)], _0x52a02[_0x1aab8a(0x233)]), _0x4bd370[_0x1aab8a(0x259)]), _0x588e51[_0x1aab8a(0x226)]));
            } finally {
                app[_0x1aab8a(0x187) + _0x1aab8a(0x2d2)] = _0xd753ff;
            }
        }
        return _0x2023e4;
    }
    function _0x1f6d6c() {
        var _0x5054ad = _0xe175e4, _0x22a582 = _0x59ab18[_0x5054ad(0x24b)], _0x4059af = _0x252e07[_0x5054ad(0x29d)][_0x5054ad(0x216)], _0x1bff0f = _0x2cf4d6[_0x5054ad(0x1ea)], _0xa3a9d8 = _0x14aec0[_0x5054ad(0x1ea)], _0x58ddf8 = _0x3cf53f[_0x5054ad(0x24b)], _0x50aea2 = {
                'totalDocs': _0x2023e4[_0x5054ad(0x2ad)],
                'successCount': 0x0,
                'failCount': 0x0,
                'errors': []
            }, _0x380869 = {};
        for (var _0x457a2b = -0xa3 * -0x7 + -0x1 * 0x961 + -0x14 * -0x3f; _0x4bd370[_0x5054ad(0x2d9)](_0x457a2b, _0x2023e4[_0x5054ad(0x2c6) + _0x5054ad(0x1a6)][_0x5054ad(0x13a)]); _0x457a2b++) {
            var _0x39070e = _0x2023e4[_0x5054ad(0x2c6) + _0x5054ad(0x1a6)][_0x457a2b], _0x154b73 = _0x39070e[_0x5054ad(0x234)], _0x322ac0 = _0x39070e[_0x5054ad(0x2be)], _0x15d217 = _0x39070e[_0x5054ad(0x139)];
            try {
                app[_0x5054ad(0x187) + _0x5054ad(0x2d2)] = app[_0x5054ad(0x148)][_0x322ac0];
                !_0x380869[_0x322ac0] && (app[_0x5054ad(0x187) + _0x5054ad(0x2d2)][_0x5054ad(0x1a0) + _0x5054ad(0x1d7)](_0x4bd370[_0x5054ad(0x1e1)], ''), _0x380869[_0x322ac0] = !![]);
                if (_0x15d217) {
                    var _0x2e26f5 = _0x4bd370[_0x5054ad(0x1b5)](_0x1a6672, _0x154b73);
                    if (!_0x2e26f5) {
                        _0x50aea2[_0x5054ad(0x196)][_0x5054ad(0x160)](_0x4bd370[_0x5054ad(0x2a8)](_0x4bd370[_0x5054ad(0x212)], _0x154b73[_0x5054ad(0x233)])), _0x50aea2[_0x5054ad(0x1e3)]++;
                        continue;
                    }
                }
                try {
                    var _0xdd6074 = _0x154b73[_0x5054ad(0x15f)][_0x5054ad(0x2d0)], _0x28f763 = _0x4bd370[_0x5054ad(0x1e0)](_0x2e10cc, _0xdd6074);
                    _0x154b73[_0x5054ad(0x15f)][_0x5054ad(0x2d0)] = _0x28f763, _0x50aea2[_0x5054ad(0x25a) + 'nt']++;
                } catch (_0x1f3b25) {
                    _0x50aea2[_0x5054ad(0x196)][_0x5054ad(0x160)](_0x4bd370[_0x5054ad(0x178)](_0x4bd370[_0x5054ad(0x203)](_0x4bd370[_0x5054ad(0x1b0)](_0x4bd370[_0x5054ad(0x272)], _0x154b73[_0x5054ad(0x233)]), _0x4bd370[_0x5054ad(0x1c9)]), _0x1f3b25[_0x5054ad(0x226)])), _0x50aea2[_0x5054ad(0x1e3)]++;
                }
            } catch (_0x194d0e) {
                _0x50aea2[_0x5054ad(0x196)][_0x5054ad(0x160)](_0x4bd370[_0x5054ad(0x186)](_0x4bd370[_0x5054ad(0x1f7)](_0x4bd370[_0x5054ad(0x20f)](_0x4bd370[_0x5054ad(0x2d4)], _0x39070e[_0x5054ad(0x233)]), _0x4bd370[_0x5054ad(0x259)]), _0x194d0e[_0x5054ad(0x226)])), _0x50aea2[_0x5054ad(0x1e3)]++;
            }
        }
        return _0x50aea2;
    }
    function _0xa7f481(_0x56c232) {
        var _0x155109 = _0xe175e4;
        return _0x56c232[_0x155109(0x2a7)](/[.*+?^${}()|[\]\\]/g, _0x4bd370[_0x155109(0x16f)]);
    }
    function _0x108d7d(_0x1b141c, _0x167cc1) {
        var _0x477c18 = _0xe175e4;
        for (var _0x42b195 = -0x1 * -0x199d + -0xd0a + -0xc93; _0x4bd370[_0x477c18(0x2d9)](_0x42b195, _0x1b141c[_0x477c18(0x13a)]); _0x42b195++) {
            var _0x46d2af = _0x1b141c[_0x42b195];
            _0x167cc1[_0x477c18(0x160)](_0x46d2af), _0x4bd370[_0x477c18(0x1c4)](_0x46d2af[_0x477c18(0x210)], _0x4bd370[_0x477c18(0x24a)]) && _0x4bd370[_0x477c18(0x185)](_0x108d7d, _0x46d2af[_0x477c18(0x200)], _0x167cc1);
        }
    }
    _0x13c43a[_0xe175e4(0x2b6)](), _0x13c43a[_0xe175e4(0x162)]();
}