// 创建对话框
var dlg = new Window('dialog', '图层重命名');
dlg.orientation = 'column';

// 输入框 - 允许用户输入新的名字
dlg.nameInputGroup = dlg.add('group');
dlg.nameInputGroup.add('statictext', undefined, '重命名为:');
var nameInput = dlg.nameInputGroup.add('edittext', [0, 0, 200, 20], '');

// 选择操作类型的选项
dlg.optionsGroup = dlg.add('group');
dlg.optionsGroup.orientation = 'column';

var option1 = dlg.optionsGroup.add('radiobutton', undefined, '只重命名组 (不包括组内的组)');
var option2 = dlg.optionsGroup.add('radiobutton', undefined, '只重命名组 (包括所有组)');
var option3 = dlg.optionsGroup.add('radiobutton', undefined, '根据组内第一个文本图层内容重命名组');
var option4 = dlg.optionsGroup.add('radiobutton', undefined, '根据组内所有文本图层内容拼接重命名组');
var option7 = dlg.optionsGroup.add('radiobutton', undefined, '重命名所有图层');

// 新增编号选项
var numberingGroup = dlg.add('group');
numberingGroup.orientation = 'row';
var numberOption = numberingGroup.add('checkbox', undefined, '编号');
numberOption.value = false;  // 默认未勾选

// 新增方向选择
var numberDirectionGroup = dlg.add('group');
numberDirectionGroup.orientation = 'row';
var numberAscend = numberDirectionGroup.add('radiobutton', undefined, '从上到下编号');
var numberDescend = numberDirectionGroup.add('radiobutton', undefined, '从下到上编号');

// 默认编号方向为从上到下
numberAscend.value = true;

// 初始化编号选择为不可用
numberDirectionGroup.enabled = false;

// 让所有选项互斥
option1.value = true; // 默认选中第一个选项

// 监听是否勾选了编号选项，禁用输入框
option1.onClick = enableInput;
option2.onClick = enableInput;
option3.onClick = enableInput;
option4.onClick = enableInput;
option7.onClick = enableInput;
numberOption.onClick = toggleNumberOptions;

// 重命名按钮
dlg.buttonsGroup = dlg.add('group');
dlg.buttonsGroup.add('button', undefined, '取消');
var renameButton = dlg.buttonsGroup.add('button', undefined, '确认');

// 点击取消时退出脚本
dlg.buttonsGroup.children[0].onClick = function() {
    dlg.close();
};

renameButton.onClick = function () {
    var doc = app.activeDocument;
    var userInput = nameInput.text;

    // 检查是否选择了一个选项
    var renamingResult = [];
    if (option1.value) {
        // 只重命名组（不包括组内的组）
        renamingResult = renameGroup(doc.layers, false, userInput);
    } else if (option2.value) {
        // 只重命名组（包括所有组）
        renamingResult = renameGroup(doc.layers, true, userInput);
    } else if (option3.value) {
        // 根据组内第一个文本图层的内容重命名组
        renamingResult = renameGroupWithFirstTextLayer(doc.layers);
    } else if (option4.value) {
        // 根据组内所有文本图层拼接内容重命名组
        renamingResult = renameGroupWithAllTextLayers(doc.layers);
    } else if (option7.value) {
        // 重命名所有图层（包括组内的图层）
        renamingResult = renameAllLayers(doc.layers, userInput);
    } else {
        alert("请选择一个操作选项！");
        return;
    }

    // 编号
    if (numberOption.value) {
        addNumbering(doc, renamingResult, numberAscend.value);
    }

    dlg.close();
};

// -------------------- 重命名所有图层 --------------------

// 重命名所有图层
function renameAllLayers(layers, newName) {
    var renamedItems = [];
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        if (layer.typename === 'LayerSet') {
            // 对组进行处理
            layer.name = newName;
            renamedItems.push(layer);
            var subRenamedItems = renameAllLayers(layer.layers, newName);  // 递归重命名子图层
            renamedItems = renamedItems.concat(subRenamedItems);
        } else {
            // 对图层进行处理
            layer.name = newName;
            renamedItems.push(layer);  // 记录重命名的图层
        }
    }
    return renamedItems;
}

// 显示对话框
dlg.show();

// -------------------- 函数部分 --------------------

// 获取选中的图层或组
function getSelectedLayers(doc) {
    var selectedLayers = [];
    var layers = doc.layers;

    try {
        if (doc.selection) {
            // 遍历所有图层，检测它们的选择状态
            for (var i = 0; i < layers.length; i++) {
                if (layers[i].selected) {  // 选中的图层或组
                    selectedLayers.push(layers[i]);
                }
            }
        }
    } catch (e) {
        alert("没有选中的图层或组！");
    }
    return selectedLayers;
}

// -------------------- 编号和重命名合并操作 --------------------

// 根据选择的重命名操作来编号
function addNumbering(doc, renamingResult, ascend) {
    var number = ascend ? 1 : renamingResult.length;
    for (var i = 0; i < renamingResult.length; i++) {
        var item = renamingResult[i];
        var paddedNumber = ('00' + number).slice(-3);  // 确保编号为3位数
        item.name = item.name + paddedNumber;
        number = ascend ? number + 1 : number - 1;
    }
}

// -------------------- 重命名相关函数 --------------------

// 递归重命名图层和组
function renameLayersAndGroups(layers, newName, ascend, startNumber) {
    var number = startNumber;
    var renamedItems = [];
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        if (layer.typename === 'LayerSet') {
            // 处理组，递归调用
            if (newName) {
                layer.name = newName;  // 如果新名称不为空，重命名组
            }
            renamedItems.push(layer); // 记录重命名的组
            var subRenamedItems = renameLayersAndGroups(layer.layers, newName, ascend, number);  // 递归重命名子组
            renamedItems = renamedItems.concat(subRenamedItems);
        } else {
            // 处理图层
            if (newName) {
                layer.name = newName;  // 如果新名称不为空，重命名图层
            }
            renamedItems.push(layer);  // 记录重命名的图层
        }
    }
    return renamedItems;
}

// 修复后的重命名组（包括子组）代码
function renameGroup(layers, includeSubGroups, newName) {
    var renamedItems = [];
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        if (layer.typename === 'LayerSet') {
            // 重命名当前组
            layer.name = newName;
            renamedItems.push(layer);

            // 如果需要递归处理子组
            if (includeSubGroups) {
                var subRenamedItems = renameGroup(layer.layers, true, newName);
                renamedItems = renamedItems.concat(subRenamedItems);
            }
        }
    }
    return renamedItems;
}

// 根据组内第一个文本图层内容重命名组
function renameGroupWithFirstTextLayer(layers) {
    var renamedItems = [];
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        if (layer.typename === 'LayerSet') {
            var firstTextLayerName = findFirstTextLayer(layer.layers);
            if (firstTextLayerName) {
                layer.name = firstTextLayerName;
                renamedItems.push(layer);
            }
        }
    }
    return renamedItems;
}

// 根据组内所有文本图层内容拼接重命名组
function renameGroupWithAllTextLayers(layers) {
    var renamedItems = [];
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        if (layer.typename === 'LayerSet') {
            var concatenatedName = concatenateTextLayerNames(layer.layers);
            if (concatenatedName) {
                layer.name = concatenatedName;
                renamedItems.push(layer);
            }
        }
    }
    return renamedItems;
}

// 找到组内第一个文本图层名称
function findFirstTextLayer(layers) {
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        if (layer.kind === LayerKind.TEXT) {
            return layer.textItem.contents;
        }
    }
    return null; // 如果没有文本图层
}

// 拼接组内所有文本图层的名称
function concatenateTextLayerNames(layers) {
    var concatenatedName = '';
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        if (layer.kind === LayerKind.TEXT) {
            concatenatedName += layer.textItem.contents + '_';
        }
    }
    return concatenatedName ? concatenatedName.slice(0, -1) : null;  // 去掉最后一个多余的下划线
}

// -------------------- 输入框和选项控制 --------------------

// 启用或禁用输入框和编号选项
function enableInput() {
    if (option3.value || option4.value) {
        nameInput.enabled = false;  // 禁用输入框
    } else {
        nameInput.enabled = true;   // 启用输入框
    }
    numberDirectionGroup.enabled = numberOption.value;
}

// 切换编号选项时启用/禁用编号方向选择
function toggleNumberOptions() {
    numberDirectionGroup.enabled = numberOption.value;
}

// 显示警告框
function showWarning(message) {
    alert(message);
}
