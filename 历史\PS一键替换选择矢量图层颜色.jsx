#target photoshop

function main() {
    if (!app.documents.length) {
        alert("请先打开一个文档！");
        return;
    }
    
    var doc = app.activeDocument;
    
    var dlg = new Window("dialog", "矢量图形颜色替换工具");
    dlg.orientation = "column";
    dlg.alignChildren = "fill";
    
    // 填充颜色选择部分
    var fillGroup = dlg.add("panel", undefined, "填充颜色替换");
    fillGroup.orientation = "row";
    fillGroup.alignChildren = "left";
    fillGroup.margins = 15;
    
    var useFillCheckbox = fillGroup.add("checkbox", undefined, "启用");
    useFillCheckbox.value = true;
    
    var fillColorGroup = fillGroup.add("group");
    fillColorGroup.orientation = "column";
    
    fillColorGroup.add("statictext", undefined, "填充颜色:");
    var fillColorPanel = fillColorGroup.add("panel", undefined, "");
    fillColorPanel.preferredSize = [100, 20];
    
    var fillColor = [255, 0, 0];
    fillColorPanel.graphics.backgroundColor = fillColorPanel.graphics.newBrush(
        fillColorPanel.graphics.BrushType.SOLID_COLOR, 
        [fillColor[0]/255, fillColor[1]/255, fillColor[2]/255]
    );
    
    var selectFillColorBtn = fillColorGroup.add("button", undefined, "选择颜色...");
    selectFillColorBtn.onClick = function() {
        var oldColor = new SolidColor();
        oldColor.rgb.red = app.foregroundColor.rgb.red;
        oldColor.rgb.green = app.foregroundColor.rgb.green;
        oldColor.rgb.blue = app.foregroundColor.rgb.blue;
        
        app.foregroundColor.rgb.red = fillColor[0];
        app.foregroundColor.rgb.green = fillColor[1];
        app.foregroundColor.rgb.blue = fillColor[2];
        
        var colorPicker = app.showColorPicker();
        
        if (colorPicker) {
            fillColor = [
                app.foregroundColor.rgb.red,
                app.foregroundColor.rgb.green,
                app.foregroundColor.rgb.blue
            ];
            
            fillColorPanel.graphics.backgroundColor = fillColorPanel.graphics.newBrush(
                fillColorPanel.graphics.BrushType.SOLID_COLOR, 
                [fillColor[0]/255, fillColor[1]/255, fillColor[2]/255]
            );
            fillColorPanel.update();
        }
        
        app.foregroundColor = oldColor;
    };
    
    // 描边颜色选择部分
    var strokeGroup = dlg.add("panel", undefined, "描边颜色替换");
    strokeGroup.orientation = "row";
    strokeGroup.alignChildren = "left";
    strokeGroup.margins = 15;
    
    var useStrokeCheckbox = strokeGroup.add("checkbox", undefined, "启用");
    useStrokeCheckbox.value = true;
    
    var strokeColorGroup = strokeGroup.add("group");
    strokeColorGroup.orientation = "column";
    
    strokeColorGroup.add("statictext", undefined, "描边颜色:");
    var strokeColorPanel = strokeColorGroup.add("panel", undefined, "");
    strokeColorPanel.preferredSize = [100, 20];
    
    var strokeColor = [0, 255, 0];
    strokeColorPanel.graphics.backgroundColor = strokeColorPanel.graphics.newBrush(
        strokeColorPanel.graphics.BrushType.SOLID_COLOR, 
        [strokeColor[0]/255, strokeColor[1]/255, strokeColor[2]/255]
    );
    
    var selectStrokeColorBtn = strokeGroup.add("button", undefined, "选择颜色...");
    selectStrokeColorBtn.onClick = function() {
        var oldColor = new SolidColor();
        oldColor.rgb.red = app.foregroundColor.rgb.red;
        oldColor.rgb.green = app.foregroundColor.rgb.green;
        oldColor.rgb.blue = app.foregroundColor.rgb.blue;
        
        app.foregroundColor.rgb.red = strokeColor[0];
        app.foregroundColor.rgb.green = strokeColor[1];
        app.foregroundColor.rgb.blue = strokeColor[2];
        
        var colorPicker = app.showColorPicker();
        
        if (colorPicker) {
            strokeColor = [
                app.foregroundColor.rgb.red,
                app.foregroundColor.rgb.green,
                app.foregroundColor.rgb.blue
            ];
            
            strokeColorPanel.graphics.backgroundColor = strokeColorPanel.graphics.newBrush(
                strokeColorPanel.graphics.BrushType.SOLID_COLOR, 
                [strokeColor[0]/255, strokeColor[1]/255, strokeColor[2]/255]
            );
            strokeColorPanel.update();
        }
        
        app.foregroundColor = oldColor;
    };
    
    var btnGroup = dlg.add("group");
    btnGroup.orientation = "row";
    btnGroup.alignment = "center";
    
    var okBtn = btnGroup.add("button", undefined, "执行替换", {name: "ok"});
    var cancelBtn = btnGroup.add("button", undefined, "取消", {name: "cancel"});
    
    if (dlg.show() == 1) {
        var options = {
            useFill: useFillCheckbox.value,
            useStroke: useStrokeCheckbox.value,
            fillColor: fillColor,
            strokeColor: strokeColor
        };
        
        replaceColors(doc, options);
    }
}

function replaceColors(doc, options) {
    if (!options.useFill && !options.useStroke) {
        alert("请至少选择一种颜色进行替换！");
        return;
    }
    
    app.activeDocument.suspendHistory("批量替换矢量图形颜色", "processSelectedLayers(doc, options)");
}

function processSelectedLayers(doc, options) {
    try {
        var selectedLayers = getSelectedLayers();
        
        if (selectedLayers.length === 0) {
            alert("请先选择至少一个图层或组！");
            return;
        }
        
        var modifiedCount = 0;
        
        for (var i = 0; i < selectedLayers.length; i++) {
            modifiedCount += processLayer(selectedLayers[i], options);
        }
        
        alert("替换完成！共修改了 " + modifiedCount + " 个矢量图形图层。");
    } catch (e) {
        alert("处理过程中发生错误：" + e);
    }
}

function getSelectedLayers() {
    var selectedLayers = [];
    var ref = new ActionReference();
    ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
    var desc = executeActionGet(ref);
    if (desc.hasKey(stringIDToTypeID('targetLayers'))) {
        var targetLayers = desc.getList(stringIDToTypeID('targetLayers'));
        for (var i = 0; i < targetLayers.count; i++) {
            var layerRef = targetLayers.getReference(i);
            var selectDesc = new ActionDescriptor();
            selectDesc.putReference(charIDToTypeID("null"), layerRef);
            executeAction(charIDToTypeID("slct"), selectDesc, DialogModes.NO);
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    }
    return selectedLayers;
}

function processLayer(layer, options) {
    var count = 0;
    
    if (layer.typename === "LayerSet") {
        for (var i = 0; i < layer.layers.length; i++) {
            count += processLayer(layer.layers[i], options);
        }
        return count;
    }
    
    try {
        app.activeDocument.activeLayer = layer;
        
        var ref = new ActionReference();
        ref.putEnumerated(stringIDToTypeID("layer"), stringIDToTypeID("ordinal"), stringIDToTypeID("targetEnum"));
        var layerDesc = executeActionGet(ref);
        
        if (layerDesc.hasKey(stringIDToTypeID("textKey")) || 
            layerDesc.hasKey(stringIDToTypeID("vectorMaskEnabled")) ||
            layerDesc.hasKey(stringIDToTypeID("contentLayer"))) {
            var isModified = false;
            
            if (options.useFill) {
                try {
                    var d = new ActionDescriptor();
                    var r = new ActionReference();
                    r.putEnumerated(stringIDToTypeID("contentLayer"), stringIDToTypeID("ordinal"), stringIDToTypeID("targetEnum"));
                    d.putReference(stringIDToTypeID("null"), r);
                    var d1 = new ActionDescriptor();
                    var d2 = new ActionDescriptor();
                    var d3 = new ActionDescriptor();
                    d3.putDouble(stringIDToTypeID("red"), options.fillColor[0]);
                    d3.putDouble(stringIDToTypeID("green"), options.fillColor[1]);
                    d3.putDouble(stringIDToTypeID("blue"), options.fillColor[2]);
                    d2.putObject(stringIDToTypeID("color"), stringIDToTypeID("RGBColor"), d3);
                    d1.putObject(stringIDToTypeID("fillContents"), stringIDToTypeID("solidColorLayer"), d2);
                    
                    try {
                        var strokeRef = new ActionReference();
                        strokeRef.putEnumerated(stringIDToTypeID("contentLayer"), stringIDToTypeID("ordinal"), stringIDToTypeID("targetEnum"));
                        var layerDesc = executeActionGet(strokeRef);
                        
                        if (layerDesc.hasKey(stringIDToTypeID("strokeStyle"))) {
                            var strokeStyle = layerDesc.getObjectValue(stringIDToTypeID("strokeStyle"));
                            d1.putObject(stringIDToTypeID("strokeStyle"), stringIDToTypeID("strokeStyle"), strokeStyle);
                        }
                    } catch (e) {
                        var d4 = new ActionDescriptor();
                        d4.putInteger(stringIDToTypeID("strokeStyleVersion"), 2);
                        d4.putBoolean(stringIDToTypeID("fillEnabled"), true);
                        d1.putObject(stringIDToTypeID("strokeStyle"), stringIDToTypeID("strokeStyle"), d4);
                    }
                    
                    d.putObject(stringIDToTypeID("to"), stringIDToTypeID("shapeStyle"), d1);
                    executeAction(stringIDToTypeID("set"), d, DialogModes.NO);
                    
                    isModified = true;
                } catch (e) {
                    // 忽略错误
                }
            }
            
            if (options.useStroke) {
                try {
                    var d = new ActionDescriptor();
                    var r = new ActionReference();
                    r.putEnumerated(stringIDToTypeID("contentLayer"), stringIDToTypeID("ordinal"), stringIDToTypeID("targetEnum"));
                    d.putReference(stringIDToTypeID("null"), r);
                    var d1 = new ActionDescriptor();
                    var d2 = new ActionDescriptor();
                    var d3 = new ActionDescriptor();
                    var d4 = new ActionDescriptor();
                    d4.putDouble(stringIDToTypeID("red"), options.strokeColor[0]);
                    d4.putDouble(stringIDToTypeID("green"), options.strokeColor[1]);
                    d4.putDouble(stringIDToTypeID("blue"), options.strokeColor[2]);
                    d3.putObject(stringIDToTypeID("color"), stringIDToTypeID("RGBColor"), d4);
                    d2.putObject(stringIDToTypeID("strokeStyleContent"), stringIDToTypeID("solidColorLayer"), d3);
                    d2.putInteger(stringIDToTypeID("strokeStyleVersion"), 2);
                    d2.putBoolean(stringIDToTypeID("strokeEnabled"), true);
                    d1.putObject(stringIDToTypeID("strokeStyle"), stringIDToTypeID("strokeStyle"), d2);
                    d.putObject(stringIDToTypeID("to"), stringIDToTypeID("shapeStyle"), d1);
                    executeAction(stringIDToTypeID("set"), d, DialogModes.NO);
                    
                    isModified = true;
                } catch (e) {
                    // 忽略错误
                }
            }
            
            if (isModified) {
                count++;
            }
        }
    } catch (e) {
        // 忽略图层处理错误
    }
    
    return count;
}

main();