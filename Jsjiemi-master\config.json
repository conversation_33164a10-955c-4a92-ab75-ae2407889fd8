{
	/**
	 * 目标脚本文件路径
	 * ·支持使用相对路径，起始路径为启动解密器时所在的路径，而不是解密器所在的位置。
	 */
	"target": "./target.js",
	/**
	 * 输出脚本文件路径
	 * ·支持使用相对路径，起始路径为启动解密器时所在的路径，而不是解密器所在的位置。
	 * ·输出路径可使用占位符“{:xxx}”，在文件输出时会被替换为对应的值，目前支持的占位符有：
	 * · {:N} - 必填。用于区分某个解密步骤的输出文件；
	 * · {:TN} - 目标脚本文件的文件名；
	 * · {:TD} - 目标脚本文件所在文件夹的绝对路径。
	 */
	"output": "./JsjiemiResult{:N}.js",
	/**
	 * 安静模式
	 * ·启用后自动跳过所有暂停，这可能错过重要的安全警告。
	 * ·若系统不支持暂停则必须启用该选项。
	 * ·可选值：true | false
	 */
	"quietMode": false,
	/**
	 * 日志配置
	 * ·调整解密器执行时展示的日志样式。
	 */
	"logger": {
		/**
		 * 内容样式
		 * ·默认内容样式：
		 *   * 解密完成！
		 *   · 耗时：25326ms
		 */
		"content": {
			// 行前缀
			"linePrefix": {
				// 首行前缀
				"first": "* ",
				// 非首行前缀
				"others": "· "
			}
		},
		/**
		 * 进度条样式
		 * ·默认进度条样式：
		 * ·已知进度：[=================================                 ]  67.7%
		 * ·未知进度：[     ==========                                   ]
		 */
		"progress": {
			// 进度条总长度（单位：字符）
			"length": 50,
			// 进度条最小刷新间隔（单位：毫秒）
			"frequency": 100,
			// 进度条背景
			"emptyStr": " ",
			// 进度条滑块
			"fullStr": "="
		}
	},
	/**
	 * 可选功能
	 * ·你可以根据需求开关以下可选功能。
	 * ·错误地配置可选功能可能导致解密效果不佳，若你不了解功能的具体用途建议维持现状。
	 */
	"optionalFunction": {
		// 合并串联字符串（'spl'+'it' → 'split'）
		"MergeString": true,
		// 转换十六进制数字（0xf → 15）
		"ConvertHex": true,
		// 替换索引器（Object['keys'] → Object.keys）
		"ReplaceIndexer": true,
		// 转换Unicode字符（\x22 → "）
		"ConvertUnicode": true
	}
}