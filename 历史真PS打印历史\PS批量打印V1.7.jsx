// 全局变量
// 存储原始文档排序的路径
var originalDocPaths = [];

// 全局变量用于打印统计
// 这些变量在UI中使用
var gTotalDocs = 0;
var gPrintedDocs = 0;
var gSuccessCount = 0;
var gStatusText = null;
var gTotalDocsText = null;
var gPrintedDocsText = null;
var gSuccessRateText = null;

// 保存原始文档排序
function saveOriginalDocOrder() {
    originalDocPaths = [];
    // 保存文档路径而不是文档对象
    for (var i = 0; i < app.documents.length; i++) {
        // 检查文档是否有路径
        if (app.documents[i].path != "") {
            originalDocPaths.push(app.documents[i].fullName);
            debugLog("保存原始文档路径: " + app.documents[i].fullName);
        } else {
            debugLog("警告: 文档 '" + app.documents[i].name + "' 没有保存路径，无法记录原始排序");
        }
    }

    debugLog("已保存 " + originalDocPaths.length + " 个文档的原始排序");
}

// 恢复原始文档排序
function restoreDocOrder() {
    if (originalDocPaths.length === 0) {
        if (gStatusText) gStatusText.text = "没有保存的原始文档排序";
        app.refresh();
        return false;
    }
    // 只保存未保存的文档，减少不必要的保存
    for (var i = 0; i < app.documents.length; i++) {
        try {
            var doc = app.documents[i];
            if (doc.saved === false) {
                app.activeDocument = doc;
                doc.save();
            }
        } catch (e) {}
    }
    // 一次性关闭所有文档
    while (app.documents.length > 0) {
        app.documents[0].close(SaveOptions.DONOTSAVECHANGES);
    }
    // 快速按原始顺序打开文档
    for (var i = 0; i < originalDocPaths.length; i++) {
        safeOpenFile(originalDocPaths[i]);
    }
    if (gStatusText) gStatusText.text = "恢复排序完成";
    app.refresh();
    return true;
}

// 按文件名重新排序文档
function reorderDocsByName() {
    if (app.documents.length <= 1) {
        if (gStatusText) gStatusText.text = "无需排序";
        app.refresh();
        return false;
    }
    // 收集所有文档路径与名称
    var docs = [];
    for (var i = 0; i < app.documents.length; i++) {
        docs.push({name: app.documents[i].name, path: app.documents[i].fullName});
    }
    // 生成按文件名排序的新顺序
    var sorted = docs.slice().sort(function(a, b) { return a.name.localeCompare(b.name); });
    // 检查是否已排序
    var changed = false;
    for (var i = 0; i < docs.length; i++) {
        if (docs[i].name !== sorted[i].name) { changed = true; break; }
    }
    if (!changed) {
        if (gStatusText) gStatusText.text = "无需排序";
        app.refresh();
        return false;
    }
    // 一次性关闭所有文档
    while (app.documents.length > 0) {
        app.documents[0].close(SaveOptions.DONOTSAVECHANGES);
    }
    // 快速按新顺序打开
    for (var i = 0; i < sorted.length; i++) {
        safeOpenFile(sorted[i].path);
    }
    if (gStatusText) gStatusText.text = "已按文件名重新排序文档";
    app.refresh();
    return true;
}

function main() {
    // Check if Photoshop is running
    if (app.documents.length === 0) {
        alert("请先打开至少一个文档！");
        return;
    }

    // 检查是否有未保存的文档
    var hasUnsavedDocs = false;
    for (var i = 0; i < app.documents.length; i++) {
        if (app.documents[i].path == "") {
            hasUnsavedDocs = true;
            break;
        }
    }

    // 如果有未保存的文档，提示用户
    if (hasUnsavedDocs) {
        // 将弹窗提示替换为状态文本
        // 这个提示会在界面上显示
    }

    // 保存原始文档排序
    saveOriginalDocOrder();

    // Create dialog - 使用标准dialog类型的窗口
    var dlg = new Window("dialog", "批量打印V1.6");
    dlg.alignChildren = ["center", "top"];
    dlg.spacing = 10;
    dlg.margins = 16;

    // Main print button
    var mainPrintBtn = dlg.add("button", undefined, "【 批 量 打 印 】");
    mainPrintBtn.preferredSize.width = 150;

    // Order buttons group
    var orderGroup = dlg.add("group");
    orderGroup.orientation = "row";
    orderGroup.alignChildren = ["center", "center"];
    orderGroup.spacing = 10;

    var restoreOrderBtn = orderGroup.add("button", undefined, "恢复排序");
    restoreOrderBtn.preferredSize.width = 100;

    var reorderBtn = orderGroup.add("button", undefined, "重新排序");
    reorderBtn.preferredSize.width = 100;

    // 合并尺寸输入控件为一行（上文本，下输入框）
    var sizeRowGroup = dlg.add("group");
    sizeRowGroup.orientation = "row";
    sizeRowGroup.alignChildren = ["center", "top"];
    sizeRowGroup.spacing = 20;

    // 宽度
    var widthGroup = sizeRowGroup.add("group");
    widthGroup.orientation = "column";
    widthGroup.alignChildren = ["center", "top"];
    widthGroup.spacing = 2;
    widthGroup.add("statictext", undefined, "宽度(毫米)");
    var widthInput = widthGroup.add("edittext", undefined, "210");
    widthInput.preferredSize.width = 50;

    // 高度
    var heightGroup = sizeRowGroup.add("group");
    heightGroup.orientation = "column";
    heightGroup.alignChildren = ["center", "top"];
    heightGroup.spacing = 2;
    heightGroup.add("statictext", undefined, "高度(毫米)");
    var heightInput = heightGroup.add("edittext", undefined, "297");
    heightInput.preferredSize.width = 50;

    // 分辨率
    var resGroup = sizeRowGroup.add("group");
    resGroup.orientation = "column";
    resGroup.alignChildren = ["center", "top"];
    resGroup.spacing = 2;
    resGroup.add("statictext", undefined, "分辨率(dpi)");
    var resolutionInput = resGroup.add("edittext", undefined, "300");
    resolutionInput.preferredSize.width = 50;

    // Proportional checkbox
    var proportionalCheck = dlg.add("checkbox", undefined, "按比例调整");
    proportionalCheck.value = true;

    // 调整图像按钮组
    var resizeGroup = dlg.add("group");
    resizeGroup.orientation = "row";
    resizeGroup.alignChildren = ["center", "center"];
    resizeGroup.spacing = 10;
    
    var rotateToPortraitBtn = resizeGroup.add("button", undefined, "横转竖");
    rotateToPortraitBtn.preferredSize.width = 100;
    var resizeBtn = resizeGroup.add("button", undefined, "调整图像大小");
    resizeBtn.preferredSize.width = 100;
    var rotateToLandscapeBtn = resizeGroup.add("button", undefined, "竖转横");
    rotateToLandscapeBtn.preferredSize.width = 100;

    // 打开打印界面按钮
    var openPrintDialogBtn = dlg.add("button", undefined, "打开打印界面");
    openPrintDialogBtn.preferredSize.width = 150;

    // Print and close buttons
    var actionGroup = dlg.add("group");
    actionGroup.orientation = "row";
    actionGroup.alignChildren = ["center", "center"];
    actionGroup.spacing = 10;

    var printBtn = actionGroup.add("button", undefined, "点此打印");
    printBtn.preferredSize.width = 100;

    var closeBtn = actionGroup.add("button", undefined, "关闭退出");
    closeBtn.preferredSize.width = 100;

    // 添加打印统计信息面板 - 移动到底部
    var statsPanel = dlg.add("panel", undefined, "数据加载，打印进度及统计");
    statsPanel.orientation = "column";
    statsPanel.alignChildren = ["left", "top"];
    statsPanel.spacing = 5;
    statsPanel.margins = 10;
    statsPanel.preferredSize.width = 300;

    // 添加状态文本 - 关联到全局变量
    gStatusText = statsPanel.add("statictext", undefined, "正在加载打印数据...");
    gStatusText.preferredSize.width = 280;

    // 添加打印统计信息 - 关联到全局变量
    gTotalDocsText = statsPanel.add("statictext", undefined, "总文档数: " + app.documents.length);
    gPrintedDocsText = statsPanel.add("statictext", undefined, "已打印文档: 0");

    // Footer text
    var footerText1 = dlg.add("statictext", undefined, "恒心-perseverance");
    footerText1.alignment = "center";
    footerText1.graphics.foregroundColor = footerText1.graphics.newPen(footerText1.graphics.PenType.SOLID_COLOR, [1, 0.5, 0], 1);




    // Button functionality
    mainPrintBtn.onClick = function() {
        batchPrint();
    };

    restoreOrderBtn.onClick = function() {
        if (restoreDocOrder()) {
            if (gStatusText) gStatusText.text = "已恢复原始文档排序";
            app.refresh();
        }
    };

    reorderBtn.onClick = function() {
        if (reorderDocsByName()) {
            if (gStatusText) gStatusText.text = "已按文件名重新排序文档";
            app.refresh();
        }
    };

    resizeBtn.onClick = function() {
        resizeImages(parseFloat(widthInput.text), parseFloat(heightInput.text), parseFloat(resolutionInput.text), proportionalCheck.value);
    };

    // 横转竖：所有文档宽>高时顺时针旋转90度
    rotateToPortraitBtn.onClick = function() {
        if (app.documents.length === 0) {
            if (gStatusText) gStatusText.text = "没有打开的文档！";
            app.refresh();
            return;
        }
        var total = app.documents.length;
        var rotated = 0;
        for (var i = 0; i < total; i++) {
            var doc = app.documents[i];
            if (doc.width > doc.height) {
                app.activeDocument = doc;
                doc.rotateCanvas(90);
                rotated++;
            }
        }
        if (gStatusText) gStatusText.text = "横转竖完成，总文档数: " + total + "，已旋转: " + rotated;
        app.refresh();
    };

    // 竖转横：所有文档高>宽时顺时针旋转90度
    rotateToLandscapeBtn.onClick = function() {
        if (app.documents.length === 0) {
            if (gStatusText) gStatusText.text = "没有打开的文档！";
            app.refresh();
            return;
        }
        var total = app.documents.length;
        var rotated = 0;
        for (var i = 0; i < total; i++) {
            var doc = app.documents[i];
            if (doc.height > doc.width) {
                app.activeDocument = doc;
                doc.rotateCanvas(90);
                rotated++;
            }
        }
        if (gStatusText) gStatusText.text = "竖转横完成，总文档数: " + total + "，已旋转: " + rotated;
        app.refresh();
    };



    printBtn.onClick = function() {
        printCurrentDocument();
    };

    // 打开打印界面按钮的点击事件
    openPrintDialogBtn.onClick = function() {
        openPrintDialog();
    };

    closeBtn.onClick = function() {
        // 在关闭对话框时清空原始文档排序记忆
        originalDocPaths = [];
        dlg.close();
    };

    // 检查是否有未保存文档，在状态栏显示
    if (hasUnsavedDocs) {
        gStatusText.text = "注意: 有未保存的文档，建议先保存所有文档";
    } else {
        gStatusText.text = "就绪，可以开始处理";
    }

    // 显示对话框
    dlg.center();
    return dlg.show();
}

// Function to resize images
function resizeImages(width, height, resolution, keepProportion) {
    if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
        if (gStatusText) gStatusText.text = "请输入有效的宽度和高度值！";
        app.refresh();
        return;
    }

    if (isNaN(resolution) || resolution <= 0) {
        if (gStatusText) gStatusText.text = "请输入有效的分辨率值！";
        app.refresh();
        return;
    }

    // 更新状态
    if (gStatusText) gStatusText.text = "正在准备调整图像大小...";
    app.refresh();

    try {
        // 保存原始单位和对话框模式
        var originalRulerUnits = app.preferences.rulerUnits;
        var originalDialogMode = app.displayDialogs;

        // 设置为毫米单位并禁用对话框
        app.displayDialogs = DialogModes.NO;
        app.preferences.rulerUnits = Units.MM;

        // Process all open documents
        for (var i = 0; i < app.documents.length; i++) {
            var doc = app.documents[i];
            app.activeDocument = doc;

            // 更新状态
            if (gStatusText) gStatusText.text = "正在调整图像: " + doc.name + " (" + (i+1) + "/" + app.documents.length + ")";
            app.refresh();

            if (keepProportion) {
                // 计算维持纵横比的新尺寸
                var ratio = doc.width.as('mm') / doc.height.as('mm');
                var newWidth, newHeight;

                if (ratio > width / height) {
                    // 宽度是限制因素
                    newWidth = width;
                    newHeight = newWidth / ratio;
                } else {
                    // 高度是限制因素
                    newHeight = height;
                    newWidth = newHeight * ratio;
                }

                // 直接使用毫米单位调整大小，并设置分辨率
                doc.resizeImage(UnitValue(newWidth, "mm"), UnitValue(newHeight, "mm"),
                               resolution, ResampleMethod.BICUBIC);
            } else {
                // 直接使用毫米单位调整到精确尺寸，并设置分辨率
                doc.resizeImage(UnitValue(width, "mm"), UnitValue(height, "mm"),
                               resolution, ResampleMethod.BICUBIC);
            }
        }

        // 恢复原始设置
        app.preferences.rulerUnits = originalRulerUnits;
        app.displayDialogs = originalDialogMode;

        // 更新状态
        if (gStatusText) gStatusText.text = "所有图像大小调整完成!";
        app.refresh();
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalRulerUnits !== 'undefined') {
            app.preferences.rulerUnits = originalRulerUnits;
        }
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }

        // 更新错误状态
        if (gStatusText) gStatusText.text = "调整大小时出错: " + e;
        app.refresh();
    }
}

// 简单的日志函数
function logMessage(message) {
    // 只在控制台输出，不显示对话框
    $.writeln(message);
}

// 调试日志函数 - 只在开发模式下输出
function debugLog(message) {
    // 设置为false可以关闭调试输出
    var debugMode = false;
    if (debugMode) {
        $.writeln(message);
    }
}

// 安全打开文件函数 - 处理中文路径问题
function safeOpenFile(filePath) {
    try {
        // 先尝试直接打开
        var fileObj = new File(filePath);
        if (fileObj.exists) {
            // 设置当前对话框模式为无，抑制警告
            var originalDialogMode = app.displayDialogs;
            app.displayDialogs = DialogModes.NO;

            try {
                app.open(fileObj);
                app.displayDialogs = originalDialogMode;
                return true;
            } catch (e) {
                // 如果直接打开失败，尝试使用Action接口
                app.displayDialogs = originalDialogMode;

                var idOpn = charIDToTypeID("Opn ");
                var desc = new ActionDescriptor();
                var idnull = charIDToTypeID("null");
                desc.putPath(idnull, fileObj);
                executeAction(idOpn, desc, DialogModes.NO);
                return true;
            }
        } else {
            debugLog("文件不存在: " + filePath);
            return false;
        }
    } catch (err) {
        debugLog("打开文件时出错: " + err + ", 路径: " + filePath);
        return false;
    }
}

// 打印方法 - 只使用成功的方法
function printDocument(doc) {
    try {
        logMessage("打印文档: " + doc.name);
        doc.print();
        return true;
    } catch(err) {
        logMessage("打印失败: " + err);
        return false;
    }
}

// 只打印当前活动文档
function printCurrentDocument() {
    if (app.documents.length === 0) {
        if (gStatusText) gStatusText.text = "没有打开的文档！";
        app.refresh();
        return;
    }

    // 更新状态
    if (gStatusText) gStatusText.text = "正在打印: " + app.activeDocument.name;
    app.refresh();

    logMessage("打印当前文档: " + app.activeDocument.name);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 打印当前文档
        var success = printDocument(app.activeDocument);

        // 更新统计信息
        gPrintedDocs = 1;
        gSuccessCount = success ? 1 : 0;

        // 更新UI显示
        if (gTotalDocsText) gTotalDocsText.text = "总文档数: 1";
        if (gPrintedDocsText) gPrintedDocsText.text = "已打印文档: 1";
        if (gSuccessRateText) {
            var rate = success ? 100 : 0;
            gSuccessRateText.text = "成功率: " + rate + "%";
        }

        // 更新状态
        if (gStatusText) gStatusText.text = success ? "打印成功!" : "打印失败!";
        app.refresh();

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }

        // 更新错误状态
        if (gStatusText) gStatusText.text = "打印时出错: " + e;
        app.refresh();
    }
}

// Function to batch print all open documents
function batchPrint() {
    if (app.documents.length === 0) {
        if (gStatusText) gStatusText.text = "没有打开的文档！";
        app.refresh();
        return;
    }

    // 重置打印统计
    gTotalDocs = app.documents.length;
    gPrintedDocs = 0;
    gSuccessCount = 0;

    // 更新UI显示
    if (gStatusText) gStatusText.text = "正在准备批量打印...";
    if (gTotalDocsText) gTotalDocsText.text = "总文档数: " + gTotalDocs;
    if (gPrintedDocsText) gPrintedDocsText.text = "已打印文档: 0";
    if (gSuccessRateText) gSuccessRateText.text = "成功率: 0%";

    // 刷新UI
    app.refresh();

    logMessage("开始批量打印操作, 文档数量: " + gTotalDocs);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 处理所有打开的文档
        for (var i = 0; i < app.documents.length; i++) {
            var doc = app.documents[i];
            app.activeDocument = doc;

            // 更新状态
            if (gStatusText) gStatusText.text = "正在打印: " + doc.name + " (" + (i+1) + "/" + gTotalDocs + ")";
            app.refresh();

            // 使用成功的打印方法
            if (printDocument(doc)) {
                gSuccessCount++;
            }

            // 更新打印计数
            gPrintedDocs++;

            // 更新UI显示
            if (gPrintedDocsText) gPrintedDocsText.text = "已打印文档: " + gPrintedDocs;
            if (gSuccessRateText) {
                var rate = Math.round((gSuccessCount / gPrintedDocs) * 100);
                gSuccessRateText.text = "成功率: " + rate + "%";
            }

            // 刷新UI
            app.refresh();
        }

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;

        // 更新最终状态
        if (gStatusText) gStatusText.text = "打印完成! 成功打印: " + gSuccessCount + "/" + gTotalDocs;
        app.refresh();
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }

        // 更新错误状态
        if (gStatusText) gStatusText.text = "打印时出错: " + e;
        app.refresh();
    }
}

// 打开打印对话框函数
function openPrintDialog() {
    if (app.documents.length === 0) {
        if (gStatusText) gStatusText.text = "没有打开的文档！";
        app.refresh();
        return;
    }

    // 更新状态
    if (gStatusText) gStatusText.text = "正在打开打印对话框...";
    app.refresh();

    try {
        // 使用最简单的方法直接调用打印命令
        var idPrnt = charIDToTypeID("Prnt");
        executeAction(idPrnt, undefined, DialogModes.ALL);

    } catch (e) {
        // 如果用户取消操作，不显示错误
        if (e.number != 8007) { // 8007是用户取消操作的错误代码
            // 更新错误状态
            if (gStatusText) gStatusText.text = "打开打印对话框时出错: " + e;
            app.refresh();
        } else {
            // 用户取消了打印
            if (gStatusText) gStatusText.text = "打印已取消";
            app.refresh();
        }
    }
}

// 直接执行脚本
main();
