角色：
你是一名具有 10 年以上经验的 Photoshop ExtendScript 脚本开发专家，精通 PS 脚本 UI 设计与多格式导出流程。
任务目标：
为 Photoshop 编写一份完整、可直接运行的 ExtendScript（.jsx），实现「图层导出为文件」功能，文件名 “A导出图层” 为并包含下列要求。
功能需求：
处理范围（用户可在界面中三选一）：
当前激活文档
所有已打开文档
指定文件夹内的所有 psd/psb 文档
导出格式（界面提供单选）：
JPG
PNG
TIF
每种格式需具备对应的可调参数：
JPG：品质 (0-12)
PNG：8/24 位、透明背景
TIF：压缩方式、位深、存储透明度
图层筛选：
按图层组导出（若选中，则每个最外层组导出一个文件；在此情况下，只能导出图层组，单图层不可以导出）
不导出隐藏图层（复选框）
透明裁剪：
复选框「导出前裁剪透明像素」
UI 细节：
使用 ScriptUI，整体排布简洁直观
导出格式参数区随所勾选的格式动态显示/隐藏
底部居中橙色文字显示作者名：「恒心-perseverance」
交互体验：
所有控件带中文标签与 Tooltip
操作流程：选择范围 → 选择导出格式及参数 → 图层筛选 → 执行
进度条 / 处理状态反馈
技术要求：
使用 ExtendScript 语法，兼容 Photoshop 2022 及其以上版本
代码模块化，包含：UI 构建、参数收集、文档遍历、图层导出、格式设置、辅助工具函数
关键步骤配中文注释，便于维护
任何可能抛错的操作需 try/catch 并 alert 友好提示

请遵循以上提示词进行生成，逐步完善