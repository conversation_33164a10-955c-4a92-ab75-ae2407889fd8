// 全局变量
// 存储原始文档排序的路径
var originalDocPaths = [];

// 保存原始文档排序
function saveOriginalDocOrder() {
    originalDocPaths = [];
    // 保存文档路径而不是文档对象
    for (var i = 0; i < app.documents.length; i++) {
        // 检查文档是否有路径
        if (app.documents[i].path != "") {
            originalDocPaths.push(app.documents[i].fullName);
            debugLog("保存原始文档路径: " + app.documents[i].fullName);
        } else {
            debugLog("警告: 文档 '" + app.documents[i].name + "' 没有保存路径，无法记录原始排序");
        }
    }

    debugLog("已保存 " + originalDocPaths.length + " 个文档的原始排序");
}

// 恢复原始文档排序
function restoreDocOrder() {
    if (originalDocPaths.length === 0) {
        alert("没有保存的原始文档排序");
        return false;
    }

    // 先询问用户是否要保存文档
    if (!confirm("恢复排序前需要保存所有文档，是否继续？")) {
        return false;
    }

    try {
        debugLog("开始恢复原始文档排序");

        // 先保存所有打开的文档
        for (var i = 0; i < app.documents.length; i++) {
            try {
                // 先激活文档
                app.activeDocument = app.documents[i];
                debugLog("保存文档: " + app.documents[i].name);

                // 如果文档已经有路径，直接保存
                if (app.documents[i].path != "") {
                    app.documents[i].save();
                } else {
                    // 如果是新文档，提示用户保存
                    try {
                        app.documents[i].saveAs(File.saveDialog("请为文档 '" + app.documents[i].name + "' 选择保存位置"));
                    } catch (saveErr) {
                        debugLog("保存文档时出错: " + saveErr);
                        alert("文档 '" + app.documents[i].name + "' 未能保存，但仍将继续恢复排序");
                    }
                }
            } catch (docErr) {
                debugLog("处理文档时出错: " + docErr);
            }
        }

        // 关闭所有文档，不再保存更改
        for (var i = app.documents.length - 1; i >= 0; i--) {
            app.documents[i].close(SaveOptions.DONOTSAVECHANGES);
        }

        // 按原始顺序重新打开文档
        for (var i = 0; i < originalDocPaths.length; i++) {
            debugLog("尝试打开文档: " + originalDocPaths[i]);

            // 使用安全打开文件函数
            if (!safeOpenFile(originalDocPaths[i])) {
                alert("无法打开文件: " + originalDocPaths[i] + "\n请手动打开此文件");
            }
        }

        // 恢复后清空原始文档排序，避免重复使用
        originalDocPaths = [];

        debugLog("恢复排序完成");
        return true;
    } catch (e) {
        debugLog("恢复排序时出错: " + e);
        alert("恢复排序时出错: " + e);
        return false;
    }
}

// 按文件名重新排序文档
function reorderDocsByName() {
    if (app.documents.length <= 1) {
        alert("文档数量不足，无需排序");
        return false;
    }

    // 先询问用户是否要保存文档
    if (!confirm("排序前需要保存所有文档，是否继续？")) {
        return false;
    }

    try {
        debugLog("开始按文件名排序");

        // 创建文档数组
        var docs = [];
        for (var i = 0; i < app.documents.length; i++) {
            docs.push(app.documents[i]);
        }

        // 按文件名排序 - 使用自然排序算法
        docs.sort(function(a, b) {
            return a.name.localeCompare(b.name, undefined, {numeric: true});
        });

        debugLog("文档排序完成");

        // 保存所有文档的路径
        var docPaths = [];
        for (var i = 0; i < docs.length; i++) {
            try {
                // 先激活文档
                app.activeDocument = docs[i];
                debugLog("保存文档: " + docs[i].name);

                // 如果文档已经有路径，直接保存
                if (docs[i].path != "") {
                    docs[i].save();
                    docPaths.push(docs[i].fullName);
                } else {
                    // 如果是新文档，提示用户保存
                    var saveSuccess = false;
                    try {
                        docs[i].saveAs(File.saveDialog("请为文档 '" + docs[i].name + "' 选择保存位置"));
                        saveSuccess = true;
                    } catch (saveErr) {
                        debugLog("保存文档时出错: " + saveErr);
                    }

                    if (saveSuccess && docs[i].fullName) {
                        docPaths.push(docs[i].fullName);
                    } else {
                        alert("文档 '" + docs[i].name + "' 未能保存，将不会包含在排序中");
                    }
                }
            } catch (docErr) {
                debugLog("处理文档时出错: " + docErr);
            }
        }

        // 检查是否有文档路径
        if (docPaths.length === 0) {
            alert("没有文档被成功保存，无法排序");
            return false;
        }

        // 关闭所有文档
        for (var i = app.documents.length - 1; i >= 0; i--) {
            app.documents[i].close(SaveOptions.DONOTSAVECHANGES); // 已经保存过了，所以这里不需要再次保存
        }

        // 按排序后的顺序重新打开文档
        for (var i = 0; i < docPaths.length; i++) {
            debugLog("尝试打开文档: " + docPaths[i]);

            // 使用安全打开文件函数
            if (!safeOpenFile(docPaths[i])) {
                alert("无法打开文件: " + docPaths[i] + "\n请手动打开此文件");
            }
        }

        debugLog("排序完成");
        return true;
    } catch (e) {
        debugLog("重新排序时出错: " + e);
        alert("重新排序时出错: " + e);
        return false;
    }
}

function main() {
    // Check if Photoshop is running
    if (app.documents.length === 0) {
        alert("请先打开至少一个文档！");
        return;
    }

    // 检查是否有未保存的文档
    var hasUnsavedDocs = false;
    for (var i = 0; i < app.documents.length; i++) {
        if (app.documents[i].path == "") {
            hasUnsavedDocs = true;
            break;
        }
    }

    // 如果有未保存的文档，提示用户
    if (hasUnsavedDocs) {
        alert("注意: 有未保存的文档\n请先保存所有文档，以确保排序和恢复功能正常工作");
    }

    // 保存原始文档排序
    saveOriginalDocOrder();

    // Create dialog - 使用标准dialog类型的窗口
    var dlg = new Window("dialog", "批量打印V1.5");
    dlg.alignChildren = ["center", "top"];
    dlg.spacing = 10;
    dlg.margins = 16;

    // Main print button
    var mainPrintBtn = dlg.add("button", undefined, "【 批 量 打 印 】");
    mainPrintBtn.preferredSize.width = 150;

    // Order buttons group
    var orderGroup = dlg.add("group");
    orderGroup.orientation = "row";
    orderGroup.alignChildren = ["center", "center"];
    orderGroup.spacing = 10;

    var restoreOrderBtn = orderGroup.add("button", undefined, "恢复排序");
    restoreOrderBtn.preferredSize.width = 100;

    var reorderBtn = orderGroup.add("button", undefined, "重新排序");
    reorderBtn.preferredSize.width = 100;

    // Size inputs
    var sizeGroup1 = dlg.add("group");
    sizeGroup1.orientation = "row";
    sizeGroup1.alignChildren = ["left", "center"];
    sizeGroup1.spacing = 10;

    sizeGroup1.add("statictext", undefined, "宽度(毫米)：");
    var widthInput = sizeGroup1.add("edittext", undefined, "210");
    widthInput.preferredSize.width = 50;

    var sizeGroup2 = dlg.add("group");
    sizeGroup2.orientation = "row";
    sizeGroup2.alignChildren = ["left", "center"];
    sizeGroup2.spacing = 10;

    sizeGroup2.add("statictext", undefined, "高度(毫米)：");
    var heightInput = sizeGroup2.add("edittext", undefined, "297");
    heightInput.preferredSize.width = 50;

    // 添加分辨率输入框
    var sizeGroup3 = dlg.add("group");
    sizeGroup3.orientation = "row";
    sizeGroup3.alignChildren = ["left", "center"];
    sizeGroup3.spacing = 10;

    sizeGroup3.add("statictext", undefined, "分辨率(dpi)：");
    var resolutionInput = sizeGroup3.add("edittext", undefined, "300");
    resolutionInput.preferredSize.width = 50;

    // Proportional checkbox
    var proportionalCheck = dlg.add("checkbox", undefined, "按比例调整");
    proportionalCheck.value = true;

    // Resize button
    var resizeBtn = dlg.add("button", undefined, "调整图像大小");
    resizeBtn.preferredSize.width = 150;

    // 打开打印界面按钮
    var openPrintDialogBtn = dlg.add("button", undefined, "打开打印界面");
    openPrintDialogBtn.preferredSize.width = 150;

    // Print and close buttons
    var actionGroup = dlg.add("group");
    actionGroup.orientation = "row";
    actionGroup.alignChildren = ["center", "center"];
    actionGroup.spacing = 10;

    var printBtn = actionGroup.add("button", undefined, "点此打印");
    printBtn.preferredSize.width = 100;

    var closeBtn = actionGroup.add("button", undefined, "关闭退出");
    closeBtn.preferredSize.width = 100;



    // Footer text
    var footerText1 = dlg.add("statictext", undefined, "恒心-perseverance");
    footerText1.alignment = "center";

    // Button functionality
    mainPrintBtn.onClick = function() {
        batchPrint();
    };

    restoreOrderBtn.onClick = function() {
        if (restoreDocOrder()) {
            alert("已恢复原始文档排序");
        }
    };

    reorderBtn.onClick = function() {
        if (reorderDocsByName()) {
            alert("已按文件名重新排序文档");
        }
    };

    resizeBtn.onClick = function() {
        resizeImages(parseFloat(widthInput.text), parseFloat(heightInput.text), parseFloat(resolutionInput.text), proportionalCheck.value);
    };

    printBtn.onClick = function() {
        printCurrentDocument();
    };

    // 打开打印界面按钮的点击事件
    openPrintDialogBtn.onClick = function() {
        openPrintDialog();
    };

    closeBtn.onClick = function() {
        // 在关闭对话框时清空原始文档排序记忆
        originalDocPaths = [];
        dlg.close();
    };



    // 显示对话框
    dlg.center();
    return dlg.show();
}

// Function to resize images
function resizeImages(width, height, resolution, keepProportion) {
    if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
        alert("请输入有效的宽度和高度值！");
        return;
    }

    if (isNaN(resolution) || resolution <= 0) {
        alert("请输入有效的分辨率值！");
        return;
    }

    try {
        // 保存原始单位和对话框模式
        var originalRulerUnits = app.preferences.rulerUnits;
        var originalDialogMode = app.displayDialogs;

        // 设置为毫米单位并禁用对话框
        app.displayDialogs = DialogModes.NO;
        app.preferences.rulerUnits = Units.MM;

        // Process all open documents
        for (var i = 0; i < app.documents.length; i++) {
            var doc = app.documents[i];
            app.activeDocument = doc;

            if (keepProportion) {
                // 计算维持纵横比的新尺寸
                var ratio = doc.width.as('mm') / doc.height.as('mm');
                var newWidth, newHeight;

                if (ratio > width / height) {
                    // 宽度是限制因素
                    newWidth = width;
                    newHeight = newWidth / ratio;
                } else {
                    // 高度是限制因素
                    newHeight = height;
                    newWidth = newHeight * ratio;
                }

                // 直接使用毫米单位调整大小，并设置分辨率
                doc.resizeImage(UnitValue(newWidth, "mm"), UnitValue(newHeight, "mm"),
                               resolution, ResampleMethod.BICUBIC);
            } else {
                // 直接使用毫米单位调整到精确尺寸，并设置分辨率
                doc.resizeImage(UnitValue(width, "mm"), UnitValue(height, "mm"),
                               resolution, ResampleMethod.BICUBIC);
            }
        }

        // 恢复原始设置
        app.preferences.rulerUnits = originalRulerUnits;
        app.displayDialogs = originalDialogMode;

        alert("所有图像已调整大小！");
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalRulerUnits !== 'undefined') {
            app.preferences.rulerUnits = originalRulerUnits;
        }
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }
        alert("调整大小时出错: " + e);
    }
}

// 简单的日志函数
function logMessage(message) {
    // 只在控制台输出，不显示对话框
    $.writeln(message);
}

// 调试日志函数 - 在弹窗中显示调试信息
function debugLog(message) {
    // 在控制台输出
    $.writeln(message);
    // 在弹窗中显示
    // alert("调试信息: " + message);
}

// 安全打开文件函数 - 处理中文路径问题
function safeOpenFile(filePath) {
    try {
        // 先尝试直接打开
        var fileObj = new File(filePath);
        if (fileObj.exists) {
            // 设置当前对话框模式为无，抑制警告
            var originalDialogMode = app.displayDialogs;
            app.displayDialogs = DialogModes.NO;

            try {
                app.open(fileObj);
                app.displayDialogs = originalDialogMode;
                return true;
            } catch (e) {
                // 如果直接打开失败，尝试使用Action接口
                app.displayDialogs = originalDialogMode;

                var idOpn = charIDToTypeID("Opn ");
                var desc = new ActionDescriptor();
                var idnull = charIDToTypeID("null");
                desc.putPath(idnull, fileObj);
                executeAction(idOpn, desc, DialogModes.NO);
                return true;
            }
        } else {
            debugLog("文件不存在: " + filePath);
            return false;
        }
    } catch (err) {
        debugLog("打开文件时出错: " + err + ", 路径: " + filePath);
        return false;
    }
}

// 打印方法 - 只使用成功的方法
function printDocument(doc) {
    try {
        logMessage("打印文档: " + doc.name);
        doc.print();
        return true;
    } catch(err) {
        logMessage("打印失败: " + err);
        return false;
    }
}

// 只打印当前活动文档
function printCurrentDocument() {
    if (app.documents.length === 0) {
        alert("没有打开的文档！");
        return;
    }

    logMessage("打印当前文档: " + app.activeDocument.name);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 打印当前文档
        if (printDocument(app.activeDocument)) {
            alert("文档 " + app.activeDocument.name + " 已成功打印。");
        } else {
            alert("文档打印失败。请尝试手动打印。");
        }

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }
        alert("打印时出错: " + e);
    }
}

// Function to batch print all open documents
function batchPrint() {
    if (app.documents.length === 0) {
        alert("没有打开的文档！");
        return;
    }

    logMessage("开始批量打印操作, 文档数量: " + app.documents.length);

    try {
        // 保存原始对话框模式
        var originalDialogMode = app.displayDialogs;

        // 处理所有打开的文档
        var successCount = 0;

        for (var i = 0; i < app.documents.length; i++) {
            var doc = app.documents[i];
            app.activeDocument = doc;

            // 使用成功的打印方法
            if (printDocument(doc)) {
                successCount++;
            }
        }

        // 恢复原始设置
        app.displayDialogs = originalDialogMode;

        if (successCount > 0) {
            alert("批量打印完成！成功打印了 " + successCount + " 个文档。");
        } else {
            alert("没有文档被成功打印。请尝试手动打印。");
        }
    } catch (e) {
        // 出错时确保恢复设置
        if (typeof originalDialogMode !== 'undefined') {
            app.displayDialogs = originalDialogMode;
        }
        alert("打印时出错: " + e);
    }
}

// 打开打印对话框函数
function openPrintDialog() {
    if (app.documents.length === 0) {
        alert("没有打开的文档！");
        return;
    }

    try {
        // 使用最简单的方法直接调用打印命令
        var idPrnt = charIDToTypeID("Prnt");
        executeAction(idPrnt, undefined, DialogModes.ALL);

    } catch (e) {
        // 如果用户取消操作，不显示错误
        if (e.number != 8007) { // 8007是用户取消操作的错误代码
            alert("打开打印对话框时出错: " + e);
        }
    }
}

// 直接执行脚本
main();
