// Photoshop OCR识别脚本
// 使用百度智能云OCR API识别图层中的文字

//@target photoshop

// 调试日志函数
function debug(message) {
    // 将调试信息保存到日志文件
    var logFile = new File(Folder.desktop + "/PhotoshopOCR_JSX_Debug.txt");
    if (!logFile.exists) {
        logFile.open("w");
        logFile.writeln("===== PhotoshopOCR 调试日志 =====");
        logFile.writeln("时间: " + new Date().toString());
        logFile.close();
    }
    
    logFile.open("a");
    logFile.writeln("[" + new Date().toLocaleTimeString() + "] " + message);
    logFile.close();
    
    // 返回消息内容，方便链式调用
    return message;
}

// 显示调试信息
function showDebug(message) {
    debug(message);
    alert("调试信息: " + message);
}

// 将Windows路径格式化为JSX兼容格式
function formatPath(path) {
    // 确保使用正斜杠
    return path.replace(/\\/g, "/");
}

// 定义JSON对象，因为ExtendScript中不包含原生JSON支持
if (typeof JSON !== 'object') {
    JSON = {};
}

// 完整重新实现JSON.parse函数
if (typeof JSON.parse !== 'function') {
    JSON.parse = function(jsonString) {
        var debug = function(msg) {
            // 将调试信息保存到日志文件
            var logFile = new File(Folder.desktop + "/PhotoshopOCR_JSX_Debug.txt");
            logFile.open("a");
            logFile.writeln("[" + new Date().toLocaleTimeString() + "] " + msg);
            logFile.close();
        };
        
        debug("执行自定义JSON解析");
        
        // 清除所有注释和不必要的空白
        jsonString = jsonString.replace(/^\s+|\s+$/g, '');
        
        // 验证起始和结束字符
        if (jsonString.charAt(0) !== '[' && jsonString.charAt(0) !== '{') {
            throw new Error("Invalid JSON: must start with [ or {");
        }
        
        if ((jsonString.charAt(0) === '[' && jsonString.charAt(jsonString.length-1) !== ']') ||
            (jsonString.charAt(0) === '{' && jsonString.charAt(jsonString.length-1) !== '}')) {
            throw new Error("Invalid JSON: missing closing bracket");
        }
        
        // 替换可能导致问题的字符
        jsonString = jsonString
            .replace(/[\n\r\t]/g, ' ')      // 换行和制表符替换为空格
            .replace(/\\"/g, '@QUOTE@')     // 临时替换转义的引号
            .replace(/\\n/g, '@NEWLINE@')   // 临时替换换行符
            .replace(/\\r/g, '@RETURN@')    // 临时替换回车符
            .replace(/\\t/g, '@TAB@')       // 临时替换制表符
            .replace(/\\\\/g, '@BACKSLASH@');  // 临时替换反斜杠
        
        try {
            // 使用eval解析
            var result = eval('(' + jsonString + ')');
            
            // 自动递归遍历并还原所有临时替换的特殊字符
            function restoreSpecialChars(obj) {
                if (obj === null || obj === undefined) {
                    return obj;
                }
                
                if (typeof obj === 'string') {
                    return obj
                        .replace(/@QUOTE@/g, '\\"')
                        .replace(/@NEWLINE@/g, '\\n')
                        .replace(/@RETURN@/g, '\\r')
                        .replace(/@TAB@/g, '\\t')
                        .replace(/@BACKSLASH@/g, '\\');
                }
                
                if (typeof obj === 'object') {
                    if (Array.isArray(obj)) {
                        for (var i = 0; i < obj.length; i++) {
                            obj[i] = restoreSpecialChars(obj[i]);
                        }
                    } else {
                        for (var key in obj) {
                            if (obj.hasOwnProperty(key)) {
                                obj[key] = restoreSpecialChars(obj[key]);
                            }
                        }
                    }
                }
                
                return obj;
            }
            
            return restoreSpecialChars(result);
        } catch (e) {
            debug("自定义JSON解析失败: " + e);
            throw e;
        }
    };
}

// 实现JSON.stringify方法
if (typeof JSON.stringify !== 'function') {
    JSON.stringify = function(obj) {
        var t = typeof obj;
        if (t === "object" && obj !== null) {
            var result = '';
            
            if (Object.prototype.toString.apply(obj) === '[object Array]') {
                // 数组
                result = '[';
                for (var i = 0; i < obj.length; i++) {
                    if (i > 0) {
                        result += ',';
                    }
                    result += JSON.stringify(obj[i]);
                }
                result += ']';
                return result;
            } else {
                // 对象
                result = '{';
                var keys = [];
                for (var key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        keys.push(key);
                    }
                }
                
                for (var i = 0; i < keys.length; i++) {
                    var key = keys[i];
                    if (i > 0) {
                        result += ',';
                    }
                    result += '"' + key + '":' + JSON.stringify(obj[key]);
                }
                result += '}';
                return result;
            }
        } else if (t === "string") {
            return '"' + obj.replace(/\\/g, '\\\\').replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r') + '"';
        } else if (t === "number" || t === "boolean") {
            return String(obj);
        } else if (obj === null) {
            return "null";
        } else {
            return '""'; // undefined或function
        }
    };
}

// 检查配置文件夹是否存在，如果不存在则创建
function checkAndCreateConfigFolder() {
    debug("检查并创建配置文件夹");
    var appDataPath = Folder.appData + "/Adobe/Logs";
    var configFolder = new Folder(appDataPath);
    
    if (!configFolder.exists) {
        debug("创建配置文件夹: " + appDataPath);
        configFolder.create();
    } else {
        debug("配置文件夹已存在: " + appDataPath);
    }
    
    return appDataPath;
}

// 读取配置文件
function readConfigFile() {
    debug("读取配置文件");
    var configPath = checkAndCreateConfigFolder() + "/PhotoshopOCR";
    var configFile = new File(configPath);
    var config = {
        apiKey: "",
        secretKey: ""
    };
    
    if (configFile.exists) {
        debug("配置文件存在，正在读取");
        configFile.open("r");
        var content = configFile.read();
        configFile.close();
        
        if (content) {
            var lines = content.split("\n");
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i];
                if (line.indexOf("API_KEY=") === 0) {
                    config.apiKey = line.substring(8);
                } else if (line.indexOf("SECRET_KEY=") === 0) {
                    config.secretKey = line.substring(11);
                }
            }
            debug("读取到API Key: " + (config.apiKey ? "是" : "否") + ", Secret Key: " + (config.secretKey ? "是" : "否"));
        } else {
            debug("配置文件为空");
        }
    } else {
        debug("配置文件不存在: " + configPath);
    }
    
    return config;
}

// 保存配置文件
function saveConfigFile(apiKey, secretKey) {
    debug("保存配置文件");
    var configPath = checkAndCreateConfigFolder() + "/PhotoshopOCR";
    var configFile = new File(configPath);
    
    configFile.open("w");
    configFile.writeln("API_KEY=" + apiKey);
    configFile.writeln("SECRET_KEY=" + secretKey);
    configFile.close();
    
    debug("配置文件已保存: " + configPath);
    return true;
}

// 显示API配置对话框
function showAPIConfigDialog(apiKey, secretKey) {
    debug("显示API配置对话框");
    var dialog = new Window("dialog", "百度智能云API配置");
    dialog.orientation = "column";
    dialog.alignChildren = ["fill", "top"];
    dialog.spacing = 10;
    dialog.margins = 16;
    
    // API Key
    var apiKeyGroup = dialog.add("group");
    apiKeyGroup.orientation = "row";
    apiKeyGroup.alignChildren = ["left", "center"];
    apiKeyGroup.spacing = 10;
    apiKeyGroup.add("statictext", undefined, "API Key:");
    var apiKeyInput = apiKeyGroup.add("edittext", undefined, apiKey);
    apiKeyInput.characters = 30;
    
    // Secret Key
    var secretKeyGroup = dialog.add("group");
    secretKeyGroup.orientation = "row";
    secretKeyGroup.alignChildren = ["left", "center"];
    secretKeyGroup.spacing = 10;
    secretKeyGroup.add("statictext", undefined, "Secret Key:");
    var secretKeyInput = secretKeyGroup.add("edittext", undefined, secretKey);
    secretKeyInput.characters = 30;
    
    // 按钮
    var buttonGroup = dialog.add("group");
    buttonGroup.orientation = "row";
    buttonGroup.alignChildren = ["center", "center"];
    buttonGroup.spacing = 10;
    
    var okButton = buttonGroup.add("button", undefined, "确定", {name: "ok"});
    var cancelButton = buttonGroup.add("button", undefined, "取消", {name: "cancel"});
    
    var result = {
        apiKey: apiKey,
        secretKey: secretKey,
        confirmed: false
    };
    
    okButton.onClick = function() {
        result.apiKey = apiKeyInput.text;
        result.secretKey = secretKeyInput.text;
        result.confirmed = true;
        dialog.close();
    };
    
    cancelButton.onClick = function() {
        dialog.close();
    };
    
    dialog.show();
    
    debug("API配置对话框关闭，确认: " + result.confirmed);
    return result;
}

// 导出当前选中的图层到临时文件
function exportSelectedLayersToTemp() {
    debug("导出选中图层");
    var doc = app.activeDocument;
    var selectedLayers = [];
    var exportedFiles = [];
    
    // 获取选中的图层
    try {
        // 直接获取当前活动图层
        debug("尝试获取当前活动图层");
        if (doc.activeLayer && doc.activeLayer.kind != LayerKind.TEXT) {
            selectedLayers.push(doc.activeLayer);
            debug("获取到活动图层: " + doc.activeLayer.name);
        } else {
            if (!doc.activeLayer) {
                debug("错误: 未获取到活动图层");
            } else if (doc.activeLayer.kind == LayerKind.TEXT) {
                debug("错误: 当前图层是文本图层，不能进行OCR识别");
            }
        }
    } catch (e) {
        var errorMsg = "获取选中图层失败: " + e;
        debug(errorMsg);
        alert(errorMsg);
        return null;
    }
    
    try {
        if (selectedLayers.length === 0) {
            var errorMsg = "请选择至少一个非文本图层!";
            debug(errorMsg);
            alert(errorMsg);
            return null;
        }
        
        // 创建临时文件夹
        debug("创建临时文件夹");
        var tempFolder = new Folder(Folder.temp + "/PhotoshopOCR");
        if (!tempFolder.exists) {
            tempFolder.create();
            debug("临时文件夹已创建: " + tempFolder.fsName);
        } else {
            debug("临时文件夹已存在: " + tempFolder.fsName);
        }
        
        // 导出每个选中的图层
        for (var i = 0; i < selectedLayers.length; i++) {
            var layer = selectedLayers[i];
            debug("处理图层: " + layer.name);
            
            try {
                // 创建临时文档并复制图层
                debug("创建临时文档");
                var tempDoc = app.documents.add(doc.width, doc.height, doc.resolution, "Temp_" + layer.name, NewDocumentMode.RGB, DocumentFill.TRANSPARENT);
                
                // 复制图层到新文档
                debug("复制图层到临时文档");
                app.activeDocument = doc;
                var layerRef = layer;
                layerRef.copy();
                app.activeDocument = tempDoc;
                tempDoc.paste();
                
                // 保存为临时文件 - 使用安全文件名
                var safeFileName = layer.name.replace(/[^a-zA-Z0-9]/g, "_") + "_" + i + ".png";
                var tempFile = new File(tempFolder + "/" + safeFileName);
                debug("保存图层为临时文件: " + tempFile.fsName);
                var saveOptions = new PNGSaveOptions();
                saveOptions.transparency = true;
                
                tempDoc.saveAs(tempFile, saveOptions, true, Extension.LOWERCASE);
                debug("临时文件已保存");
                
                // 关闭临时文档不保存
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
                debug("临时文档已关闭");
                
                // 切回原始文档
                app.activeDocument = doc;
                
                exportedFiles.push({
                    path: formatPath(tempFile.fsName), // 格式化路径
                    name: layer.name,
                    layerIndex: i
                });
                debug("图层已导出: " + layer.name);
            } catch (e) {
                var errorMsg = "导出图层 " + layer.name + " 时出错: " + e;
                debug(errorMsg);
                showDebug(errorMsg);
            }
        }
        
        debug("所有图层导出完成，共导出 " + exportedFiles.length + " 个文件");
        return exportedFiles;
    } catch (e) {
        var errorMsg = "导出图层过程中出错: " + e;
        debug(errorMsg);
        alert(errorMsg);
        return null;
    }
}

// 调用Python脚本执行OCR识别
function runOCRProcess(exportedFiles, apiKey, secretKey, recognitionType, exportScope) {
    debug("开始OCR识别过程");
    var scriptFolder = new File($.fileName).parent;
    var pythonExePath = scriptFolder.fsName + "/OCR识别核心.exe";
    var pythonScriptPath = scriptFolder.fsName + "/OCR识别核心.py";
    
    debug("检查OCR核心程序路径");
    var pythonFile = new File(pythonExePath);
    var useExe = pythonFile.exists;
    
    if (useExe) {
        debug("找到EXE程序: " + pythonExePath);
    } else {
        pythonFile = new File(pythonScriptPath);
        if (pythonFile.exists) {
            debug("找到Python脚本: " + pythonScriptPath);
        } else {
            var errorMsg = "无法找到OCR识别核心程序，请确保在同一目录下存在'OCR识别核心.exe'或'OCR识别核心.py'文件";
            debug(errorMsg);
            alert(errorMsg);
            return null;
        }
    }
    
    // 创建临时文件夹 - 使用绝对路径
    var tempFolderPath = Folder.temp + "/PhotoshopOCR";
    var tempFolder = new Folder(tempFolderPath);
    debug("临时文件夹路径: " + tempFolderPath);
    
    if (!tempFolder.exists) {
        if(tempFolder.create()) {
            debug("临时文件夹创建成功: " + tempFolder.fsName);
        } else {
            debug("临时文件夹创建失败!");
        }
    } else {
        debug("临时文件夹已存在: " + tempFolder.fsName);
    }
    
    // 检查临时文件夹是否真的存在
    var checkFolder = new Folder(tempFolderPath);
    if (!checkFolder.exists) {
        debug("临时文件夹不存在，尝试创建完整路径");
        // 尝试创建完整路径
        var fullPath = Folder.temp.fsName + "/PhotoshopOCR";
        fullPath = fullPath.replace(/\\/g, "/");
        tempFolder = new Folder(fullPath);
        if (!tempFolder.exists) {
            tempFolder.create();
            debug("使用完整路径创建临时文件夹: " + fullPath);
        }
    }
    
    // 清理可能存在的旧结果文件
    var resultFileName = "results.json";
    var resultFilePath = tempFolderPath + "/" + resultFileName;
    debug("结果文件路径: " + resultFilePath);
    
    var oldResultFile = new File(resultFilePath);
    if (oldResultFile.exists) {
        try {
            oldResultFile.remove();
            debug("删除已存在的旧结果文件");
        } catch (e) {
            debug("删除旧结果文件失败: " + e);
        }
    }
    
    // 创建参数文件
    debug("创建参数文件");
    var paramsFilePath = tempFolderPath + "/params.json";
    var paramsFile = new File(paramsFilePath);
    var paramsData = {
        api_key: apiKey,
        secret_key: secretKey,
        files: exportedFiles,
        recognition_type: recognitionType,
        export_scope: exportScope
    };
    
    try {
        paramsFile.open("w");
        debug("写入参数数据: " + JSON.stringify(paramsData).substring(0, 100) + "...");
        paramsFile.write(JSON.stringify(paramsData));
        paramsFile.close();
        debug("参数文件已保存: " + paramsFile.fsName);
    } catch (e) {
        var errorMsg = "创建参数文件时出错: " + e;
        debug(errorMsg);
        showDebug(errorMsg);
        return null;
    }
    
    // 再次检查参数文件是否创建成功
    var checkParamsFile = new File(paramsFilePath);
    if (!checkParamsFile.exists) {
        debug("参数文件创建失败，无法继续");
        alert("无法创建参数文件，OCR识别无法继续");
        return null;
    } else {
        debug("参数文件检查成功: " + checkParamsFile.fsName + ", 大小: " + checkParamsFile.length + "字节");
    }
    
    // 使用完整绝对路径
    var absoluteParamsPath = paramsFile.fsName;
    var absoluteResultPath = new File(resultFilePath).fsName;
    debug("参数文件绝对路径: " + absoluteParamsPath);
    debug("结果文件绝对路径: " + absoluteResultPath);

    // 执行Python脚本
    debug("准备执行OCR程序");

    try {
        // 构建命令行
        var cmd = '';
        
        // 使用桌面作为结果路径，避免权限问题
        var desktopResultPath = Folder.desktop.fsName + "/OCR_Results.json";
        desktopResultPath = desktopResultPath.replace(/\\/g, "/");
        debug("设置桌面结果文件路径: " + desktopResultPath);
        
        if (useExe) {
            debug("使用EXE程序");
            cmd = '"' + pythonExePath + '" "' + absoluteParamsPath + '" "' + desktopResultPath + '"';
        } else {
            debug("使用Python脚本");
            // 使用绝对路径引用python
            var pythonPath = "pythonw"; // 使用pythonw而不是python来避免显示控制台窗口
            try {
                // 测试Python是否存在于PATH中
                var testCmd = "pythonw --version";
                debug("测试Python命令: " + testCmd);
                try {
                    var testResult = app.system(testCmd);
                    debug("Python测试结果: " + testResult);
                } catch (e) {
                    debug("pythonw命令测试失败，尝试python命令");
                    pythonPath = "python";
                }
            } catch (e) {
                debug("Python命令测试失败: " + e);
                // 尝试使用py命令
                pythonPath = "py -3 -W ignore";
                debug("尝试使用py命令替代");
            }
            
            cmd = pythonPath + ' "' + pythonScriptPath + '" "' + absoluteParamsPath + '" "' + desktopResultPath + '"';
        }
        debug("执行命令: " + cmd);
        
        // 记录执行环境
        debug("当前工作目录: " + Folder.current.fsName);
        debug("脚本目录: " + scriptFolder.fsName);
        debug("系统环境: " + $.os);
        
        // 将命令写入临时VBS文件，隐藏CMD窗口执行
        var vbsFile = new File(Folder.temp + "/PhotoshopOCR_hidden.vbs");
        vbsFile.open("w");
        vbsFile.writeln('Set WshShell = CreateObject("WScript.Shell")');
        vbsFile.writeln('WshShell.CurrentDirectory = "' + scriptFolder.fsName.replace(/\\/g, "\\\\") + '"');
        vbsFile.writeln('WshShell.Run "' + cmd.replace(/"/g, '""').replace(/\\/g, "\\\\") + '", 0, True');
        vbsFile.close();
        debug("创建临时VBS文件: " + vbsFile.fsName);
        
        // 使用app.system执行VBS文件
        debug("开始执行外部命令(隐藏窗口)");
        app.system('wscript.exe "' + vbsFile.fsName + '"');
        debug("外部命令执行完成");
        
        // 等待结果文件生成，最多等待20秒
        debug("等待结果文件生成");
        var startTime = new Date().getTime();
        var timeout = 20000; // 20秒超时
        var fileGenerated = false;
        var checkInterval = 1000; // 每1秒检查一次
        var lastCheckTime = startTime;
        
        while (new Date().getTime() - startTime < timeout) {
            var currentTime = new Date().getTime();
            if (currentTime - lastCheckTime >= checkInterval) {
                lastCheckTime = currentTime;
                debug("检查结果文件是否生成，已等待: " + ((currentTime - startTime) / 1000) + "秒");
                
                // 重建文件引用以获取最新状态
                var checkFile = new File(desktopResultPath);
                
                if (checkFile.exists) {
                    fileGenerated = true;
                    debug("结果文件已生成，等待时间: " + ((currentTime - startTime) / 1000) + "秒");
                    break;
                } else {
                    // 检查Python调试日志是否生成
                    var pyDebugLog = new File(Folder.desktop + "/PhotoshopOCR_Python_Debug.log");
                    if (pyDebugLog.exists) {
                        debug("Python调试日志已生成，但结果文件尚未生成");
                    } else {
                        debug("Python调试日志尚未生成，可能Python未被调用");
                    }
                }
            }
            $.sleep(100); // 减小sleep时间以减少CPU使用
        }
        
        if (!fileGenerated) {
            var errorMsg = "OCR识别超时，未能生成结果文件。等待时间: " + (timeout / 1000) + "秒";
            debug(errorMsg);
            alert("OCR识别超时，可能是网络问题或图像太大导致处理时间过长");
            return null;
        }
        
        debug("结果文件已生成");
    } catch (e) {
        var errorMsg = "执行OCR程序时出错: " + e;
        debug(errorMsg);
        showDebug(errorMsg);
        return null;
    }
    
    // 检查是否生成了结果文件
    var resultFile = new File(desktopResultPath);
    debug("最终检查结果文件是否存在: " + resultFile.fsName);
    if (!resultFile.exists) {
        var errorMsg = "OCR识别失败，未生成结果文件。";
        debug(errorMsg);
        alert(errorMsg);
        return null;
    }
    
    // 读取结果文件 - 尝试多种编码方式
    debug("开始读取结果文件: " + resultFile.fsName);
    var jsonResult = "";

    try {
        // 直接读取文本模式
        resultFile.encoding = "UTF-8";
        resultFile.open("r");
        jsonResult = resultFile.read();
        resultFile.close();
        
        debug("UTF-8编码读取结果文件，内容长度: " + jsonResult.length + " 字符");
        
        if (jsonResult.length === 0) {
            // 尝试二进制读取
            resultFile.encoding = "binary";
            resultFile.open("r");
            var binaryContent = resultFile.read();
            resultFile.close();
            
            debug("二进制读取结果文件，长度: " + binaryContent.length + " 字节");
            jsonResult = binaryContent;
        }
        
        if (jsonResult.length === 0) {
            var errorMsg = "结果文件为空";
            debug(errorMsg);
            showDebug(errorMsg);
            return null;
        }
        
        // 尝试直接用正则表达式提取OCR结果
        debug("尝试直接提取OCR结果内容");
        var results = [];
        
        // 提取text字段
        var textMatch = jsonResult.match(/"text"\s*:\s*"([^"]*)"/);
        if (textMatch && textMatch[1]) {
            var text = textMatch[1];
            debug("提取到的文本: " + text);
            
            // 替换转义的换行符为\r\n
            text = text.replace(/\\n/g, "\r\n");
            debug("处理换行后的文本: " + text);
            
            // 提取位置信息
            var posX = 0, posY = 0;
            var posXMatch = jsonResult.match(/"x"\s*:\s*(\d+)/);
            var posYMatch = jsonResult.match(/"y"\s*:\s*(\d+)/);
            
            if (posXMatch && posXMatch[1]) {
                posX = parseInt(posXMatch[1]);
            }
            
            if (posYMatch && posYMatch[1]) {
                posY = parseInt(posYMatch[1]);
            }
            
            debug("提取到的位置: x=" + posX + ", y=" + posY);
            
            // 提取图层名称
            var layerName = "未命名图层";
            var layerNameMatch = jsonResult.match(/"layerName"\s*:\s*"([^"]*)"/);
            
            if (layerNameMatch && layerNameMatch[1]) {
                layerName = layerNameMatch[1];
            }
            
            debug("提取到的图层名称: " + layerName);
            
            // 构建结果对象
            var result = {
                text: text,
                position: {
                    x: posX,
                    y: posY
                },
                layerName: layerName
            };
            
            results.push(result);
            debug("成功构建结果对象");
            
            return results;
        }
        
        // 如果正则提取失败，尝试解析JSON
        debug("正则提取失败，尝试解析完整JSON");
        
        // 清理JSON字符串
        jsonResult = jsonResult.replace(/[\r\n\t]+/g, ' ').replace(/\s{2,}/g, ' ');
        debug("清理后的JSON: " + jsonResult.substring(0, 200));
        
        try {
            // 尝试使用eval解析
            var evalResults = eval('(' + jsonResult + ')');
            debug("使用eval成功解析结果");
            
            // 处理换行符
            if (evalResults && evalResults.length > 0) {
                for (var i = 0; i < evalResults.length; i++) {
                    if (evalResults[i].text) {
                        evalResults[i].text = evalResults[i].text.replace(/\\n/g, "\r\n");
                        debug("处理了结果 #" + i + " 中的换行符");
                    }
                }
            }
            
            return evalResults;
        } catch (evalError) {
            debug("eval解析失败: " + evalError);
            
            // 最后尝试手动解析
            try {
                debug("尝试手动构建结果");
                // 检查是否包含错误信息
                var errorMatch = jsonResult.match(/"error"\s*:\s*"([^"]*)"/);
                if (errorMatch && errorMatch[1]) {
                    var errorMsg = "OCR识别出错: " + errorMatch[1];
                    debug(errorMsg);
                    alert(errorMsg);
                    return null;
                }
                
                // 告知用户无法解析
                alert("无法解析OCR识别结果，但已提取文本内容");
                return null;
            } catch (e3) {
                debug("最终尝试也失败: " + e3);
                alert("无法解析OCR识别结果");
                return null;
            }
        }
    } catch (e) {
        var errorMsg = "读取结果文件时出错: " + e;
        debug(errorMsg);
        showDebug(errorMsg);
        return null;
    }
}

// 将OCR结果应用到Photoshop文档
function applyOCRResults(results) {
    debug("应用OCR结果到文档");
    var doc = app.activeDocument;
    
    if (!results || !results.length) {
        debug("没有可应用的OCR结果");
        return false;
    }
    
    // 根据文档宽度计算合适的字体大小
    var docWidthMM = doc.width.as('mm');
    var fontSize = docWidthMM / 15; // 文档宽度除以15得到合适的字体大小(mm)
    debug("文档宽度: " + docWidthMM + "mm, 计算字体大小: " + fontSize + "mm");
    
    // 获取文档中心点坐标
    var docCenterX = doc.width.value / 2;
    var docCenterY = doc.height.value / 2;
    debug("文档中心点: x=" + docCenterX + ", y=" + docCenterY);
    
    debug("OCR结果数量: " + results.length);
    for (var i = 0; i < results.length; i++) {
        var result = results[i];
        debug("处理结果 #" + i + ": " + (result.layerName || "未命名") + ", 文本长度: " + (result.text ? result.text.length : 0));
        
        if (result.error) {
            debug("OCR结果错误: " + result.error);
            continue;
        }
        
        if (!result.text) {
            debug("OCR结果无文本");
            continue;
        }
        
        try {
            // 检查文本中是否包含\n
            debug("原始文本中是否包含'\\n'字符: " + (result.text.indexOf('\\n') > -1));
            debug("原始文本中是否包含换行符(\\n): " + (result.text.indexOf('\n') > -1));
            
            // 处理文本中的换行符
            var processedText = result.text;
            // 将文本中的\n替换为\r\n换行符 - PhotoshopCSS需要\r\n作为换行符
            processedText = processedText.replace(/\\n/g, "\r\n");
            debug("处理后的文本: " + processedText);
            
            // 创建文本图层
            var textLayer = doc.artLayers.add();
            textLayer.kind = LayerKind.TEXT;
            textLayer.name = result.layerName + "_OCR文本";
            
            // 设置文本内容
            var textItem = textLayer.textItem;
            textItem.contents = processedText;
            
            // 设置文本样式
            textItem.size = fontSize + " mm"; // 根据文档宽度计算的字体大小
            debug("设置字体大小: " + fontSize + "mm");
            textItem.color.rgb.red = 0;
            textItem.color.rgb.green = 0;
            textItem.color.rgb.blue = 0;
            
            // 计算文本位置使其居中 - 使用点文本直接居中
            textItem.position = [docCenterX, docCenterY];
            debug("设置文本位置到文档中心: x=" + docCenterX + ", y=" + docCenterY);
            
            debug("成功创建文本图层: " + textLayer.name);
        } catch (e) {
            var errorMsg = "创建文本图层时出错: " + e;
            debug(errorMsg);
            showDebug(errorMsg);
        }
    }
    
    debug("OCR结果应用完成");
    return true;
}

// 主界面
function showMainDialog() {
    debug("显示主界面");
    var config = readConfigFile();
    
    // 创建对话框
    var dialog = new Window("dialog", "Photoshop OCR识别");
    dialog.orientation = "column";
    dialog.alignChildren = ["fill", "top"];
    dialog.spacing = 10;
    dialog.margins = 16;
    
    // 识别范围
    var recognitionScopePanel = dialog.add("panel", undefined, "识别范围");
    recognitionScopePanel.orientation = "column";
    recognitionScopePanel.alignChildren = ["left", "top"];
    recognitionScopePanel.spacing = 5;
    recognitionScopePanel.margins = 10;
    
    var selectedLayerRadio = recognitionScopePanel.add("radiobutton", undefined, "选择图层识别");
    var selectedAreaRadio = recognitionScopePanel.add("radiobutton", undefined, "选区识别");
    
    selectedLayerRadio.value = true;
    
    // 识别类型
    var recognitionTypePanel = dialog.add("panel", undefined, "识别类型");
    recognitionTypePanel.orientation = "column";
    recognitionTypePanel.alignChildren = ["left", "top"];
    recognitionTypePanel.spacing = 5;
    recognitionTypePanel.margins = 10;
    
    var generalTextRadio = recognitionTypePanel.add("radiobutton", undefined, "通用文字识别（高精度版）");
    var handwritingRadio = recognitionTypePanel.add("radiobutton", undefined, "手写文字识别");
    
    generalTextRadio.value = true;
    
    // 调试选项
    var debugGroup = dialog.add("group");
    debugGroup.orientation = "row";
    debugGroup.alignChildren = ["left", "center"];
    var debugCheckbox = debugGroup.add("checkbox", undefined, "启用详细调试信息");
    
    // 按钮
    var buttonGroup = dialog.add("group");
    buttonGroup.orientation = "row";
    buttonGroup.alignChildren = ["center", "center"];
    buttonGroup.spacing = 10;
    
    var okButton = buttonGroup.add("button", undefined, "确定", {name: "ok"});
    var apiConfigButton = buttonGroup.add("button", undefined, "API配置");
    var closeButton = buttonGroup.add("button", undefined, "关闭", {name: "cancel"});
    
    // 事件处理
    apiConfigButton.onClick = function() {
        debug("点击API配置按钮");
        var configResult = showAPIConfigDialog(config.apiKey, config.secretKey);
        if (configResult.confirmed) {
            config.apiKey = configResult.apiKey;
            config.secretKey = configResult.secretKey;
            saveConfigFile(config.apiKey, config.secretKey);
            debug("API配置已更新");
        }
    };
    
    var dialogResult = {
        confirmed: false,
        exportScope: "layer",
        recognitionType: "general",
        debugMode: false
    };
    
    okButton.onClick = function() {
        debug("点击确定按钮");
        if (!config.apiKey || !config.secretKey) {
            debug("API密钥未设置，显示配置对话框");
            alert("请先配置百度智能云API密钥！");
            var configResult = showAPIConfigDialog(config.apiKey, config.secretKey);
            if (configResult.confirmed) {
                config.apiKey = configResult.apiKey;
                config.secretKey = configResult.secretKey;
                saveConfigFile(config.apiKey, config.secretKey);
                debug("API配置已保存");
            } else {
                debug("用户取消了API配置");
                return;
            }
        }
        
        // 设置识别范围
        if (selectedLayerRadio.value) {
            dialogResult.exportScope = "layer";
            debug("选择了图层识别范围");
        } else if (selectedAreaRadio.value) {
            dialogResult.exportScope = "selection";
            debug("选择了选区识别范围");
        }
        
        // 设置识别类型
        if (generalTextRadio.value) {
            dialogResult.recognitionType = "general";
            debug("选择了通用文字识别类型");
        } else if (handwritingRadio.value) {
            dialogResult.recognitionType = "handwriting";
            debug("选择了手写文字识别类型");
        }
        
        // 设置调试模式
        dialogResult.debugMode = debugCheckbox.value;
        debug("调试模式: " + (dialogResult.debugMode ? "开启" : "关闭"));
        
        dialogResult.confirmed = true;
        dialog.close();
    };
    
    closeButton.onClick = function() {
        debug("点击关闭按钮");
        dialog.close();
    };
    
    dialog.show();
    
    debug("主界面关闭，确认: " + dialogResult.confirmed);
    return dialogResult;
}

// 主函数
function main() {
    debug("=== 启动PhotoshopOCR脚本 ===");
    
    if (!documents.length) {
        var errorMsg = "请先打开一个文档！";
        debug(errorMsg);
        alert(errorMsg);
        return;
    }
    
    var dialogResult = showMainDialog();
    
    if (!dialogResult.confirmed) {
        debug("用户取消了操作");
        return;
    }
    
    var config = readConfigFile();
    
    if (!config.apiKey || !config.secretKey) {
        var errorMsg = "未设置API密钥，无法继续操作";
        debug(errorMsg);
        alert(errorMsg);
        return;
    }
    
    debug("开始导出选中的图层");
    var exportedFiles = exportSelectedLayersToTemp();
    
    if (!exportedFiles || exportedFiles.length === 0) {
        debug("没有可用的导出文件");
        return;
    }
    
    // 显示进度
    var progressWin = new Window("palette", "正在进行OCR识别...");
    progressWin.orientation = "column";
    progressWin.alignChildren = ["center", "center"];
    progressWin.spacing = 10;
    progressWin.margins = 16;
    
    progressWin.add("statictext", undefined, "正在处理，请稍候...");
    var progressBar = progressWin.add("progressbar", undefined, 0, 100);
    progressBar.preferredSize.width = 300;
    progressBar.value = 0;
    
    debug("显示进度窗口");
    progressWin.show();
    
    // 执行OCR识别
    debug("开始OCR识别");
    var recognitionType = dialogResult.recognitionType;
    var exportScope = dialogResult.exportScope;
    
    if (dialogResult.debugMode) {
        showDebug("开始OCR识别，类型: " + recognitionType + ", 范围: " + exportScope);
    }
    
    var results = runOCRProcess(exportedFiles, config.apiKey, config.secretKey, recognitionType, exportScope);
    
    debug("OCR识别完成，关闭进度窗口");
    progressWin.close();
    
    if (!results) {
        var errorMsg = "OCR识别失败，请检查API密钥和网络连接";
        debug(errorMsg);
        alert(errorMsg);
        return;
    }
    
    // 应用结果
    debug("应用OCR结果");
    if (applyOCRResults(results)) {
        var successMsg = "OCR识别完成";
        debug(successMsg);
        alert(successMsg);
    } else {
        var errorMsg = "应用OCR结果时出错";
        debug(errorMsg);
        alert(errorMsg);
    }
    
    debug("=== PhotoshopOCR脚本执行结束 ===");
}

// 执行主函数
main(); 