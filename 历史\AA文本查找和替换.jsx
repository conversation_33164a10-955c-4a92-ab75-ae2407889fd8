/**
 * Photoshop 高级文本查找替换工具
 * 功能：支持多文档处理、智能查找、样式保留、日志系统等
 * 作者：Cody
 * 版本：1.0
 */

// 启用严格模式
"use strict";

// 确保可以撤销
app.activeDocument.suspendHistory("文本查找替换", "main()");

function main() {
    // 检查是否有打开的文档
    if (documents.length === 0) {
        alert("请先打开一个文档！");
        return;
    }

    // 创建主对话框
    var dlg = new Window("dialog", "高级文本查找替换工具");
    dlg.orientation = "column";
    dlg.alignChildren = "fill";
    
    // 查找和替换面板
    var findReplacePanel = dlg.add("panel", undefined, "查找和替换");
    findReplacePanel.orientation = "column";
    findReplacePanel.alignChildren = "left";
    findReplacePanel.margins = 20;
    
    // 查找文本
    var findGroup = findReplacePanel.add("group");
    findGroup.orientation = "row";
    findGroup.alignChildren = "center";
    findGroup.add("statictext", undefined, "查找内容:");
    var findText = findGroup.add("edittext", undefined, "", {multiline: true});
    findText.preferredSize.width = 300;
    findText.preferredSize.height = 60;
    
    // 替换文本
    var replaceGroup = findReplacePanel.add("group");
    replaceGroup.orientation = "row";
    replaceGroup.alignChildren = "center";
    replaceGroup.add("statictext", undefined, "替换为:");
    var replaceText = replaceGroup.add("edittext", undefined, "", {multiline: true});
    replaceText.preferredSize.width = 300;
    replaceText.preferredSize.height = 60;
    
    // 处理范围面板
    var scopePanel = dlg.add("panel", undefined, "处理范围");
    scopePanel.orientation = "column";
    scopePanel.alignChildren = "left";
    scopePanel.margins = 20;
    
    // 范围选项
    var selectedLayersRadio = scopePanel.add("radiobutton", undefined, "选定图层");
    var activeDocRadio = scopePanel.add("radiobutton", undefined, "当前活动文档");
    var allDocsRadio = scopePanel.add("radiobutton", undefined, "所有已打开文档");
    
    // 默认选择当前文档
    activeDocRadio.value = true;
    
    // 匹配选项面板
    var matchPanel = dlg.add("panel", undefined, "匹配选项");
    matchPanel.orientation = "column";
    matchPanel.alignChildren = "left";
    matchPanel.margins = 20;
    
    // 精确匹配选项
    var exactMatchCheck = matchPanel.add("checkbox", undefined, "精确匹配 (区分大小写且全词匹配)");
    
    // 匹配模式
    var matchModeGroup = matchPanel.add("group");
    matchModeGroup.orientation = "row";
    matchModeGroup.alignChildren = "center";
    matchModeGroup.add("statictext", undefined, "匹配模式:");
    var matchModeDropdown = matchModeGroup.add("dropdownlist", undefined, ["包含", "开头于", "结尾于", "正则表达式"]);
    matchModeDropdown.selection = 0;
    
    // 替换选项面板
    var replaceOptionsPanel = dlg.add("panel", undefined, "替换选项");
    replaceOptionsPanel.orientation = "column";
    replaceOptionsPanel.alignChildren = "left";
    replaceOptionsPanel.margins = 20;
    
    var preserveStyleCheck = replaceOptionsPanel.add("checkbox", undefined, "保留原始文本样式");
    preserveStyleCheck.value = true;
    
    var autoResizeCheck = replaceOptionsPanel.add("checkbox", undefined, "自动适应文本框尺寸");
    autoResizeCheck.value = true;
    
    var inheritStyleCheck = replaceOptionsPanel.add("checkbox", undefined, "继承新文本样式 (如果可用)");
    inheritStyleCheck.value = false;
    
    // 过滤选项面板
    var filterPanel = dlg.add("panel", undefined, "过滤选项");
    filterPanel.orientation = "column";
    filterPanel.alignChildren = "left";
    filterPanel.margins = 20;
    
    var excludeHiddenCheck = filterPanel.add("checkbox", undefined, "排除隐藏图层");
    excludeHiddenCheck.value = true;
    
    var skipLockedCheck = filterPanel.add("checkbox", undefined, "跳过锁定图层");
    skipLockedCheck.value = true;
    
    var textLayersOnlyCheck = filterPanel.add("checkbox", undefined, "仅处理文本图层");
    textLayersOnlyCheck.value = true;
    
    // 按钮组
    var btnGroup = dlg.add("group");
    btnGroup.orientation = "row";
    btnGroup.alignChildren = "center";
    
    var cancelBtn = btnGroup.add("button", undefined, "取消");
    var previewBtn = btnGroup.add("button", undefined, "预览");
    var replaceBtn = btnGroup.add("button", undefined, "替换");
    
    // 日志区域
    var logPanel = dlg.add("panel", undefined, "操作日志");
    logPanel.orientation = "column";
    logPanel.alignChildren = "fill";
    logPanel.margins = 20;
    
    var logText = logPanel.add("edittext", undefined, "", {multiline: true, readonly: true});
    logText.preferredSize.width = 400;
    logText.preferredSize.height = 100;
    
    // 存储查找结果
    var findResults = {
        totalDocs: 0,
        totalLayers: 0,
        matchedLayers: [],
        skippedLayers: []
    };
    
    // 预览按钮事件
    previewBtn.onClick = function() {
        if (!validateInputs()) return;
        
        // 清空之前的结果
        findResults = {
            totalDocs: 0,
            totalLayers: 0,
            matchedLayers: [],
            skippedLayers: []
        };
        
        // 执行查找
        findTextInLayers(true);
        
        // 显示预览结果
        var previewMsg = "预览结果:\n";
        previewMsg += "处理文档数: " + findResults.totalDocs + "\n";
        previewMsg += "扫描图层数: " + findResults.totalLayers + "\n";
        previewMsg += "匹配图层数: " + findResults.matchedLayers.length + "\n";
        previewMsg += "跳过图层数: " + findResults.skippedLayers.length + "\n\n";
        
        if (findResults.matchedLayers.length > 0) {
            previewMsg += "匹配图层:\n";
            for (var i = 0; i < Math.min(findResults.matchedLayers.length, 10); i++) {
                var layer = findResults.matchedLayers[i];
                previewMsg += "- " + layer.docName + " > " + layer.name + "\n";
            }
            
            if (findResults.matchedLayers.length > 10) {
                previewMsg += "... 以及其他 " + (findResults.matchedLayers.length - 10) + " 个图层\n";
            }
        } else {
            previewMsg += "未找到匹配项。\n";
        }
        
        logText.text = previewMsg;
    };
    
    // 替换按钮事件
    replaceBtn.onClick = function() {
        if (!validateInputs()) return;
        
        // 如果没有预览，先执行查找
        if (findResults.matchedLayers.length === 0) {
            findTextInLayers(true);
        }
        
        // 确认替换
        if (findResults.matchedLayers.length === 0) {
            alert("未找到匹配项，无需替换。");
            return;
        }
        
        var confirmMsg = "确认替换 " + findResults.matchedLayers.length + " 处文本吗？\n";
        confirmMsg += "此操作将影响 " + findResults.totalDocs + " 个文档中的 " + findResults.matchedLayers.length + " 个图层。";
        
        if (!confirm(confirmMsg)) {
            return;
        }
        
        // 执行替换
        var replaceResults = replaceTextInLayers();
        
        // 显示替换结果
        var resultMsg = "替换完成:\n";
        resultMsg += "处理文档数: " + replaceResults.totalDocs + "\n";
        resultMsg += "成功替换数: " + replaceResults.successCount + "\n";
        resultMsg += "失败替换数: " + replaceResults.failCount + "\n";
        
        if (replaceResults.errors.length > 0) {
            resultMsg += "\n错误信息:\n";
            for (var i = 0; i < Math.min(replaceResults.errors.length, 5); i++) {
                resultMsg += "- " + replaceResults.errors[i] + "\n";
            }
            
            if (replaceResults.errors.length > 5) {
                resultMsg += "... 以及其他 " + (replaceResults.errors.length - 5) + " 个错误\n";
            }
        }
        
        logText.text = resultMsg;
        
        // 如果全部成功，显示成功消息
        if (replaceResults.failCount === 0) {
            alert("替换成功完成！共替换 " + replaceResults.successCount + " 处文本。");
        } else {
            alert("替换部分完成。成功: " + replaceResults.successCount + ", 失败: " + replaceResults.failCount + "。请查看日志了解详情。");
        }
    };
    
    // 取消按钮事件
    cancelBtn.onClick = function() {
        dlg.close();
    };
    
    // 验证输入
    function validateInputs() {
        if (findText.text === "") {
            alert("请输入要查找的文本！");
            findText.active = true;
            return false;
        }
        return true;
    }
    
    // 查找文本函数
    function findTextInLayers(previewOnly) {
        var searchText = findText.text;
        var isExactMatch = exactMatchCheck.value;
        var matchMode = matchModeDropdown.selection.index;
        var excludeHidden = excludeHiddenCheck.value;
        var skipLocked = skipLockedCheck.value;
        var textLayersOnly = textLayersOnlyCheck.value;
        
        // 确定处理范围
        var docsToProcess = [];
        
        if (selectedLayersRadio.value) {
            docsToProcess.push(app.activeDocument);
        } else if (activeDocRadio.value) {
            docsToProcess.push(app.activeDocument);
        } else if (allDocsRadio.value) {
            for (var i = 0; i < app.documents.length; i++) {
                docsToProcess.push(app.documents[i]);
            }
        }
        
        findResults.totalDocs = docsToProcess.length;
        
        // 遍历文档
        for (var d = 0; d < docsToProcess.length; d++) {
            var currentDoc = docsToProcess[d];
            var originalDoc = app.activeDocument;
            
            try {
                app.activeDocument = currentDoc;
                
                // 获取要处理的图层
                var layersToProcess = [];
                
                if (selectedLayersRadio.value) {
                    // 获取选中的图层
                    var selectedLayers = getSelectedLayers();
                    for (var s = 0; s < selectedLayers.length; s++) {
                        layersToProcess.push(selectedLayers[s]);
                    }
                } else {
                    // 获取所有图层
                    collectAllLayers(currentDoc.layers, layersToProcess);
                }
                
                findResults.totalLayers += layersToProcess.length;
                
                // 遍历图层
                for (var l = 0; l < layersToProcess.length; l++) {
                    var layer = layersToProcess[l];
                    
                    // 检查过滤条件
                    if (excludeHidden && !layer.visible) {
                        findResults.skippedLayers.push({
                            name: layer.name,
                            docName: currentDoc.name,
                            reason: "隐藏图层"
                        });
                        continue;
                    }
                    
                    if (skipLocked && layer.locked) {
                        findResults.skippedLayers.push({
                            name: layer.name,
                            docName: currentDoc.name,
                            reason: "锁定图层"
                        });
                        continue;
                    }
                    
                    if (textLayersOnly && layer.kind != LayerKind.TEXT) {
                        continue;
                    }
                    
                    // 只处理文本图层
                    if (layer.kind == LayerKind.TEXT) {
                        var layerText = layer.textItem.contents;
                        
                        // 检查是否匹配
                        var isMatch = false;
                        
                        if (matchMode === 3) {
                            // 正则表达式模式
                            try {
                                var regex = new RegExp(searchText, isExactMatch ? "" : "i");
                                isMatch = regex.test(layerText);
                            } catch (e) {
                                alert("正则表达式错误: " + e.message);
                                return;
                            }
                        } else {
                            // 其他匹配模式
                            if (!isExactMatch) {
                                layerText = layerText.toLowerCase();
                                searchText = searchText.toLowerCase();
                            }
                            
                            switch (matchMode) {
                                case 0: // 包含
                                    isMatch = layerText.indexOf(searchText) !== -1;
                                    break;
                                case 1: // 开头于
                                isMatch = layerText.indexOf(searchText) === 0;
                                break;
                            case 2: // 结尾于
                                isMatch = layerText.lastIndexOf(searchText) === (layerText.length - searchText.length);
                                break;
                        }
                    }
                    
                    if (isMatch) {
                        findResults.matchedLayers.push({
                            layer: layer,
                            name: layer.name,
                            docName: currentDoc.name,
                            docIndex: d,
                            text: layerText
                        });
                    }
                }
            }
        } catch (e) {
            alert("处理文档 " + currentDoc.name + " 时出错: " + e.message);
        } finally {
            // 恢复原始文档
            app.activeDocument = originalDoc;
        }
    }
    
    return findResults;
}

// 替换文本函数
function replaceTextInLayers() {
    var replaceString = replaceText.text;
    var preserveStyle = preserveStyleCheck.value;
    var autoResize = autoResizeCheck.value;
    var inheritStyle = inheritStyleCheck.value;
    var matchMode = matchModeDropdown.selection.index;
    var isExactMatch = exactMatchCheck.value;
    var searchText = findText.text;
    
    var results = {
        totalDocs: findResults.totalDocs,
        successCount: 0,
        failCount: 0,
        errors: []
    };
    
    // 创建处理过的文档集合
    var processedDocs = {};
    
    // 遍历匹配的图层
    for (var i = 0; i < findResults.matchedLayers.length; i++) {
        var match = findResults.matchedLayers[i];
        var layer = match.layer;
        var docIndex = match.docIndex;
        
        try {
            // 激活文档
            app.activeDocument = app.documents[docIndex];
            
            // 记录已处理的文档
            if (!processedDocs[docIndex]) {
                // 为每个文档创建历史快照
                app.activeDocument.suspendHistory("文本替换操作", "");
                processedDocs[docIndex] = true;
            }
            
            // 获取原始文本和样式信息
            var originalText = layer.textItem.contents;
            var originalFont = null;
            var originalColor = null;
            var originalSize = null;
            var originalTracking = null;
            
            if (preserveStyle) {
                try {
                    originalFont = layer.textItem.font;
                    originalColor = layer.textItem.color;
                    originalSize = layer.textItem.size;
                    originalTracking = layer.textItem.tracking;
                } catch (e) {
                    results.errors.push("无法获取图层 " + layer.name + " 的样式: " + e.message);
                }
            }
            
            // 执行替换
            var newText = originalText;
            
            if (matchMode === 3) {
                // 正则表达式替换
                try {
                    var regex = new RegExp(searchText, isExactMatch ? "g" : "gi");
                    newText = originalText.replace(regex, replaceString);
                } catch (e) {
                    results.errors.push("正则表达式替换错误: " + e.message);
                    results.failCount++;
                    continue;
                }
            } else {
                // 标准替换
                if (matchMode === 0) { // 包含
                    if (isExactMatch) {
                        newText = originalText.split(searchText).join(replaceString);
                    } else {
                        var regex = new RegExp(escapeRegExp(searchText), "gi");
                        newText = originalText.replace(regex, replaceString);
                    }
                } else if (matchMode === 1) { // 开头于
                    if (originalText.indexOf(searchText) === 0) {
                        newText = replaceString + originalText.substring(searchText.length);
                    }
                } else if (matchMode === 2) { // 结尾于
                    if (originalText.lastIndexOf(searchText) === (originalText.length - searchText.length)) {
                        newText = originalText.substring(0, originalText.length - searchText.length) + replaceString;
                    }
                }
            }
            
            // 应用新文本
            layer.textItem.contents = newText;
            
            // 恢复样式
            if (preserveStyle && !inheritStyle) {
                try {
                    if (originalFont) layer.textItem.font = originalFont;
                    if (originalColor) layer.textItem.color = originalColor;
                    if (originalSize) layer.textItem.size = originalSize;
                    if (originalTracking) layer.textItem.tracking = originalTracking;
                } catch (e) {
                    results.errors.push("无法恢复图层 " + layer.name + " 的样式: " + e.message);
                }
            }
            
            // 自动调整文本框大小
            if (autoResize) {
                try {
                    // 尝试自动调整文本框大小
                    var idFtTe = charIDToTypeID("FtTe");
                    var desc = new ActionDescriptor();
                    var ref = new ActionReference();
                    ref.putEnumerated(charIDToTypeID("TxLr"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
                    desc.putReference(charIDToTypeID("null"), ref);
                    executeAction(idFtTe, desc, DialogModes.NO);
                } catch (e) {
                    results.errors.push("无法调整图层 " + layer.name + " 的文本框大小: " + e.message);
                }
            }
            
            results.successCount++;
        } catch (e) {
            results.errors.push("处理图层 " + match.name + " 时出错: " + e.message);
            results.failCount++;
        }
    }
    
    return results;
}

// 辅助函数：转义正则表达式特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 辅助函数：获取选中的图层
function getSelectedLayers() {
    var selectedLayers = [];
    
    try {
        // 获取当前选中的图层索引
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID('targetLayers'))) {
            desc = desc.getList(stringIDToTypeID('targetLayers'));
            var count = desc.count;
            
            for (var i = 0; i < count; i++) {
                try {
                    var ref = new ActionReference();
                    ref.putIndex(charIDToTypeID('Lyr '), desc.getReference(i).getIndex());
                    var layerDesc = executeActionGet(ref);
                    var layerName = layerDesc.getString(charIDToTypeID('Nm  '));
                    
                    // 获取图层对象
                    var layer = app.activeDocument.layers.getByName(layerName);
                    selectedLayers.push(layer);
                } catch (e) {
                    // 忽略错误
                }
            }
        } else {
            // 单个选中的图层
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        alert("获取选中图层时出错: " + e.message);
    }
    
    return selectedLayers;
}

// 辅助函数：递归收集所有图层
function collectAllLayers(layers, result) {
    for (var i = 0; i < layers.length; i++) {
        var layer = layers[i];
        
        // 添加当前图层
        result.push(layer);
        
        // 如果是图层组，递归处理
        if (layer.typename === "LayerSet") {
            collectAllLayers(layer.layers, result);
        }
    }
}

// 显示对话框
dlg.center();
dlg.show();
}

/**
* 处理字体缺失的辅助函数
* 尝试找到最相似的可用字体
*/
function findSimilarFont(missingFont) {
// 常见字体替代列表
var fontSubstitutes = {
    "Arial": ["Helvetica", "Verdana", "Tahoma"],
    "Times New Roman": ["Times", "Georgia", "Serif"],
    "Courier New": ["Courier", "Monaco", "Monospace"],
    "Georgia": ["Times New Roman", "Times", "Serif"],
    "Verdana": ["Arial", "Helvetica", "Tahoma"],
    "Tahoma": ["Arial", "Verdana", "Helvetica"],
    "Helvetica": ["Arial", "Verdana", "Tahoma"]
};

// 尝试找到替代字体
if (fontSubstitutes[missingFont]) {
    for (var i = 0; i < fontSubstitutes[missingFont].length; i++) {
        var substitute = fontSubstitutes[missingFont][i];
        try {
            // 检查字体是否可用
            var textLayer = app.activeDocument.artLayers.add();
            textLayer.kind = LayerKind.TEXT;
            textLayer.textItem.font = substitute;
            app.activeDocument.artLayers.remove(textLayer);
            return substitute;
        } catch (e) {
            // 字体不可用，继续尝试下一个
            try {
                app.activeDocument.artLayers.remove(textLayer);
            } catch (e) {}
        }
    }
}

// 如果没有找到合适的替代字体，返回系统默认字体
return app.fonts[0].postScriptName;
}