// 智能红章 V3.5 - 朋亿工具

function createUI() {
    var mainWindow = new Window("dialog", "朋亿工具：智能红章V3.5  作者：朋亿");
    mainWindow.orientation = "column";
    mainWindow.alignChildren = "left";
    mainWindow.size = [480, 410];
    mainWindow.minSize = [480, 410];
    mainWindow.maxSize = [490, 430];
    
    // 标题文本
    var titleText = mainWindow.add("statictext", undefined, "智能红章喷印联盟QQ群出品");
    titleText.graphics.font = ScriptUI.newFont("微软雅黑", "BOLD", 18);
    titleText.alignment = "center";
    titleText.preferredSize.height = 30;
    
    // 说明文本
    var descText = mainWindow.add("statictext", undefined, "本工具（包含所有迭代版本）仅用于优化打印效果，");
    descText.graphics.font = ScriptUI.newFont("微软雅黑", "REGULAR", 14);
    descText.alignment = "center";
    descText.preferredSize.height = 20;
    
    var legalText = mainWindow.add("statictext", undefined, "请合法合规使用。");
    legalText.graphics.font = ScriptUI.newFont("微软雅黑", "REGULAR", 14);
    legalText.alignment = "center";
    legalText.preferredSize.height = 20;
    
    var disclaimerText = mainWindow.add("statictext", undefined, "如因使用本工具产生的任何后果，作者不承担责任。");
    disclaimerText.graphics.font = ScriptUI.newFont("微软雅黑", "REGULAR", 14);
    disclaimerText.alignment = "center";
    disclaimerText.preferredSize.height = 20;
    
    var contactText = mainWindow.add("statictext", undefined, "  广告图文交流群 248884685");
    contactText.graphics.font = ScriptUI.newFont("微软雅黑", "REGULAR", 14);
    contactText.alignment = "center";
    
    // 分隔线
    var separator = mainWindow.add("statictext", undefined, "---------------------------------------------------------------------------------------------------------------------------------");
    separator.graphics.foregroundColor = separator.graphics.newPen(separator.graphics.PenType.SOLID_COLOR, [0, 0.4, 0], 1);
    
    // 文件选择区域
    var fileSelectionGroup = mainWindow.add("panel");
    fileSelectionGroup.orientation = "column";
    
    var currentFileRadio = fileSelectionGroup.add("radiobutton", undefined, "当前的");
    var allOpenFilesRadio = fileSelectionGroup.add("radiobutton", undefined, "所有打开的");
    var folderFilesRadio = fileSelectionGroup.add("radiobutton", undefined, "文件夹中的");
    
    currentFileRadio.value = true;
    folderFilesRadio.helpTip = "处理文件夹中的所有文件及子文件。————选择此功能后，点确定按钮再选择你要处理的文件夹";
    
    // 实时预览选项
    var previewCheckbox = mainWindow.add("checkbox", undefined, "启用实时预览（增加计算负担影响预览速度）");
    previewCheckbox.enabled = false;
    
    // 保存历史状态
    var activeHistoryState = null;
    if (app.documents.length > 0 && currentFileRadio.value) {
        activeHistoryState = app.activeDocument.activeHistoryState;
    }
    
    // 预览功能
    previewCheckbox.onClick = function() {
        if (!previewCheckbox.value && app.documents.length > 0 && activeHistoryState != null) {
            app.activeDocument.activeHistoryState = activeHistoryState;
        } else {
            updatePreview();
        }
    };
    
    // 设置面板
    var settingsPanel = mainWindow.add("panel", undefined, "-- 设置数值 --");
    settingsPanel.preferredSize = [280, 120];
    settingsPanel.orientation = "column";
    settingsPanel.alignChildren = "left";
    
    // RGB设置组
    var rgbGroup = settingsPanel.add("group");
    rgbGroup.orientation = "row";
    rgbGroup.add("statictext", undefined, "追加数值（1-5）");
    
    var rgbSlider = rgbGroup.add("slider", undefined, 3, 1, 5);
    rgbSlider.preferredSize.width = 150;
    
    var rgbValue = rgbGroup.add("statictext", undefined, '3');
    
    // 细节处理选项
    var detailCheckbox = settingsPanel.add("checkbox", undefined, "使用细节处理");
    detailCheckbox.value = false;
    
    // 模式选择面板
    var modePanel = settingsPanel.add("panel");
    modePanel.orientation = "column";
    modePanel.add("statictext", undefined, "-- 【章】模式选择 --");
    
    var rgbModeRadio = modePanel.add("radiobutton", undefined, "【RGB】的文件");
    var cmykModeRadio = modePanel.add("radiobutton", undefined, "【CMYK】的文件");
    
    // 根据当前文档设置默认模式
    if (app.documents.length > 0) {
        if (app.activeDocument.mode == DocumentMode.RGB) {
            rgbModeRadio.value = true;
        } else if (app.activeDocument.mode == DocumentMode.CMYK) {
            cmykModeRadio.value = true;
        } else {
            rgbModeRadio.value = true;
        }
    } else {
        rgbModeRadio.value = true;
    }
    
    // 模式选择事件
    rgbModeRadio.onClick = function() {
        updatePreview();
    };
    
    cmykModeRadio.onClick = function() {
        updatePreview();
    };
    
    // 保存选项
    var saveCheckbox = mainWindow.add("checkbox", undefined, "处理完后保存文件");
    saveCheckbox.helpTip = "此功能为：直接保存文件！！！注意备份你的原始文件。";
    
    // 细节处理选项事件
    detailCheckbox.onClick = function() {
        saveCheckbox.value = true;
    };
    
    // 实时预览更新函数
    function updatePreview() {
        if (!previewCheckbox.value) return;
        if (!currentFileRadio.value || app.documents.length === 0 || activeHistoryState == null) {
            return;
        }
        
        var doc = app.activeDocument;
        doc.activeHistoryState = activeHistoryState;
        
        var rgbValue = Math.round(rgbSlider.value);
        var cmykValue = Math.round(cmykSlider.value);
        var useDetail = detailCheckbox.value;
        var colorMode = rgbModeRadio.value ? "RGB" : "CMYK";
        
        var execString = '';
        if (useDetail) {
            execString = "executeFill(" + rgbValue + ", '" + colorMode + "'); executeFill(" + cmykValue + ", '" + colorMode + "')";
        } else {
            execString = "executeFill(" + rgbValue + ", '" + colorMode + "')";
        }
        
        doc.suspendHistory("朋亿工具：智能红章 QQ 156886680", execString);
    }
    
    // 滑块事件
    rgbSlider.onChanging = function() {
        rgbValue.text = Math.round(rgbSlider.value);
        updatePreview();
    };
    
    // CMYK设置组
    var cmykGroup = settingsPanel.add("group");
    cmykGroup.orientation = "row";
    
    cmykGroup.add("statictext", undefined, "追加数值（1-5）");
    var cmykSlider = cmykGroup.add("slider", undefined, 1, 1, 5);
    cmykSlider.preferredSize.width = 150;
    
    var cmykValue = cmykGroup.add("statictext", undefined, '1');
    cmykGroup.enabled = false;
    
    // 细节处理选项事件
    detailCheckbox.onClick = function() {
        cmykGroup.enabled = detailCheckbox.value;
        updatePreview();
    };
    
    // CMYK滑块事件
    cmykSlider.onChanging = function() {
        cmykValue.text = Math.round(cmykSlider.value);
        updatePreview();
    };
    
    // 按钮组
    var buttonGroup = mainWindow.add("group");
    buttonGroup.orientation = "row";
    buttonGroup.alignment = "center";
    
    var okButton = buttonGroup.add("button", undefined, "确  定");
    var cancelButton = buttonGroup.add("button", undefined, "取  消");
    
    // 确定按钮事件
    okButton.onClick = function() {
        mainWindow.close();
        
        var rgbVal = Math.round(rgbSlider.value);
        var cmykVal = Math.round(cmykSlider.value);
        var useDetailProcess = detailCheckbox.value;
        var saveAfterProcess = saveCheckbox.value;
        var colorMode = rgbModeRadio.value ? "RGB" : "CMYK";
        
        // 恢复历史状态
        if (currentFileRadio.value && app.documents.length > 0 && activeHistoryState != null) {
            app.activeDocument.activeHistoryState = activeHistoryState;
        }
        
        // 处理文件
        if (currentFileRadio.value) {
            processDocument(app.activeDocument, rgbVal, cmykVal, useDetailProcess, saveAfterProcess, "朋亿工具：智能红章 QQ 156886680", colorMode);
        } else if (allOpenFilesRadio.value) {
            var docs = app.documents;
            var activeDoc = app.activeDocument;
            
            for (var i = 0; i < docs.length; i++) {
                app.activeDocument = docs[i];
                processDocument(docs[i], rgbVal, cmykVal, useDetailProcess, saveAfterProcess, "朋亿工具：智能红章 QQ 156886680", colorMode);
            }
            
            app.activeDocument = activeDoc;
        } else if (folderFilesRadio.value) {
            var folder = Folder.selectDialog("选择要处理的文件夹：朋亿工具：智能红章 QQ 156886680");
            if (folder) {
                processFilesInFolder(folder, rgbVal, cmykVal, useDetailProcess, saveAfterProcess, "朋亿工具：智能红章 QQ 156886680", colorMode);
                folder.execute();
            }
        }
    };
    
    // 取消按钮事件
    cancelButton.onClick = function() {
        if (app.documents.length > 0 && activeHistoryState != null) {
            app.activeDocument.activeHistoryState = activeHistoryState;
        }
        mainWindow.close();
    };
    
    // 底部版权信息
    mainWindow.add("statictext", undefined, "© 朋亿工具系列 QQ 156886680  作者:朋亿");
    mainWindow.add("statictext", undefined, "                           喷印联盟QQ群 248884685");
    
    mainWindow.show();
}

// 处理单个文档
function processDocument(doc, rgbValue, cmykValue, useDetail, saveAfterProcess, historyName, colorMode) {
    if (!doc) {
        alert("没有可用的文档");
        return false;
    }
    
    app.activeDocument = doc;
    
    // 创建历史记录
    var startHistoryState = doc.activeHistoryState;
    
    try {
        // 根据颜色模式选择对应的颜色值
        var colorValue = (colorMode === "CMYK") ? cmykValue : rgbValue;
        
        // 保存当前选区
        var hasSelection = false;
        try {
            hasSelection = doc.selection.bounds;
            var originalSelection = doc.selection.store();
        } catch (e) {
            // 没有选区
        }
        
        // 应用颜色调整
        if (useDetail) {
            applyDetailedColorAdjustment(doc, colorValue, colorMode);
        } else {
            applySimpleColorAdjustment(doc, colorValue, colorMode);
        }
        
        // 恢复原始选区
        if (hasSelection) {
            doc.selection.load(originalSelection);
            originalSelection.remove();
        }
        
        // 如果需要保存文档
        if (saveAfterProcess) {
            saveDocument(doc);
        }
        
        // 设置历史记录名称
        if (historyName) {
            doc.activeHistoryState.name = historyName;
        }
        
        return true;
    } catch (e) {
        // 发生错误时恢复到初始状态
        doc.activeHistoryState = startHistoryState;
        alert("处理文档时出错: " + e);
        return false;
    }
}

// 应用简单的颜色调整
function applySimpleColorAdjustment(doc, colorValue, colorMode) {
    // 创建调整图层
    var adjustmentLayer = doc.artLayers.add();
    adjustmentLayer.name = "颜色调整";
    adjustmentLayer.kind = LayerKind.SOLIDFILL;
    
    // 设置图层颜色
    var solidColor = new SolidColor();
    if (colorMode === ColorMode.CMYK) {
        solidColor.cmyk.cyan = colorValue[0];
        solidColor.cmyk.magenta = colorValue[1];
        solidColor.cmyk.yellow = colorValue[2];
        solidColor.cmyk.black = colorValue[3];
    } else {
        solidColor.rgb.red = colorValue[0];
        solidColor.rgb.green = colorValue[1];
        solidColor.rgb.blue = colorValue[2];
    }
    
    adjustmentLayer.fillColor = solidColor;
    
    // 设置混合模式
    adjustmentLayer.blendMode = BlendMode.OVERLAY;
    adjustmentLayer.opacity = 50;
}

// 应用详细的颜色调整
function applyDetailedColorAdjustment(doc, colorValue, colorMode) {
    // 创建色相/饱和度调整图层
    var adjustmentDesc = new ActionDescriptor();
    var reference = new ActionReference();
    reference.putClass(stringIDToTypeID("adjustmentLayer"));
    adjustmentDesc.putReference(charIDToTypeID("null"), reference);
    
    var layerDesc = new ActionDescriptor();
    layerDesc.putString(charIDToTypeID("Nm  "), "高级颜色调整");
    
    var adjustSettings = new ActionDescriptor();
    adjustSettings.putEnumerated(charIDToTypeID("AdjT"), charIDToTypeID("Adjs"), stringIDToTypeID("hueSaturation"));
    
    // 设置色相、饱和度和明度值
    var hsSettings = new ActionDescriptor();
    hsSettings.putInteger(charIDToTypeID("H   "), calculateHue(colorValue, colorMode));
    hsSettings.putInteger(charIDToTypeID("Strt"), calculateSaturation(colorValue, colorMode));
    hsSettings.putInteger(charIDToTypeID("Lght"), calculateLightness(colorValue, colorMode));
    
    adjustSettings.putObject(charIDToTypeID("Adjs"), stringIDToTypeID("hueSaturation"), hsSettings);
    layerDesc.putObject(charIDToTypeID("Type"), stringIDToTypeID("adjustmentLayer"), adjustSettings);
    
    adjustmentDesc.putObject(charIDToTypeID("Usng"), stringIDToTypeID("layer"), layerDesc);
    executeAction(charIDToTypeID("Mk  "), adjustmentDesc, DialogModes.NO);
}

// 计算色相值
function calculateHue(colorValue, colorMode) {
    // 根据颜色值计算适当的色相调整
    if (colorMode === ColorMode.CMYK) {
        // CMYK转HSB的色相计算
        return Math.round((colorValue[1] - colorValue[0]) * 180 / 100);
    } else {
        // RGB转HSB的色相计算
        var r = colorValue[0] / 255;
        var g = colorValue[1] / 255;
        var b = colorValue[2] / 255;
        
        var max = Math.max(r, g, b);
        var min = Math.min(r, g, b);
        var h = 0;
        
        if (max === min) {
            h = 0;
        } else if (max === r) {
            h = 60 * ((g - b) / (max - min));
        } else if (max === g) {
            h = 60 * (2 + (b - r) / (max - min));
        } else {
            h = 60 * (4 + (r - g) / (max - min));
        }
        
        if (h < 0) h += 360;
        return Math.round(h / 2); // Photoshop的色相范围是-180到+180
    }
}

// 计算饱和度值
function calculateSaturation(colorValue, colorMode) {
    if (colorMode === ColorMode.CMYK) {
        // 简单计算CMYK的饱和度
        return Math.round((colorValue[0] + colorValue[1] + colorValue[2]) / 3);
    } else {
        // RGB转HSB的饱和度计算
        var r = colorValue[0] / 255;
        var g = colorValue[1] / 255;
        var b = colorValue[2] / 255;
        
        var max = Math.max(r, g, b);
        var min = Math.min(r, g, b);
        
        if (max === 0) return 0;
        return Math.round(((max - min) / max) * 100);
    }
}

// 计算明度值
function calculateLightness(colorValue, colorMode) {
    if (colorMode === ColorMode.CMYK) {
        // CMYK的明度与黑版有关
        return Math.round(-colorValue[3] / 2); // 黑版越高，明度越低
    } else {
        // RGB的明度计算
        return Math.round(((colorValue[0] + colorValue[1] + colorValue[2]) / 3) / 2.55) - 50;
    }
}
// 处理文件夹中的文件
function processFilesInFolder(folder, rgbValue, cmykValue, useDetail, saveAfterProcess, historyName, colorMode) {
    if (!folder || !folder.exists) {
        alert("文件夹不存在");
        return;
    }
    
    var fileList = folder.getFiles(/\.(psd|tif|tiff|jpg|jpeg|png)$/i);
    
    for (var i = 0; i < fileList.length; i++) {
        var file = fileList[i];
        
        if (file instanceof File) {
            try {
                var doc = open(file);
                processDocument(doc, rgbValue, cmykValue, useDetail, saveAfterProcess, historyName, colorMode);
                doc.close(SaveOptions.SAVECHANGES);
            } catch (e) {
                alert("处理文件 " + file.name + " 时出错: " + e);
            }
        } else if (file instanceof Folder) {
            // 递归处理子文件夹
            processFilesInFolder(file, rgbValue, cmykValue, useDetail, saveAfterProcess, historyName, colorMode);
        }
    }
}

// 执行填充操作
function executeFill(value, colorMode) {
    var doc = app.activeDocument;
    
    // 根据颜色模式创建颜色
    var fillColor = new SolidColor();
    if (colorMode === "CMYK") {
        fillColor.cmyk.cyan = 0;
        fillColor.cmyk.magenta = 100;
        fillColor.cmyk.yellow = 100;
        fillColor.cmyk.black = 0;
    } else {
        fillColor.rgb.red = 255;
        fillColor.rgb.green = 0;
        fillColor.rgb.blue = 0;
    }
    
    // 创建填充图层
    var fillLayer = doc.artLayers.add();
    fillLayer.name = "红章调整 " + value;
    fillLayer.kind = LayerKind.SOLIDFILL;
    fillLayer.fillColor = fillColor;
    
    // 设置混合模式和不透明度
    fillLayer.blendMode = BlendMode.OVERLAY;
    fillLayer.opacity = value * 10; // 根据值设置不透明度
}

// 保存文档
function saveDocument(doc) {
    // 如果文档已经有保存路径，则使用相同格式保存
    if (doc.saved) {
        doc.save();
    } else {
        // 否则另存为PSD
        var saveFile = File(doc.path + "/" + doc.name.replace(/\.[^\.]+$/, "") + ".psd");
        var saveOptions = new PhotoshopSaveOptions();
        saveOptions.embedColorProfile = true;
        saveOptions.alphaChannels = true;
        saveOptions.layers = true;
        
        doc.saveAs(saveFile, saveOptions, true, Extension.LOWERCASE);
    }
}
// 在脚本末尾添加这一行
createUI();